#!/usr/bin/env ts-node

/**
 * Manual Search Validation Script
 * 
 * This script performs manual validation of the search functionality
 * and outputs performance metrics and accuracy results.
 * 
 * Usage: npx ts-node scripts/validate-search.ts
 */

import { supabase } from "../src/integrations/supabase/client";
import { searchCandidates } from "../src/utils/searchCandidates";
import { searchJobs } from "../src/utils/searchJobs";

interface TestResult {
  testName: string;
  query: string;
  resultCount: number;
  executionTime: number;
  passed: boolean;
  details?: string;
}

const results: TestResult[] = [];

async function runTest(
  testName: string,
  query: string,
  searchFn: (query: string, filters?: any) => Promise<any[]>,
  filters?: any,
  validation?: (results: any[]) => boolean
): Promise<void> {
  console.log(`\n🧪 Running test: ${testName}`);
  console.log(`   Query: "${query}"`);
  
  const startTime = performance.now();
  try {
    const searchResults = await searchFn(query, filters);
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    
    const passed = validation ? validation(searchResults) : searchResults.length > 0;
    
    results.push({
      testName,
      query,
      resultCount: searchResults.length,
      executionTime,
      passed,
      details: passed ? "✅ Test passed" : "❌ Test failed"
    });
    
    console.log(`   Results: ${searchResults.length} found`);
    console.log(`   Time: ${executionTime.toFixed(2)}ms`);
    console.log(`   Status: ${passed ? "✅ PASSED" : "❌ FAILED"}`);
    
    // Show first few results for debugging
    if (searchResults.length > 0) {
      console.log(`   Sample results:`);
      searchResults.slice(0, 3).forEach((result, idx) => {
        if ('name' in result) {
          console.log(`     ${idx + 1}. ${result.name} - ${result.role || result.title || ''}`);
        }
      });
    }
  } catch (error) {
    console.error(`   Error: ${error}`);
    results.push({
      testName,
      query,
      resultCount: 0,
      executionTime: 0,
      passed: false,
      details: `Error: ${error}`
    });
  }
}

async function main() {
  console.log("🔍 Search Validation Script");
  console.log("===========================\n");
  
  // Authenticate with test user
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: process.env.TEST_EMAIL || "<EMAIL>",
    password: process.env.TEST_PASSWORD || "testPassword123!"
  });
  
  if (authError) {
    console.error("❌ Authentication failed:", authError);
    console.log("\nPlease set TEST_EMAIL and TEST_PASSWORD environment variables");
    return;
  }
  
  console.log("✅ Authenticated successfully\n");
  
  // Test 1: Basic candidate search
  await runTest(
    "Basic Candidate Name Search",
    "John",
    searchCandidates,
    {},
    (results) => results.some(r => r.name.toLowerCase().includes("john"))
  );
  
  // Test 2: Role-based search
  await runTest(
    "Role-based Search",
    "Software Engineer",
    searchCandidates,
    {},
    (results) => results.some(r => r.role?.toLowerCase().includes("engineer"))
  );
  
  // Test 3: Multi-term search
  await runTest(
    "Multi-term Search",
    "Senior Developer React",
    searchCandidates,
    {}
  );
  
  // Test 4: Empty query (should return all)
  await runTest(
    "Empty Query Search",
    "",
    searchCandidates,
    {}
  );
  
  // Test 5: Job search
  await runTest(
    "Job Title Search",
    "Frontend Developer",
    searchJobs,
    {},
    (results) => results.some(r => r.title?.toLowerCase().includes("frontend"))
  );
  
  // Test 6: Remote job filter
  await runTest(
    "Remote Job Filter",
    "",
    searchJobs,
    { remoteOnly: true },
    (results) => results.some(r => 
      r.location?.toLowerCase().includes("remote") || 
      r.job_type?.toLowerCase().includes("remote")
    )
  );
  
  // Test 7: Performance test with complex query
  await runTest(
    "Complex Query Performance",
    "Senior Full Stack Developer React Node JavaScript TypeScript",
    searchCandidates,
    {}
  );
  
  // Generate summary report
  console.log("\n\n📊 SEARCH VALIDATION SUMMARY");
  console.log("=============================\n");
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const avgExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0) / totalTests;
  const maxExecutionTime = Math.max(...results.map(r => r.executionTime));
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  console.log(`\nPerformance Metrics:`);
  console.log(`Average Execution Time: ${avgExecutionTime.toFixed(2)}ms`);
  console.log(`Max Execution Time: ${maxExecutionTime.toFixed(2)}ms`);
  
  console.log("\n\nDetailed Results:");
  console.log("=================\n");
  
  results.forEach((result) => {
    console.log(`${result.passed ? "✅" : "❌"} ${result.testName}`);
    console.log(`   Query: "${result.query}"`);
    console.log(`   Results: ${result.resultCount}`);
    console.log(`   Time: ${result.executionTime.toFixed(2)}ms`);
    if (result.details) {
      console.log(`   Details: ${result.details}`);
    }
    console.log("");
  });
  
  // Check search vector indexes
  console.log("\n🗄️  Database Search Vector Status");
  console.log("==================================\n");
  
  const { data: candidateVectors } = await supabase
    .from("candidates")
    .select("id, search_vector")
    .limit(5);
    
  const { data: jobVectors } = await supabase
    .from("jobs")
    .select("id, search_vector")
    .limit(5);
  
  const candidatesWithVectors = candidateVectors?.filter(c => c.search_vector)?.length || 0;
  const jobsWithVectors = jobVectors?.filter(j => j.search_vector)?.length || 0;
  
  console.log(`Candidates with search vectors: ${candidatesWithVectors}/${candidateVectors?.length || 0}`);
  console.log(`Jobs with search vectors: ${jobsWithVectors}/${jobVectors?.length || 0}`);
  
  if (candidatesWithVectors === 0 || jobsWithVectors === 0) {
    console.log("\n⚠️  Warning: Some records are missing search vectors!");
    console.log("   Run the following SQL to regenerate vectors:");
    console.log("   UPDATE candidates SET search_vector = NULL WHERE search_vector IS NULL;");
    console.log("   UPDATE jobs SET search_vector = NULL WHERE search_vector IS NULL;");
  }
  
  // Sign out
  await supabase.auth.signOut();
  
  console.log("\n✅ Validation complete!");
}

// Run the script
main().catch(console.error);
