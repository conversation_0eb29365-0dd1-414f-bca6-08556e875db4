import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface EmailRequest {
  to: string[];
  subject: string;
  html: string;
  from?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
      {
        global: {
          headers: { Authorization: req.headers.get("Authorization")! },
        },
      },
    );

    // Verify the user is authenticated
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();

    if (!user) {
      throw new Error("Not authenticated");
    }

    const {
      to,
      subject,
      html,
      from = "<EMAIL>",
    }: EmailRequest = await req.json();

    if (!to || !Array.isArray(to) || to.length === 0) {
      throw new Error("Recipients are required");
    }

    if (!subject || !html) {
      throw new Error("Subject and HTML content are required");
    }

    // Here you would integrate with your email service provider
    // For example: SendGrid, AWS SES, Postmark, etc.

    // Example with a generic email API (replace with your actual provider):
    const emailServiceUrl = Deno.env.get("EMAIL_SERVICE_URL");
    const emailServiceApiKey = Deno.env.get("EMAIL_SERVICE_API_KEY");

    if (!emailServiceUrl || !emailServiceApiKey) {
      console.error("Email service not configured");
      throw new Error("Email service not configured");
    }

    const emailResponse = await fetch(emailServiceUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${emailServiceApiKey}`,
      },
      body: JSON.stringify({
        from,
        to,
        subject,
        html,
        // Add any additional fields required by your email provider
      }),
    });

    if (!emailResponse.ok) {
      const errorText = await emailResponse.text();
      console.error("Email service error:", errorText);
      throw new Error(`Failed to send email: ${emailResponse.status}`);
    }

    const result = await emailResponse.json();

    return new Response(
      JSON.stringify({
        success: true,
        message: "Email sent successfully",
        messageId: result.messageId || result.id,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error in send-email function:", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: error.message === "Not authenticated" ? 401 : 400,
      },
    );
  }
});

// Example environment variables to set:
// EMAIL_SERVICE_URL=https://api.sendgrid.com/v3/mail/send (for SendGrid)
// EMAIL_SERVICE_API_KEY=your-api-key

// For SendGrid specifically, the body format would be:
// {
//   "personalizations": [{ "to": to.map(email => ({ email })) }],
//   "from": { "email": from },
//   "subject": subject,
//   "content": [{ "type": "text/html", "value": html }]
// }

// For AWS SES, you would use the AWS SDK:
// import { SESClient, SendEmailCommand } from "https://deno.land/x/aws_sdk/client-ses/mod.ts"
