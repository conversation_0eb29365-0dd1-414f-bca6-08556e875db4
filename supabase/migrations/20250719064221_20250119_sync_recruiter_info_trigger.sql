-- Migration: Create trigger to sync recruiter info from profiles table using user_id
-- This trigger automatically updates recruiter_name and recruiter_avatar columns
-- in the candidates table whenever user_id is inserted or updated
-- Note: recruiter_id column is redundant since user_id already links to auth.users/profiles

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION public.sync_recruiter_info_from_user_id()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if user_id has changed or is being set
  IF (TG_OP = 'INSERT' AND NEW.user_id IS NOT NULL) OR 
     (TG_OP = 'UPDATE' AND NEW.user_id IS DISTINCT FROM OLD.user_id) THEN
    
    -- If user_id is set to NULL, clear the denormalized fields
    IF NEW.user_id IS NULL THEN
      NEW.recruiter_name := NULL;
      NEW.recruiter_avatar := NULL;
    ELSE
      -- Update the denormalized fields from the profiles table
      SELECT 
        TRIM(CONCAT(COALESCE(p.first_name, ''), ' ', COALESCE(p.last_name, ''))),
        p.avatar_url
      INTO 
        NEW.recruiter_name,
        NEW.recruiter_avatar
      FROM public.profiles p
      WHERE p.id = NEW.user_id;
      
      -- If no profile was found, set the fields to NULL
      IF NOT FOUND THEN
        NEW.recruiter_name := NULL;
        NEW.recruiter_avatar := NULL;
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists (to ensure clean state)
DROP TRIGGER IF EXISTS sync_recruiter_info_trigger ON public.candidates;

-- Create the trigger
CREATE TRIGGER sync_recruiter_info_trigger
  BEFORE INSERT OR UPDATE OF user_id
  ON public.candidates
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_recruiter_info_from_user_id();

-- Update existing records to sync the current data
-- This ensures all existing records have the correct recruiter info
UPDATE public.candidates c
SET 
  recruiter_name = TRIM(CONCAT(COALESCE(p.first_name, ''), ' ', COALESCE(p.last_name, ''))),
  recruiter_avatar = p.avatar_url
FROM public.profiles p
WHERE c.user_id IS NOT NULL 
  AND p.id = c.user_id
  AND (
    c.recruiter_name IS DISTINCT FROM TRIM(CONCAT(COALESCE(p.first_name, ''), ' ', COALESCE(p.last_name, '')))
    OR c.recruiter_avatar IS DISTINCT FROM p.avatar_url
  );

-- Add comments to document the trigger
COMMENT ON TRIGGER sync_recruiter_info_trigger ON public.candidates IS 
'Automatically syncs recruiter_name and recruiter_avatar from profiles table when user_id changes';

COMMENT ON FUNCTION public.sync_recruiter_info_from_user_id() IS 
'Trigger function that updates recruiter_name and recruiter_avatar based on user_id lookup in profiles table';

-- Note: In a future migration, consider removing the redundant recruiter_id column
-- since user_id already serves the same purpose
