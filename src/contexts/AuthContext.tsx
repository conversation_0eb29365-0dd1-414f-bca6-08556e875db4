import React, { createContext, useContext, useEffect, useState } from "react";
import { User, Session } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (
    email: string,
    password: string,
    companyName?: string,
  ) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function provisionCompanyIfNeeded(u: User | null) {
      try {
        const companyName = (u as any)?.user_metadata?.company_name as
          | string
          | undefined;
        if (u?.id && companyName && companyName.trim().length > 0) {
          await supabase.rpc("provision_company_and_profile", {
            p_user_id: u.id,
            p_company_name: companyName.trim(),
          });
        }
      } catch (err) {
        console.error("Error provisioning company/profile:", err);
      }
    }
    // Set up auth state listener first
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log("Auth state changed:", event, session?.user?.id);
      setSession(session);
      setUser(session?.user ?? null);
      // Do not flip loading here; wait for initial getSession below to avoid UI flash
      // Attempt provisioning when a valid session appears
      (async () => {
        const u = session?.user ?? null;
        if (u?.id) {
          await supabase.rpc("ensure_profile", { p_user_id: u.id });
        }
        await provisionCompanyIfNeeded(u);
      })();
    });

    // Then check for existing session
    supabase.auth.getSession().then(async ({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      // Now that initial session is set, flip loading off once
      setLoading(false);
      // Attempt provisioning for existing session
      const u = session?.user ?? null;
      if (u?.id) {
        await supabase.rpc("ensure_profile", { p_user_id: u.id });
      }
      await provisionCompanyIfNeeded(u);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signUp = async (
    email: string,
    password: string,
    companyName?: string,
  ) => {
    const redirectUrl = `${window.location.origin}/`;

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: companyName ? { company_name: companyName } : undefined,
      },
    });
    return { error };
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("Error signing out:", error);
    }
  };

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
