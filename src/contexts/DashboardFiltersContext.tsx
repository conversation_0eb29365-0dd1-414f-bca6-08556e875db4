import React, { createContext, useContext, useState, useEffect } from "react";

export interface DashboardComponentVisibility {
  dashboardStats: boolean;
  recentCandidates: boolean;
  recentJobs: boolean;
  hiringPipeline: boolean;
  quickActions: boolean;
  upcomingEvents: boolean;
  taskWidget: boolean;
}

interface DashboardFiltersContextType {
  componentVisibility: DashboardComponentVisibility;
  setComponentVisibility: (
    visibility: Partial<DashboardComponentVisibility>,
  ) => void;
  toggleComponent: (component: keyof DashboardComponentVisibility) => void;
  resetToDefaults: () => void;
  isFilterSidebarOpen: boolean;
  setIsFilterSidebarOpen: (open: boolean) => void;
}

const defaultVisibility: DashboardComponentVisibility = {
  dashboardStats: true,
  recentCandidates: true,
  recentJobs: true,
  hiringPipeline: true,
  quickActions: true,
  upcomingEvents: true,
  taskWidget: true,
};

const STORAGE_KEY = "dashboard-component-visibility";

const DashboardFiltersContext = createContext<
  DashboardFiltersContextType | undefined
>(undefined);

export const DashboardFiltersProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [componentVisibility, setComponentVisibilityState] =
    useState<DashboardComponentVisibility>(defaultVisibility);
  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);

  // Load saved preferences from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsedVisibility = JSON.parse(saved);
        // Merge with defaults to handle any new components that might have been added
        setComponentVisibilityState({
          ...defaultVisibility,
          ...parsedVisibility,
        });
      }
    } catch (error) {
      console.warn("Failed to load dashboard filter preferences:", error);
    }
  }, []);

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(componentVisibility));
    } catch (error) {
      console.warn("Failed to save dashboard filter preferences:", error);
    }
  }, [componentVisibility]);

  const setComponentVisibility = (
    visibility: Partial<DashboardComponentVisibility>,
  ) => {
    setComponentVisibilityState((prev) => ({ ...prev, ...visibility }));
  };

  const toggleComponent = (component: keyof DashboardComponentVisibility) => {
    setComponentVisibilityState((prev) => ({
      ...prev,
      [component]: !prev[component],
    }));
  };

  const resetToDefaults = () => {
    setComponentVisibilityState(defaultVisibility);
  };

  const value: DashboardFiltersContextType = {
    componentVisibility,
    setComponentVisibility,
    toggleComponent,
    resetToDefaults,
    isFilterSidebarOpen,
    setIsFilterSidebarOpen,
  };

  return (
    <DashboardFiltersContext.Provider value={value}>
      {children}
    </DashboardFiltersContext.Provider>
  );
};

export const useDashboardFilters = (): DashboardFiltersContextType => {
  const context = useContext(DashboardFiltersContext);
  if (context === undefined) {
    throw new Error(
      "useDashboardFilters must be used within a DashboardFiltersProvider",
    );
  }
  return context;
};
