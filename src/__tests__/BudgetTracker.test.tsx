import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BudgetTracker } from '@/components/analytics/BudgetTracker';
import React from 'react';

// Mock the hooks
vi.mock('@/hooks/useBudget', () => ({
  useBudgetSummary: vi.fn(),
}));

vi.mock('@/hooks/useBudgetAllocations', () => ({
  useEnhancedBudgetSummary: vi.fn(),
  useBudgetUtilization: vi.fn(),
}));

vi.mock('@/hooks/useJobExpenses', () => ({
  useJobExpenseSummary: vi.fn(),
  useCreateJobExpense: vi.fn(() => ({
    mutateAsync: vi.fn(),
    isPending: false,
  })),
}));

vi.mock('@/hooks/useJobs', () => ({
  useJobs: vi.fn(() => ({ data: [], isLoading: false })),
}));

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    user: { id: '66e968bb-aa2e-4b57-aabb-6a1bb7f90466', email: '<EMAIL>' },
    session: null,
    loading: false,
  })),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('BudgetTracker', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state correctly', async () => {
    const { useBudgetSummary } = await import('@/hooks/useBudget');
    const { useEnhancedBudgetSummary, useBudgetUtilization } = await import('@/hooks/useBudgetAllocations');
    const { useJobExpenseSummary } = await import('@/hooks/useJobExpenses');

    vi.mocked(useBudgetSummary).mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    vi.mocked(useEnhancedBudgetSummary).mockReturnValue({
      data: [],
      isLoading: true,
      error: null,
    });

    vi.mocked(useBudgetUtilization).mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    vi.mocked(useJobExpenseSummary).mockReturnValue({
      data: [],
      isLoading: true,
      error: null,
    });

    render(
      <TestWrapper>
        <BudgetTracker />
      </TestWrapper>
    );

    expect(screen.getByText('Budget Tracker')).toBeInTheDocument();
    // Should show loading skeletons
    expect(document.querySelectorAll('[data-testid="skeleton"]').length).toBeGreaterThan(0);
  });

  it('renders enhanced budget data correctly', async () => {
    const { useBudgetSummary } = await import('@/hooks/useBudget');
    const { useEnhancedBudgetSummary, useBudgetUtilization } = await import('@/hooks/useBudgetAllocations');
    const { useJobExpenseSummary } = await import('@/hooks/useJobExpenses');

    const mockEnhancedSummary = [
      {
        allocation_id: '1',
        budget_name: '2025 Annual Recruitment Budget',
        total_budget: 500000,
        spent_amount: 15948,
        remaining_amount: 484052,
        percentage_used: 3.19,
        expense_count: 4,
        department: undefined,
        job_id: undefined,
        budget_period_start: '2025-01-01',
        budget_period_end: '2025-12-31',
      },
    ];

    const mockUtilization = {
      total_allocated: 500000,
      total_spent: 15948,
      utilization_percentage: 3.19,
      active_allocations: 1,
    };

    vi.mocked(useBudgetSummary).mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    });

    vi.mocked(useEnhancedBudgetSummary).mockReturnValue({
      data: mockEnhancedSummary,
      isLoading: false,
      error: null,
    });

    vi.mocked(useBudgetUtilization).mockReturnValue({
      data: mockUtilization,
      isLoading: false,
      error: null,
    });

    vi.mocked(useJobExpenseSummary).mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });

    render(
      <TestWrapper>
        <BudgetTracker />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Budget Tracker')).toBeInTheDocument();
      expect(screen.getByText('Real-time')).toBeInTheDocument();
      expect(screen.getByText('3.2%')).toBeInTheDocument();
      expect(screen.getByText('$15,948')).toBeInTheDocument();
      expect(screen.getByText('$484,052 remaining')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument(); // Active budgets
      expect(screen.getByText('Add Expense')).toBeInTheDocument();
    });
  });

  it('falls back to legacy budget data when enhanced data is not available', async () => {
    const { useBudgetSummary } = await import('@/hooks/useBudget');
    const { useEnhancedBudgetSummary, useBudgetUtilization } = await import('@/hooks/useBudgetAllocations');
    const { useJobExpenseSummary } = await import('@/hooks/useJobExpenses');

    const mockLegacyBudget = {
      total_budget: 500000,
      spent_amount: 320000,
      categories: [
        {
          id: '1',
          user_id: '66e968bb-aa2e-4b57-aabb-6a1bb7f90466',
          total_budget: 500000,
          spent_amount: 320000,
          category: 'Job Postings',
          amount: 120000,
          percentage: 37.5,
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
        },
        {
          id: '2',
          user_id: '66e968bb-aa2e-4b57-aabb-6a1bb7f90466',
          total_budget: 500000,
          spent_amount: 320000,
          category: 'Agency Fees',
          amount: 100000,
          percentage: 31.25,
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
        },
      ],
    };

    vi.mocked(useBudgetSummary).mockReturnValue({
      data: mockLegacyBudget,
      isLoading: false,
      error: null,
    });

    vi.mocked(useEnhancedBudgetSummary).mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });

    vi.mocked(useBudgetUtilization).mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    });

    vi.mocked(useJobExpenseSummary).mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });

    render(
      <TestWrapper>
        <BudgetTracker />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Budget Tracker')).toBeInTheDocument();
      expect(screen.getByText('64.0%')).toBeInTheDocument(); // (320000/500000)*100
      expect(screen.getByText('$320,000')).toBeInTheDocument();
      expect(screen.getByText('$180,000 remaining')).toBeInTheDocument();
      expect(screen.getByText('Job Postings')).toBeInTheDocument();
      expect(screen.getByText('Agency Fees')).toBeInTheDocument();
      expect(screen.getByText('Using static budget data')).toBeInTheDocument();
    });
  });
});
