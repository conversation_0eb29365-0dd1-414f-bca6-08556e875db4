import { vi } from "vitest";
import { NodeExecutor<PERSON><PERSON>ult, ExecutionContext } from "@/engine/types";

// Mock service responses
export const mockCandidateData = {
  id: "test-candidate-123",
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+1234567890",
  skills: ["JavaScript", "React", "Node.js"],
  experience_years: 5,
  education: "Bachelor of Computer Science",
  location: "San Francisco, CA",
  status: "active",
  created_at: new Date().toISOString(),
};

export const mockJobData = {
  id: "test-job-456",
  title: "Senior Frontend Developer",
  description: "Looking for experienced React developer",
  requirements: ["5+ years experience", "React expertise", "Team player"],
  department: "Engineering",
  location: "Remote",
  status: "open",
  created_at: new Date().toISOString(),
};

export const mockMessageTemplate = {
  id: "template-789",
  name: "interview",
  content: "Dear {{candidateName}}, We would like to schedule an interview...",
  subject: "Interview Invitation",
  created_at: new Date().toISOString(),
};

// Mock Services
export const createMockServices = () => ({
  CandidatesService: {
    getCandidate: vi.fn().mockResolvedValue(mockCandidateData),
    getCandidates: vi.fn().mockResolvedValue([mockCandidateData]),
    createCandidate: vi.fn().mockResolvedValue({ id: "new-candidate-id" }),
    updateCandidate: vi.fn().mockResolvedValue(mockCandidateData),
  },
  MessagingService: {
    getMessageTemplate: vi.fn().mockResolvedValue(mockMessageTemplate),
    sendMessage: vi.fn().mockResolvedValue({ id: "message-id", sent: true }),
    createMessage: vi.fn().mockResolvedValue({ id: "message-id" }),
  },
  ActivityService: {
    createActivityEntry: vi.fn().mockResolvedValue({ id: "activity-entry-id" }),
    getCandidateActivities: vi.fn().mockResolvedValue([]),
  },
  EventsService: {
    createEvent: vi.fn().mockResolvedValue({ id: "event-id" }),
    getEvents: vi.fn().mockResolvedValue([]),
  },
  WorkflowLogger: {
    log: vi.fn().mockResolvedValue(undefined),
  },
  WorkflowAlertService: {
    checkAndSendAlerts: vi.fn().mockResolvedValue(undefined),
  },
});

// Mock Email Integration
export const createMockEmailIntegration = () => ({
  sendEmail: vi
    .fn()
    .mockResolvedValue({ success: true, messageId: "email-123" }),
});

// Mock Execution Context
export const createMockExecutionContext = (
  overrides?: Partial<ExecutionContext>,
): ExecutionContext => ({
  candidateId: "test-candidate-123",
  jobId: "test-job-456",
  userId: "test-user-789",
  user: { id: "test-user-789", email: "<EMAIL>" },
  lastResult: null,
  lastNodeId: null,
  lastNodeType: null,
  ...overrides,
});

// Mock Node Data
export const createMockNodeData = (type: string, config: any = {}) => ({
  id: `node-${Math.random().toString(36).substring(7)}`,
  type,
  label: `Test ${type} Node`,
  position: { x: 0, y: 0 },
  data: {
    type,
    label: `Test ${type} Node`,
    config,
  },
  config,
});

// Mock Executor Result
export const createMockExecutorResult = (
  success: boolean,
  data?: any,
  error?: Error,
): NodeExecutorResult => ({
  success,
  data,
  error,
  duration: 100,
  retryCount: 0,
});

// Workflow Test Data
export const createMockWorkflow = () => ({
  id: "test-workflow-123",
  name: "Test Workflow",
  nodes: [
    {
      id: "trigger-1",
      type: "trigger",
      position: { x: 100, y: 100 },
      data: {
        type: "trigger",
        category: "triggers",
        label: "New Application",
        config: {
          triggerType: "new-application",
        },
      },
    },
    {
      id: "action-1",
      type: "action",
      position: { x: 300, y: 100 },
      data: {
        type: "action",
        label: "Send Email",
        config: {
          template: "welcome",
        },
      },
    },
  ],
  edges: [
    {
      id: "edge-1",
      source: "trigger-1",
      target: "action-1",
      type: "default",
    },
  ],
});

// Mock Supabase Responses
export const createMockSupabaseResponse = (data: any, error: any = null) => ({
  data,
  error,
});

// Test Helpers
export const waitForAsync = (ms: number = 0) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export const createTestScheduler = () => {
  let scheduledCallbacks: Array<{
    callback: () => void | Promise<void>;
    delay: number;
    id: number;
  }> = [];
  let currentId = 0;

  return {
    schedule: (callback: () => void | Promise<void>, delay: number) => {
      const id = currentId++;
      scheduledCallbacks.push({ callback, delay, id });
      return id;
    },
    cancel: (id: number) => {
      scheduledCallbacks = scheduledCallbacks.filter((item) => item.id !== id);
    },
    runAll: async () => {
      const callbacks = [...scheduledCallbacks];
      scheduledCallbacks = [];
      for (const { callback } of callbacks) {
        await callback();
      }
    },
    clear: () => {
      scheduledCallbacks = [];
    },
  };
};
