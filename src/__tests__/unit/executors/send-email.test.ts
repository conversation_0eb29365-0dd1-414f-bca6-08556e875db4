import { describe, it, expect, vi, beforeEach } from "vitest";
import { SendEmailExecutor } from "@/engine/executors/send-email";
import {
  createMockServices,
  createMockExecutionContext,
  createMockNodeData,
  createMockEmailIntegration,
  mockCandidateData,
  mockMessageTemplate,
} from "../../utils/mockServices";

// Mock the services
vi.mock("@/services", () => ({
  CandidatesService: createMockServices().CandidatesService,
  MessagingService: createMockServices().MessagingService,
  ActivityService: createMockServices().ActivityService,
}));

// Mock the email integration
vi.mock("@/integrations/email", () => ({
  default: createMockEmailIntegration(),
}));

describe("SendEmailExecutor", () => {
  let executor: SendEmailExecutor;
  let mockServices: ReturnType<typeof createMockServices>;
  let mockEmailIntegration: ReturnType<typeof createMockEmailIntegration>;

  beforeEach(async () => {
    vi.clearAllMocks();
    executor = new SendEmailExecutor();
    mockServices = createMockServices();
    mockEmailIntegration = createMockEmailIntegration();

    // Re-mock with fresh instances
    const services = await import("@/services");
    const emailIntegration = await import("@/integrations/email");

    vi.mocked(services).CandidatesService =
      mockServices.CandidatesService as any;
    vi.mocked(services).MessagingService = mockServices.MessagingService as any;
    vi.mocked(services).ActivityService = mockServices.ActivityService as any;
    vi.mocked(emailIntegration).default = mockEmailIntegration as any;
  });

  describe("Basic Properties", () => {
    it("should have correct metadata", () => {
      expect(executor.id).toBe("send-email");
      expect(executor.name).toBe("Send Email");
      expect(executor.description).toBe("Send an email to a candidate");
      expect(executor.category).toBe("action");
    });

    it("should have correct config schema", () => {
      expect(executor.configSchema).toBeDefined();
      expect(executor.configSchema?.template).toBeDefined();
      expect(executor.configSchema?.customMessage).toBeDefined();
    });
  });

  describe("Execute Method", () => {
    it("should successfully send email with template", async () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        sent: true,
        candidateId: (mockCandidateData as any).id,
        candidateName: (mockCandidateData as any).name,
        candidateEmail: (mockCandidateData as any).email,
        template: "welcome",
      });

      // Verify service calls
      expect(mockServices.CandidatesService.getCandidate).toHaveBeenCalledWith(
        context.candidateId,
      );
      expect(
        mockServices.MessagingService.getMessageTemplate,
      ).toHaveBeenCalledWith("welcome");
      expect(mockEmailIntegration.sendEmail).toHaveBeenCalledWith(
        (context as any).user.id,
        expect.objectContaining({
          to: (mockCandidateData as any).email,
          subject: expect.any(String),
          body: (mockMessageTemplate as any).content,
        }),
      );
    });

    it("should send email with custom message", async () => {
      const customMessage = "This is a custom email message";
      const nodeData = createMockNodeData("send-email", {
        template: "custom",
        customMessage,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(mockEmailIntegration.sendEmail).toHaveBeenCalledWith(
        (context as any).user.id,
        expect.objectContaining({
          body: customMessage,
        }),
      );
    });

    it("should use candidateId from lastResult if not in context", async () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: { candidateId: "last-result-candidate-id" },
      });

      mockServices.CandidatesService.getCandidate.mockResolvedValueOnce({
        ...mockCandidateData,
        id: "last-result-candidate-id",
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(mockServices.CandidatesService.getCandidate).toHaveBeenCalledWith(
        "last-result-candidate-id",
      );
    });

    it("should fail if no candidate ID is available", async () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: {},
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain(
        "No candidate ID found in context",
      );
    });

    it("should fail if candidate is not found", async () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const context = createMockExecutionContext();

      mockServices.CandidatesService.getCandidate.mockResolvedValueOnce(null);

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Candidate not found");
    });

    it("should fail if email sending fails", async () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const context = createMockExecutionContext();

      mockEmailIntegration.sendEmail.mockResolvedValueOnce({
        success: false,
        error: "Email service unavailable",
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Email service unavailable");
    });

    it("should handle timeout correctly", async () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const context = createMockExecutionContext();

      // Make the email service hang
      mockEmailIntegration.sendEmail.mockImplementationOnce(
        () => new Promise(() => {}), // Never resolves
      );

      const result = await executor.execute(nodeData, context, {
        timeout: 100, // 100ms timeout
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("timeout");
    });

    it("should retry on failure", async () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const context = createMockExecutionContext();

      // Fail first time, succeed second time
      mockEmailIntegration.sendEmail
        .mockResolvedValueOnce({ success: false, error: "Temporary failure" })
        .mockResolvedValueOnce({ success: true, messageId: "email-123" });

      const result = await executor.execute(nodeData, context, {
        retries: 1,
        retryDelay: 10,
      });

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
      expect(mockEmailIntegration.sendEmail).toHaveBeenCalledTimes(2);
    });
  });

  describe("canExecute Method", () => {
    it("should return true when candidateId is in context", () => {
      const context = createMockExecutionContext();
      expect(executor.canExecute(context)).toBe(true);
    });

    it("should return true when candidateId is in lastResult", () => {
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: { candidateId: "some-id" },
      });
      expect(executor.canExecute(context)).toBe(true);
    });

    it("should return false when no candidateId is available", () => {
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: {},
      });
      expect(executor.canExecute(context)).toBe(false);
    });
  });

  describe("validate Method", () => {
    it("should validate correct node data", () => {
      const nodeData = createMockNodeData("send-email", {
        template: "welcome",
      });
      const validation = executor.validate(nodeData);

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it("should return errors for missing node data", () => {
      const validation = executor.validate(null);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain("Node data is required");
    });

    it("should return errors for missing node ID", () => {
      const nodeData = { label: "Test Node" };
      const validation = executor.validate(nodeData);

      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain("Node ID is required");
    });
  });
});
