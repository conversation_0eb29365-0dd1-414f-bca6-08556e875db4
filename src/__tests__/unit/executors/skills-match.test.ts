import { searchCandidatesOptimized } from "@/utils/optimizedSearch";

// Mock data
const candidates = [
  {
    id: 1,
    name: "<PERSON>",
    skills: [{ name: "<PERSON><PERSON>" }, { name: "TypeScript" }],
    tags: ["developer"],
  },
  {
    id: 2,
    name: "<PERSON>",
    skills: [{ name: "JavaScript" }],
    tags: ["developer", "frontend"],
  },
  {
    id: 3,
    name: "<PERSON>",
    skills: [{ name: "TypeScript" }],
    tags: ["backend"],
  },
];

describe("searchCandidatesOptimized", () => {
  it("should find candidates with TypeScript skill", () => {
    const results = searchCandidatesOptimized(candidates, "TypeScript");
    expect(results).toHaveLength(2);
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ name: "<PERSON>" }),
        expect.objectContaining({ name: "<PERSON>" }),
      ]),
    );
  });

  it("should find candidates with React skill", () => {
    const results = searchCandidatesOptimized(candidates, "React");
    expect(results).toHaveLength(1);
    expect(results).toEqual(
      expect.arrayContaining([expect.objectContaining({ name: "<PERSON>" })]),
    );
  });

  it("should return empty if no match is found", () => {
    const results = searchCandidatesOptimized(candidates, "Python");
    expect(results).toHaveLength(0);
  });

  it("should find candidates using partial skill name", () => {
    const results = searchCandidatesOptimized(candidates, "Script");
    expect(results).toHaveLength(3);
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ name: "Alice" }),
        expect.objectContaining({ name: "Bob" }),
        expect.objectContaining({ name: "Charlie" }),
      ]),
    );
  });
});

import { describe, it, expect, vi, beforeEach } from "vitest";
import { SkillsMatchExecutor } from "@/engine/executors/skills-match";
import {
  createMockServices,
  createMockExecutionContext,
  createMockNodeData,
  mockCandidateData,
} from "../../utils/mockServices";

// Mock the services
vi.mock("@/services", () => ({
  CandidatesService: createMockServices().CandidatesService,
}));

describe("SkillsMatchExecutor", () => {
  let executor: SkillsMatchExecutor;
  let mockServices: ReturnType<typeof createMockServices>;

  beforeEach(async () => {
    vi.clearAllMocks();
    executor = new SkillsMatchExecutor();
    mockServices = createMockServices();

    // Re-mock with fresh instances
    const services = await import("@/services");
    vi.mocked(services).CandidatesService =
      mockServices.CandidatesService as any;
  });

  describe("Basic Properties", () => {
    it("should have correct metadata", () => {
      expect(executor.id).toBe("skills-match");
      expect(executor.name).toBe("Skills Match");
      expect(executor.description).toBe(
        "Check if candidate has required skills",
      );
      expect(executor.category).toBe("condition");
    });

    it("should have correct config schema", () => {
      expect(executor.configSchema).toBeDefined();
      expect(executor.configSchema?.requiredSkills).toBeDefined();
      expect(executor.configSchema?.minMatchPercentage).toBeDefined();
      expect(executor.configSchema?.minMatchPercentage.default).toBe(70);
    });
  });

  describe("Execute Method - With Candidate Skills", () => {
    it("should match when candidate has all required skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React, Node.js",
        minMatchPercentage: 100,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        matchPercentage: 100,
        matchedSkills: 3,
        totalRequired: 3,
        threshold: 100,
      });
    });

    it("should match when candidate meets minimum percentage", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React, Python",
        minMatchPercentage: 60,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        matchPercentage: 66.66666666666666, // 2/3 skills
        matchedSkills: 2,
        totalRequired: 3,
        threshold: 60,
      });
    });

    it("should not match when candidate does not meet minimum percentage", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "Python, Ruby, Go, Rust",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        matchPercentage: 0,
        matchedSkills: 0,
        totalRequired: 4,
        threshold: 50,
      });
    });

    it("should handle case-insensitive matching", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "javascript, REACT, node.JS",
        minMatchPercentage: 100,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).result).toBe(true);
      expect((result.data as any).matchedSkills).toBe(3);
    });

    it("should handle skills as objects with name property", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      mockServices.CandidatesService.getCandidate.mockResolvedValueOnce({
        ...mockCandidateData,
        skills: [
          { name: "JavaScript", level: "expert" },
          { name: "React", level: "intermediate" },
        ],
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).result).toBe(true);
      expect((result.data as any).matchedSkills).toBe(2);
    });

    it("should handle empty required skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "",
        minMatchPercentage: 70,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).matchPercentage).toBe(0);
      expect((result.data as any).totalRequired).toBe(0);
    });
  });

  describe("Execute Method - With AI Screening Result", () => {
    it("should use AI screening score from lastResult", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 75,
      });
      const context = createMockExecutionContext({
        lastResult: { score: 80 },
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: true,
        score: 80,
        threshold: 75,
      });

      // Should not call CandidatesService when using AI score
      expect(
        mockServices.CandidatesService.getCandidate,
      ).not.toHaveBeenCalled();
    });

    it("should fail AI screening when score is below threshold", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 80,
      });
      const context = createMockExecutionContext({
        lastResult: { score: 70 },
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        score: 70,
        threshold: 80,
      });
    });
  });

  describe("Execute Method - Edge Cases", () => {
    it("should use candidateId from lastResult if not in context", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: { candidateId: "last-result-candidate-id" },
      });

      mockServices.CandidatesService.getCandidate.mockResolvedValueOnce({
        ...mockCandidateData,
        id: "last-result-candidate-id",
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(mockServices.CandidatesService.getCandidate).toHaveBeenCalledWith(
        "last-result-candidate-id",
      );
    });

    it("should return false when no candidate data is available", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext({
        candidateId: undefined,
        lastResult: null,
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        reason: "No candidate data available",
      });
    });

    it("should fail when candidate is not found", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      mockServices.CandidatesService.getCandidate.mockResolvedValueOnce(null);

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Candidate not found");
    });

    it("should handle candidate with no skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript, React",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      mockServices.CandidatesService.getCandidate.mockResolvedValueOnce({
        ...mockCandidateData,
        skills: null,
      });

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        result: false,
        matchPercentage: 0,
        matchedSkills: 0,
      });
    });

    it("should handle whitespace in required skills", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "  JavaScript  ,   React   ,  Node.js  ",
        minMatchPercentage: 100,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).matchedSkills).toBe(3);
    });

    it("should handle partial skill name matches", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "Java, React",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      const result = await executor.execute(nodeData, context);

      expect(result.success).toBe(true);
      expect((result.data as any).result).toBe(true);
      expect((result.data as any).matchedSkills).toBe(2); // JavaScript contains 'Java'
    });
  });

  describe("Timeout and Retry Behavior", () => {
    it("should handle timeout correctly", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      // Make the service hang
      mockServices.CandidatesService.getCandidate.mockImplementationOnce(
        () => new Promise(() => {}), // Never resolves
      );

      const result = await executor.execute(nodeData, context, {
        timeout: 100, // 100ms timeout
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("timeout");
    });

    it("should retry on failure", async () => {
      const nodeData = createMockNodeData("skills-match", {
        requiredSkills: "JavaScript",
        minMatchPercentage: 50,
      });
      const context = createMockExecutionContext();

      // Fail first time, succeed second time
      mockServices.CandidatesService.getCandidate
        .mockRejectedValueOnce(new Error("Temporary failure"))
        .mockResolvedValueOnce(mockCandidateData);

      const result = await executor.execute(nodeData, context, {
        retries: 1,
        retryDelay: 10,
      });

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
      expect(mockServices.CandidatesService.getCandidate).toHaveBeenCalledTimes(
        2,
      );
    });
  });
});
