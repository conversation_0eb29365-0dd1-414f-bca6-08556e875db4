import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { CandidateList } from "@/components/candidate/CandidateList";
import { useCandidates } from "@/hooks/useCandidates";
import { useAuth } from "@/contexts/AuthContext";

// Mock the hooks
vi.mock("@/hooks/useCandidates");
vi.mock("@/contexts/AuthContext");

describe("Normalized Tags", () => {
  const mockUser = { id: "test-user-id" };

  beforeEach(() => {
    vi.mocked(useAuth).mockReturnValue({
      user: mockUser,
      loading: false,
      signIn: vi.fn(),
      signOut: vi.fn(),
      signUp: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("CandidateList Tag Display", () => {
    it("should display normalized tags with colors", () => {
      const mockCandidates = [
        {
          id: "1",
          name: "<PERSON>",
          role: "Software Engineer",
          email: "<EMAIL>",
          phone: "************",
          location: "New York",
          avatar: "/placeholder.svg",
          recruiter: { id: "1", name: "Recruiter", avatar: "/placeholder.svg" },
          tags: ["frontend", "react"],
          normalized_tags: [
            { id: "1", name: "frontend", color: "#3B82F6" },
            { id: "2", name: "react", color: "#10B981" },
          ],
          socialLinks: {},
          relationshipScore: 85,
          experience: "Senior",
          industry: "Tech",
          remotePreference: "Remote",
          visaStatus: "US Citizen",
          skills: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      vi.mocked(useCandidates).mockReturnValue({
        data: mockCandidates,
        isLoading: false,
        error: null,
      });

      render(<CandidateList />);

      // Check if tags are rendered
      expect(screen.getByText("frontend")).toBeInTheDocument();
      expect(screen.getByText("react")).toBeInTheDocument();
    });

    it("should fallback to legacy tags when normalized_tags are not available", () => {
      const mockCandidates = [
        {
          id: "1",
          name: "Jane Smith",
          role: "Product Manager",
          email: "<EMAIL>",
          phone: "************",
          location: "San Francisco",
          avatar: "/placeholder.svg",
          recruiter: { id: "1", name: "Recruiter", avatar: "/placeholder.svg" },
          tags: ["product", "agile", "strategy"],
          normalized_tags: [], // Empty normalized tags
          socialLinks: {},
          relationshipScore: 92,
          experience: "Senior",
          industry: "Tech",
          remotePreference: "Hybrid",
          visaStatus: "US Citizen",
          skills: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      vi.mocked(useCandidates).mockReturnValue({
        data: mockCandidates,
        isLoading: false,
        error: null,
      });

      render(<CandidateList />);

      // Check if legacy tags are rendered
      expect(screen.getByText("product")).toBeInTheDocument();
      expect(screen.getByText("agile")).toBeInTheDocument();
      expect(screen.getByText("strategy")).toBeInTheDocument();
    });
  });

  describe("Tag Filtering", () => {
    it("should filter candidates by normalized tags", () => {
      const mockCandidates = [
        {
          id: "1",
          name: "Developer 1",
          role: "Frontend Developer",
          email: "<EMAIL>",
          phone: "************",
          location: "Remote",
          avatar: "/placeholder.svg",
          recruiter: { id: "1", name: "Recruiter", avatar: "/placeholder.svg" },
          tags: ["frontend"],
          normalized_tags: [{ id: "1", name: "frontend", color: "#3B82F6" }],
          socialLinks: {},
          relationshipScore: 80,
          experience: "Mid",
          industry: "Tech",
          remotePreference: "Remote",
          visaStatus: "US Citizen",
          skills: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: "2",
          name: "Developer 2",
          role: "Backend Developer",
          email: "<EMAIL>",
          phone: "************",
          location: "Remote",
          avatar: "/placeholder.svg",
          recruiter: { id: "1", name: "Recruiter", avatar: "/placeholder.svg" },
          tags: ["backend"],
          normalized_tags: [{ id: "2", name: "backend", color: "#F59E0B" }],
          socialLinks: {},
          relationshipScore: 75,
          experience: "Senior",
          industry: "Tech",
          remotePreference: "Remote",
          visaStatus: "US Citizen",
          skills: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      vi.mocked(useCandidates).mockReturnValue({
        data: mockCandidates,
        isLoading: false,
        error: null,
      });

      // Render with tag filter
      render(<CandidateList filters={{ tags: ["frontend"] }} />);

      // Should show only the frontend developer
      expect(screen.getByText("Developer 1")).toBeInTheDocument();
      expect(screen.queryByText("Developer 2")).not.toBeInTheDocument();
    });
  });
});
