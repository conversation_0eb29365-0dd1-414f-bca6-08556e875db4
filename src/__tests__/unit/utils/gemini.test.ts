import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock the environment variable
const mockEnv = vi.hoisted(() => ({
  VITE_GEMINI_API_KEY: "test-api-key",
}));

vi.mock("import.meta", () => ({
  env: mockEnv,
}));

// Mock the GoogleGenerativeAI
const mockGenerateContent = vi.fn();
const mockStartChat = vi.fn();
const mockGetGenerativeModel = vi.fn();

vi.mock("@google/generative-ai", () => ({
  GoogleGenerativeAI: vi.fn().mockImplementation(() => ({
    getGenerativeModel: mockGetGenerativeModel,
  })),
}));

describe("Gemini Utils", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockGetGenerativeModel.mockReturnValue({
      startChat: mockStartChat,
    });
    mockStartChat.mockReturnValue({
      sendMessage: mockGenerateContent,
    });
  });

  describe("Environment Variable Configuration", () => {
    it("should throw error when VITE_GEMINI_API_KEY is not set", async () => {
      // Temporarily remove the API key
      const originalKey = mockEnv.VITE_GEMINI_API_KEY;
      delete mockEnv.VITE_GEMINI_API_KEY;

      // Clear module cache to force re-import
      vi.resetModules();

      try {
        await import("@/utils/gemini");
        expect.fail("Should have thrown an error");
      } catch (error) {
        expect(error.message).toContain(
          "VITE_GEMINI_API_KEY environment variable is not set",
        );
      }

      // Restore the API key
      mockEnv.VITE_GEMINI_API_KEY = originalKey;
    });

    it("should initialize successfully when VITE_GEMINI_API_KEY is set", async () => {
      mockEnv.VITE_GEMINI_API_KEY = "test-api-key";
      vi.resetModules();

      // This should not throw
      const geminiModule = await import("@/utils/gemini");
      expect(geminiModule).toBeDefined();
    });
  });

  describe("generateText function", () => {
    it("should call Gemini API with correct parameters", async () => {
      mockGenerateContent.mockResolvedValue({
        response: {
          text: () => "Generated text response",
        },
      });

      vi.resetModules();
      const { generateText } = await import("@/utils/gemini");

      const result = await generateText("Test prompt", "Test system prompt");

      expect(result).toBe("Generated text response");
      expect(mockGenerateContent).toHaveBeenCalledWith("Test prompt");
    });

    it("should handle API errors gracefully", async () => {
      mockGenerateContent.mockRejectedValue(new Error("API Error"));

      vi.resetModules();
      const { generateText } = await import("@/utils/gemini");

      await expect(generateText("Test prompt")).rejects.toThrow(
        "Failed to generate text: API Error",
      );
    });
  });
});
