import { describe, it, expect, beforeEach } from "vitest";
import { searchCandidatesOptimized } from "@/utils/optimizedSearch";
import { CandidateType } from "@/types/candidate";

describe("searchCandidatesOptimized - Skills Search", () => {
  let mockCandidates: CandidateType[];

  beforeEach(() => {
    // Create mock candidates with various skill configurations
    mockCandidates = [
      {
        id: "1",
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "+1234567890",
        role: "Frontend Developer",
        location: "New York, NY",
        experience: "Senior",
        tags: ["frontend", "ui"],
        skills: [
          { name: "TypeScript", level: "expert" },
          { name: "React", level: "expert" },
          { name: "Node.js", level: "intermediate" },
        ],
        relationshipScore: 85,
        status: "active",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "2",
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "+1234567891",
        role: "Full Stack Developer",
        location: "San Francisco, CA",
        experience: "Mid",
        tags: ["fullstack"],
        skills: [
          { name: "JavaScript", level: "expert" },
          { name: "Python", level: "intermediate" },
          { name: "Django", level: "intermediate" },
        ],
        relationshipScore: 75,
        status: "active",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "3",
        name: "Charlie Davis",
        email: "<EMAIL>",
        phone: "+1234567892",
        role: "Backend Developer",
        location: "Austin, TX",
        experience: "Senior",
        tags: ["backend", "api"],
        normalized_skills: [
          { id: "1", name: "Python", category: "Programming Language" },
          { id: "2", name: "TypeScript", category: "Programming Language" },
          { id: "3", name: "PostgreSQL", category: "Database" },
        ],
        relationshipScore: 90,
        status: "active",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "4",
        name: "Diana Lee",
        email: "<EMAIL>",
        phone: "+1234567893",
        role: "DevOps Engineer",
        location: "Seattle, WA",
        experience: "Lead",
        tags: ["devops", "cloud", "react-expert"], // Tag containing skill name
        skills: ["Docker", "Kubernetes", "AWS"], // Legacy string array format
        relationshipScore: 95,
        status: "active",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
  });

  describe("Basic Skills Search", () => {
    it("should find candidates with TypeScript skill", () => {
      const results = searchCandidatesOptimized(
        mockCandidates,
        "TypeScript",
        {},
      );

      expect(results).toHaveLength(2);
      expect(results.map((c) => c.name)).toContain("Alice Johnson");
      expect(results.map((c) => c.name)).toContain("Charlie Davis");
    });

    it("should find candidates with React skill", () => {
      const results = searchCandidatesOptimized(mockCandidates, "React", {});

      expect(results).toHaveLength(2);
      expect(results.map((c) => c.name)).toContain("Alice Johnson");
      expect(results.map((c) => c.name)).toContain("Diana Lee"); // Has 'react-expert' tag
    });

    it("should find candidates with Python skill", () => {
      const results = searchCandidatesOptimized(mockCandidates, "Python", {});

      expect(results).toHaveLength(2);
      expect(results.map((c) => c.name)).toContain("Bob Smith");
      expect(results.map((c) => c.name)).toContain("Charlie Davis");
    });
  });

  describe("Case-Insensitive Skills Search", () => {
    it("should find candidates regardless of case", () => {
      const results1 = searchCandidatesOptimized(
        mockCandidates,
        "typescript",
        {},
      );
      const results2 = searchCandidatesOptimized(
        mockCandidates,
        "TYPESCRIPT",
        {},
      );
      const results3 = searchCandidatesOptimized(
        mockCandidates,
        "TypeScript",
        {},
      );

      expect(results1).toHaveLength(2);
      expect(results2).toHaveLength(2);
      expect(results3).toHaveLength(2);
      expect(results1).toEqual(results2);
      expect(results2).toEqual(results3);
    });
  });

  describe("Partial Skills Search", () => {
    it("should find candidates with partial skill matches", () => {
      const results = searchCandidatesOptimized(mockCandidates, "Script", {});

      expect(results).toHaveLength(3); // TypeScript, JavaScript
      expect(results.map((c) => c.name)).toContain("Alice Johnson");
      expect(results.map((c) => c.name)).toContain("Bob Smith");
      expect(results.map((c) => c.name)).toContain("Charlie Davis");
    });

    it('should find candidates with partial skill "Java"', () => {
      const results = searchCandidatesOptimized(mockCandidates, "Java", {});

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe("Bob Smith"); // JavaScript
    });
  });

  describe("Multiple Terms Search", () => {
    it("should find candidates matching all terms", () => {
      const results = searchCandidatesOptimized(
        mockCandidates,
        "TypeScript React",
        {},
      );

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe("Alice Johnson");
    });

    it("should find candidates with skills and name", () => {
      const results = searchCandidatesOptimized(
        mockCandidates,
        "Charlie Python",
        {},
      );

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe("Charlie Davis");
    });
  });

  describe("Edge Cases", () => {
    it("should return empty array for short queries", () => {
      expect(searchCandidatesOptimized(mockCandidates, "JS", {})).toHaveLength(
        0,
      );
      expect(searchCandidatesOptimized(mockCandidates, "", {})).toHaveLength(0);
    });

    it("should return empty array for no matches", () => {
      const results = searchCandidatesOptimized(
        mockCandidates,
        "Ruby Rails",
        {},
      );
      expect(results).toHaveLength(0);
    });

    it("should handle candidates with different skill formats", () => {
      // Diana has legacy string array skills
      const dockerResults = searchCandidatesOptimized(
        mockCandidates,
        "Docker",
        {},
      );
      expect(dockerResults).toHaveLength(1);
      expect(dockerResults[0].name).toBe("Diana Lee");

      // Charlie has normalized_skills
      const postgresResults = searchCandidatesOptimized(
        mockCandidates,
        "PostgreSQL",
        {},
      );
      expect(postgresResults).toHaveLength(1);
      expect(postgresResults[0].name).toBe("Charlie Davis");
    });

    it("should handle candidates with no skills", () => {
      const candidatesWithNoSkills = [
        ...mockCandidates,
        {
          id: "5",
          name: "Eve Wilson",
          email: "<EMAIL>",
          phone: "+1234567894",
          role: "Project Manager",
          location: "Chicago, IL",
          tags: ["management"],
          relationshipScore: 80,
          status: "active",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      const results = searchCandidatesOptimized(
        candidatesWithNoSkills,
        "management",
        {},
      );
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe("Eve Wilson");
    });
  });

  describe("Skills Filter Integration", () => {
    it("should filter by specific skills when using filters", () => {
      const filters = {
        skills: [
          { name: "TypeScript", required: true },
          { name: "React", required: false },
        ],
      };

      const results = searchCandidatesOptimized(
        mockCandidates,
        "Developer",
        filters,
      );

      // Should find candidates who are developers AND have TypeScript or React
      expect(results.length).toBeGreaterThan(0);
      expect(results.every((c) => c.role.includes("Developer"))).toBe(true);
    });
  });

  describe("Performance", () => {
    it("should handle large candidate lists efficiently", () => {
      // Create a large dataset
      const largeCandidateList = Array(1000)
        .fill(null)
        .map((_, index) => ({
          ...mockCandidates[0],
          id: `large-${index}`,
          name: `Candidate ${index}`,
          skills:
            index % 2 === 0
              ? [{ name: "TypeScript", level: "expert" }]
              : [{ name: "JavaScript", level: "expert" }],
        }));

      const startTime = performance.now();
      const results = searchCandidatesOptimized(
        largeCandidateList,
        "TypeScript",
        {},
      );
      const endTime = performance.now();

      expect(results).toHaveLength(500);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in less than 100ms
    });
  });
});
