import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { WorkflowExecutionEngine } from "@/components/ai/workflow/WorkflowExecutionEngine";
import { SendEmailExecutor } from "@/engine/executors/send-email";
import { SkillsMatchExecutor } from "@/engine/executors/skills-match";
import { AIScreenExecutor } from "@/engine/executors/ai-screen";
import { UpdateStatusExecutor } from "@/engine/executors/update-status";
import { NewApplicationTriggerExecutor } from "@/engine/executors/new-application";
import { executorRegistry } from "@/engine/ExecutorRegistry";
import { workflowLogger, LogLevel } from "@/services/WorkflowLogger";
import {
  createMockWorkflow,
  createMockSupabaseResponse,
  createMockServices,
} from "../utils/mockServices";
import { supabase } from "@/integrations/supabase/client";

// Mock all executors
vi.mock("@/engine/executors", () => {
  // Register mocked executors
  executorRegistry.register(new SendEmailExecutor());
  executorRegistry.register(new SkillsMatchExecutor());
  executorRegistry.register(new AIScreenExecutor());
  executorRegistry.register(new UpdateStatusExecutor());
  executorRegistry.register(new NewApplicationTriggerExecutor());

  return {};
});

// Mock services
vi.mock("@/services", () => createMockServices());
vi.mock("@/services/WorkflowLogger", () => ({
  workflowLogger: {
    log: vi.fn().mockResolvedValue(undefined),
  },
  LogLevel: {
    DEBUG: "debug",
    INFO: "info",
    WARN: "warn",
    ERROR: "error",
  },
}));

vi.mock("@/services/WorkflowAlertService", () => ({
  workflowAlertService: {
    checkAndSendAlerts: vi.fn().mockResolvedValue(undefined),
  },
}));

vi.mock("@/engine/RealtimeEventPublisher", () => ({
  realtimePublisher: {
    publish: vi.fn(),
  },
}));

describe("WorkflowExecutionEngine Integration Tests", () => {
  let mockSupabase: any;
  const userId = "test-user-123";
  const workflowId = "test-workflow-123";

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup Supabase mocks
    mockSupabase = {
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn(() =>
              Promise.resolve(
                createMockSupabaseResponse({
                  id: workflowId,
                  name: "Test Workflow",
                  configuration: createMockWorkflow(),
                }),
              ),
            ),
          })),
        })),
        insert: vi.fn(() =>
          Promise.resolve(createMockSupabaseResponse({ id: "execution-123" })),
        ),
        update: vi.fn(() => ({
          eq: vi.fn(() => Promise.resolve(createMockSupabaseResponse(null))),
        })),
      })),
      channel: vi.fn(() => ({
        subscribe: vi.fn(() => ({ unsubscribe: vi.fn() })),
        send: vi.fn(),
      })),
    };

    vi.mocked(supabase).from = mockSupabase.from;
    vi.mocked(supabase).channel = mockSupabase.channel;
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe("Basic Workflow Execution", () => {
    it("should execute a simple workflow successfully", async () => {
      const workflow = createMockWorkflow();
      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
        { candidateId: "test-candidate-123" },
      );

      const result = await engine.execute();

      expect(result.success).toBe(true);
      expect(result.executionPath).toContain("trigger-1");
      expect(result.executionPath).toContain("action-1");
      expect(result.logs).toHaveLength(2);
      expect(result.logs[0].status).toBe("success");
    });

    it("should handle workflow with no trigger node", async () => {
      const workflow = createMockWorkflow();
      // Remove trigger node
      workflow.nodes = workflow.nodes.filter((n) => n.id !== "trigger-1");

      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
      );

      const result = await engine.execute();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain("No trigger node found");
    });

    it("should track execution metrics", async () => {
      const workflow = createMockWorkflow();
      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
      );

      const result = await engine.execute();

      // Check that workflow logger was called with metrics
      expect(workflowLogger.log).toHaveBeenCalledWith(
        expect.objectContaining({
          level: "info",
          message: expect.stringContaining("Starting workflow execution"),
          workflowId,
          userId,
        }),
      );
    });
  });

  describe("Complex Workflow Patterns", () => {
    it("should execute workflow with conditional branching", async () => {
      const workflow = {
        id: "conditional-workflow",
        name: "Conditional Workflow",
        nodes: [
          {
            id: "trigger-1",
            type: "trigger",
            position: { x: 100, y: 100 },
            data: {
              type: "trigger",
              category: "triggers",
              label: "New Application",
              config: { triggerType: "new-application" },
            },
          },
          {
            id: "condition-1",
            type: "condition",
            position: { x: 300, y: 100 },
            data: {
              type: "skills-match",
              label: "Check Skills",
              config: {
                requiredSkills: "JavaScript, React",
                minMatchPercentage: 70,
              },
            },
          },
          {
            id: "action-approved",
            type: "action",
            position: { x: 500, y: 50 },
            data: {
              type: "send-email",
              label: "Send Welcome Email",
              config: { template: "welcome" },
            },
          },
          {
            id: "action-rejected",
            type: "action",
            position: { x: 500, y: 150 },
            data: {
              type: "send-email",
              label: "Send Rejection Email",
              config: { template: "rejection" },
            },
          },
        ],
        edges: [
          {
            id: "edge-1",
            source: "trigger-1",
            target: "condition-1",
            type: "default",
          },
          {
            id: "edge-approved",
            source: "condition-1",
            target: "action-approved",
            type: "conditional",
            data: { condition: "true" },
          },
          {
            id: "edge-rejected",
            source: "condition-1",
            target: "action-rejected",
            type: "conditional",
            data: { condition: "false" },
          },
        ],
      };

      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
        { candidateId: "test-candidate-123" },
      );

      const result = await engine.execute();

      expect(result.success).toBe(true);
      expect(result.executionPath).toContain("trigger-1");
      expect(result.executionPath).toContain("condition-1");
      // Should take the approved path (candidate has JavaScript and React skills)
      expect(result.executionPath).toContain("action-approved");
      expect(result.executionPath).not.toContain("action-rejected");
    });

    it("should handle parallel execution", async () => {
      const workflow = {
        id: "parallel-workflow",
        name: "Parallel Workflow",
        nodes: [
          {
            id: "trigger-1",
            type: "trigger",
            position: { x: 100, y: 100 },
            data: {
              type: "trigger",
              category: "triggers",
              label: "New Application",
              config: { triggerType: "new-application" },
            },
          },
          {
            id: "action-1",
            type: "action",
            position: { x: 300, y: 50 },
            data: {
              type: "send-email",
              label: "Send Email 1",
              config: { template: "welcome" },
            },
          },
          {
            id: "action-2",
            type: "action",
            position: { x: 300, y: 150 },
            data: {
              type: "update-status",
              label: "Update Status",
              config: { status: "in-review" },
            },
          },
        ],
        edges: [
          {
            id: "edge-1",
            source: "trigger-1",
            target: "action-1",
            type: "parallel",
          },
          {
            id: "edge-2",
            source: "trigger-1",
            target: "action-2",
            type: "parallel",
          },
        ],
      };

      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
        { candidateId: "test-candidate-123" },
      );

      const result = await engine.execute();

      expect(result.success).toBe(true);
      expect(result.executionPath).toContain("trigger-1");
      expect(result.executionPath).toContain("action-1");
      expect(result.executionPath).toContain("action-2");
    });
  });

  describe("Error Handling and Recovery", () => {
    it("should handle node execution failure", async () => {
      const workflow = createMockWorkflow();

      // Make the email executor fail
      const mockServices = await import("@/services");
      const mockEmailIntegration = (await import("@/integrations/email"))
        .default;
      mockEmailIntegration.sendEmail = vi.fn().mockResolvedValue({
        success: false,
        error: "Email service down",
      });

      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
        { candidateId: "test-candidate-123" },
      );

      const result = await engine.execute();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain("Email service down");
      expect(result.executionPath).toContain("trigger-1");
      expect(result.logs.find((log) => log.status === "error")).toBeDefined();
    });

    it("should respect global timeout", async () => {
      const workflow = createMockWorkflow();

      // Make an executor hang
      const mockServices = await import("@/services");
      mockServices.CandidatesService.getCandidate = vi.fn(
        () => new Promise(() => {}), // Never resolves
      );

      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
        { candidateId: "test-candidate-123" },
      );

      // Set a short global timeout
      const workflowConfig = {
        enableRealtime: false,
        globalTimeout: 200, // 200ms
      };

      const result = await engine.execute();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain("timeout");
    });

    it("should continue workflow after recoverable error", async () => {
      const workflow = {
        id: "recovery-workflow",
        name: "Recovery Workflow",
        nodes: [
          {
            id: "trigger-1",
            type: "trigger",
            position: { x: 100, y: 100 },
            data: {
              type: "trigger",
              category: "triggers",
              label: "New Application",
              config: { triggerType: "new-application" },
            },
          },
          {
            id: "action-1",
            type: "action",
            position: { x: 300, y: 100 },
            data: {
              type: "send-email",
              label: "Optional Email",
              config: {
                template: "welcome",
                optional: true, // Mark as optional
              },
            },
          },
          {
            id: "action-2",
            type: "action",
            position: { x: 500, y: 100 },
            data: {
              type: "update-status",
              label: "Update Status",
              config: { status: "processed" },
            },
          },
        ],
        edges: [
          {
            id: "edge-1",
            source: "trigger-1",
            target: "action-1",
            type: "default",
          },
          {
            id: "edge-2",
            source: "action-1",
            target: "action-2",
            type: "default",
          },
        ],
      };

      // Make email fail
      const mockEmailIntegration = (await import("@/integrations/email"))
        .default;
      mockEmailIntegration.sendEmail = vi.fn().mockResolvedValue({
        success: false,
        error: "Email service down",
      });

      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
        { candidateId: "test-candidate-123" },
      );

      const result = await engine.execute();

      // Should still succeed if optional nodes fail
      expect(result.executionPath).toContain("trigger-1");
      expect(result.executionPath).toContain("action-1");
      expect(result.executionPath).toContain("action-2");
    });
  });

  describe("Database Side Effects", () => {
    it("should create execution record in database", async () => {
      const workflow = createMockWorkflow();
      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
      );

      await engine.execute();

      // Verify execution was logged to database
      expect(mockSupabase.from).toHaveBeenCalledWith("workflow_executions");
      expect(mockSupabase.from().insert).toHaveBeenCalledWith(
        expect.objectContaining({
          workflow_id: workflowId,
          user_id: userId,
          status: "running",
        }),
      );
    });

    it("should update execution status on completion", async () => {
      const workflow = createMockWorkflow();
      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
      );

      await engine.execute();

      // Verify execution status was updated
      expect(mockSupabase.from().update).toHaveBeenCalledWith(
        expect.objectContaining({
          status: "completed",
          completed_at: expect.any(String),
        }),
      );
    });

    it("should send realtime updates during execution", async () => {
      const workflow = createMockWorkflow();
      const mockChannel = {
        subscribe: vi.fn(() => ({ unsubscribe: vi.fn() })),
        send: vi.fn(),
      };

      mockSupabase.channel = vi.fn(() => mockChannel);

      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
      );

      await engine.execute();

      // Verify realtime events were sent
      const { realtimePublisher } = await import(
        "@/engine/RealtimeEventPublisher"
      );
      expect(realtimePublisher.publishEvent).toHaveBeenCalledWith(
        `workflow-${workflowId}`,
        expect.objectContaining({
          type: "started",
          nodeId: workflowId,
        }),
      );
      expect(realtimePublisher.publishEvent).toHaveBeenCalledWith(
        `workflow-${workflowId}`,
        expect.objectContaining({
          type: "success",
          nodeId: workflowId,
        }),
      );
    });

    it("should trigger alerts on workflow completion", async () => {
      const workflow = createMockWorkflow();
      const engine = new WorkflowExecutionEngine(
        workflowId,
        userId,
        workflow.nodes,
        workflow.edges,
      );

      await engine.execute();

      // Verify alerts were checked
      const { workflowAlertService: alertService } = await import(
        "@/services/WorkflowAlertService"
      );
      expect(alertService.checkAndSendAlerts).toHaveBeenCalledWith(
        workflowId,
        "Test Workflow",
        expect.any(String), // executionId
        "completed",
        expect.any(Number), // duration
      );
    });
  });
});
