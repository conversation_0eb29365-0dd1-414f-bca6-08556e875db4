import { describe, it, expect, beforeAll, afterAll } from "vitest";
import { supabase } from "@/integrations/supabase/client";
import { searchCandidates } from "@/utils/searchCandidates";
import { searchJobs } from "@/utils/searchJobs";
import { searchMessages } from "@/utils/searchMessages";

describe("Search Integration Tests", () => {
  let testUserId: string;
  let testCandidateId: string;
  let testJobId: string;
  let testMessageId: string;

  beforeAll(async () => {
    // Create test user and authenticate
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: "<EMAIL>",
      password: "testPassword123!",
    });

    if (authError) throw authError;
    testUserId = authData.user!.id;
  });

  afterAll(async () => {
    // Cleanup test data
    if (testCandidateId) {
      await supabase.from("candidates").delete().eq("id", testCandidateId);
    }
    if (testJobId) {
      await supabase.from("jobs").delete().eq("id", testJobId);
    }
    if (testMessageId) {
      await supabase.from("messages").delete().eq("id", testMessageId);
    }

    // Delete test user
    await supabase.auth.signOut();
  });

  describe("Full-text Search Validation", () => {
    it("should create test data with search vectors", async () => {
      // Create a test candidate
      const { data: candidateData, error: candidateError } = await supabase
        .from("candidates")
        .insert({
          user_id: testUserId,
          name: "John Doe",
          email: "<EMAIL>",
          role: "Senior Software Engineer",
          location: "San Francisco, CA",
          experience: "5 years",
          industry: "Technology",
          tags: ["javascript", "react", "nodejs"],
          ai_summary: "Experienced full-stack developer with expertise in modern web technologies",
        })
        .select()
        .single();

      expect(candidateError).toBeNull();
      expect(candidateData).toBeDefined();
      testCandidateId = candidateData!.id;

      // Verify search vector was created
      const { data: vectorCheck } = await supabase
        .from("candidates")
        .select("search_vector")
        .eq("id", testCandidateId)
        .single();

      expect(vectorCheck?.search_vector).toBeDefined();
      expect(vectorCheck?.search_vector).not.toBeNull();
    });

    it("should search candidates by name", async () => {
      const results = await searchCandidates("John", {});
      
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].name).toContain("John");
    });

    it("should search candidates by role", async () => {
      const results = await searchCandidates("Software Engineer", {});
      
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].role).toContain("Software Engineer");
    });

    it("should search candidates by skills in tags", async () => {
      const results = await searchCandidates("javascript", {});
      
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].tags).toContain("javascript");
    });

    it("should handle multi-term searches", async () => {
      const results = await searchCandidates("John San Francisco", {});
      
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
    });

    it("should apply filters correctly", async () => {
      const results = await searchCandidates("", {
        remoteOnly: true,
      });
      
      // This should return empty results since our test candidate doesn't have remote preference set
      expect(results).toBeDefined();
    });
  });

  describe("Search Performance", () => {
    it("should complete searches within acceptable time", async () => {
      const startTime = performance.now();
      const results = await searchCandidates("engineer", {});
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(1000); // Should complete within 1 second
      expect(results).toBeDefined();
    });

    it("should handle empty queries efficiently", async () => {
      const startTime = performance.now();
      const results = await searchCandidates("", {});
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(500); // Empty queries should be faster
      expect(results).toBeDefined();
    });
  });

  describe("Job Search Validation", () => {
    beforeAll(async () => {
      // Create a test job
      const { data: jobData, error: jobError } = await supabase
        .from("jobs")
        .insert({
          title: "Senior Frontend Developer",
          department: "Engineering",
          location: "Remote",
          description: "We are looking for an experienced frontend developer",
          job_type: "Full-time",
          experience_required: "5+ years",
          requirements: ["React", "TypeScript", "CSS"],
          benefits: ["Health insurance", "401k", "Remote work"],
          is_active: true,
          user_id: testUserId,
        })
        .select()
        .single();

      expect(jobError).toBeNull();
      expect(jobData).toBeDefined();
      testJobId = jobData!.id;
    });

    it("should search jobs by title", async () => {
      const results = await searchJobs("Frontend Developer", {});
      
      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].title).toContain("Frontend Developer");
    });

    it("should filter remote jobs", async () => {
      const results = await searchJobs("", { remoteOnly: true });
      
      expect(results).toBeDefined();
      expect(results.some(job => job.location?.includes("Remote") || job.job_type?.includes("Remote"))).toBe(true);
    });
  });

  describe("Search Vector Accuracy", () => {
    it("should rank exact matches higher", async () => {
      // Create two candidates with different relevance
      const { data: exactMatch } = await supabase
        .from("candidates")
        .insert({
          user_id: testUserId,
          name: "Python Developer",
          email: "<EMAIL>",
          role: "Python Developer",
        })
        .select()
        .single();

      const { data: partialMatch } = await supabase
        .from("candidates")
        .insert({
          user_id: testUserId,
          name: "Java Developer with Python knowledge",
          email: "<EMAIL>",
          role: "Java Developer",
        })
        .select()
        .single();

      // Wait for search vectors to be generated
      await new Promise(resolve => setTimeout(resolve, 1000));

      const results = await searchCandidates("Python Developer", {});

      expect(results).toBeDefined();
      expect(results.length).toBeGreaterThanOrEqual(2);
      
      // The exact match should appear before the partial match
      const exactMatchIndex = results.findIndex(r => r.id === exactMatch?.id);
      const partialMatchIndex = results.findIndex(r => r.id === partialMatch?.id);
      
      expect(exactMatchIndex).toBeLessThan(partialMatchIndex);

      // Cleanup
      await supabase.from("candidates").delete().eq("id", exactMatch?.id);
      await supabase.from("candidates").delete().eq("id", partialMatch?.id);
    });
  });
});
