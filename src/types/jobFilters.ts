/**
 * Job Filtering Types
 * Interfaces and types for filtering job listings
 */

export interface JobFilters {
  search?: string;
  jobType?: string;
  experienceLevel?: string;
  salaryRange?: [number, number];
  location?: string;
  urgentOnly?: boolean;
  featuredOnly?: boolean;
}

export interface JobFiltersProps {
  filters: JobFilters;
  onFiltersChange: (filters: JobFilters) => void;
}

// Job type options that match the database values
export const JOB_TYPE_OPTIONS = [
  { value: "all", label: "All" },
  { value: "Full-time", label: "Full-time" },
  { value: "Part-time", label: "Part-time" },
  { value: "Contract", label: "Contract" },
  { value: "Internship", label: "Internship" },
  { value: "Freelance", label: "Freelance" },
] as const;

// Experience level options
export const EXPERIENCE_LEVEL_OPTIONS = [
  { value: "entry", label: "Entry Level" },
  { value: "mid", label: "Mid Level" },
  { value: "senior", label: "Senior Level" },
  { value: "lead", label: "Lead" },
] as const;

// Location type options
export const LOCATION_TYPE_OPTIONS = [
  { value: "remote", label: "Remote" },
  { value: "hybrid", label: "Hybrid" },
  { value: "onsite", label: "On-site" },
] as const;

// Default filter values
export const DEFAULT_FILTERS: JobFilters = {
  search: "",
  jobType: "all",
  experienceLevel: "all",
  salaryRange: [0, 300000],
  location: "all",
  urgentOnly: false,
  featuredOnly: false,
};

// Salary range constants
export const SALARY_RANGE = {
  MIN: 0,
  MAX: 300000,
  STEP: 5000,
  DEFAULT_MIN: 40000,
  DEFAULT_MAX: 150000,
} as const;
