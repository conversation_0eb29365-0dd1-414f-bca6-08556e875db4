export interface SearchFilters {
  department?: string;
  location?: {
    address: string;
    radius: number;
  };
  experience?: string;
  remote?: boolean;
  urgent?: boolean;
  jobType?: string;
  salaryRange?: string;
  skills?: {
    name: string;
    required: boolean;
    proficiency?: "beginner" | "intermediate" | "expert";
    id?: string; // Optional skill ID from the skills table
  }[];
  remoteOnly?: boolean;
  visaSponsor?: boolean;
}

export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: SearchFilters;
  createdAt: Date;
}

export interface SearchResult {
  id: string;
  title: string;
  type: "candidate" | "job";
  description: string;
  location?: string;
  match?: number;
  avatar?: string;
  tags?: string[];
}
