/**
 * Comprehensive TypeScript definitions for Workflow configurations
 */

// Base configuration interface
export interface BaseWorkflowConfig {
  id?: string;
  name?: string;
  description?: string;
}

// LinkedIn Integration Configuration
export interface LinkedInIntegrationConfig extends BaseWorkflowConfig {
  actionType:
    | "post-job"
    | "search-candidates"
    | "send-message"
    | "get-profile"
    | "company-update";
  jobTitle?: string;
  jobDescription?: string;
  location?: string;
  searchKeywords?: string;
  maxResults?: string;
  recipient?: string;
  message?: string;
  profileId?: string;
  useCompanyPage?: boolean;
  trackAnalytics?: boolean;
}

// Job Board Integration Configuration
export interface JobBoardIntegrationConfig extends BaseWorkflowConfig {
  jobBoard:
    | "indeed"
    | "glassdoor"
    | "monster"
    | "ziprecruiter"
    | "dice"
    | "custom";
  customJobBoard?: string;
  actionType: "post-job" | "update-job" | "close-job" | "get-applications";
  jobId?: string;
  jobDataSource?: "context" | "database" | "custom";
  customJobData?: string;
  maxResults?: string;
  filterStatus?: "all" | "new" | "reviewed" | "contacted" | "rejected";
  sponsoredListing?: boolean;
  autoImportResponses?: boolean;
}

// ATS Integration Configuration
export interface ATSIntegrationConfig extends BaseWorkflowConfig {
  atsProvider:
    | "greenhouse"
    | "lever"
    | "workday"
    | "bamboohr"
    | "workable"
    | "custom";
  customAtsName?: string;
  actionType:
    | "create-candidate"
    | "update-candidate"
    | "add-note"
    | "schedule-interview"
    | "update-status"
    | "get-candidate";
  candidateId?: string;
  candidateDataSource?: "context" | "database" | "custom";
  customCandidateData?: string;
  noteContent?: string;
  noteVisibility?: "public" | "private" | "team";
  interviewType?: "phone" | "technical" | "behavioral" | "onsite" | "final";
  interviewDate?: string;
  interviewTime?: string;
  newStatus?:
    | "applied"
    | "screening"
    | "interview"
    | "offer"
    | "hired"
    | "rejected"
    | "custom";
  customStatus?: string;
  syncBidirectional?: boolean;
}

// Calendar Integration Configuration
export interface CalendarIntegrationConfig extends BaseWorkflowConfig {
  calendarProvider: "google" | "outlook" | "apple" | "zoom" | "custom";
  customProvider?: string;
  actionType:
    | "create-event"
    | "update-event"
    | "delete-event"
    | "get-availability"
    | "find-meeting-time";
  eventId?: string;
  eventTitle?: string;
  startDateTime?: string;
  endDateTime?: string;
  attendees?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  calendarIds?: string;
  duration?: string;
  workingHoursStart?: string;
  workingHoursEnd?: string;
  sendNotifications?: boolean;
}

// Trigger Configuration Types
export interface NewApplicationTriggerConfig extends BaseWorkflowConfig {
  jobTypes:
    | "all"
    | "engineering"
    | "design"
    | "marketing"
    | "sales"
    | "product"
    | "management"
    | "custom";
  customJobFilter?: string;
  sourceFilter:
    | "all"
    | "website"
    | "linkedin"
    | "indeed"
    | "referral"
    | "custom";
  customSource?: string;
  notifyTeam?: boolean;
  autoScreen?: boolean;
}

export interface ApplicationStatusTriggerConfig extends BaseWorkflowConfig {
  statusType:
    | "any"
    | "approved"
    | "rejected"
    | "pending"
    | "interview"
    | "offer"
    | "hired"
    | "custom";
  customStatus?: string;
  jobFilter:
    | "all"
    | "engineering"
    | "design"
    | "marketing"
    | "sales"
    | "product"
    | "custom";
  customJobFilter?: string;
  includeInternal?: boolean;
}

export interface ScheduledTriggerConfig extends BaseWorkflowConfig {
  scheduleType: "daily" | "weekly" | "monthly" | "custom";
  dayOfWeek?: string;
  dayOfMonth?: string;
  cronExpression?: string;
  time?: string;
  context?: string;
}

export interface DocumentUploadTriggerConfig extends BaseWorkflowConfig {
  documentType:
    | "all"
    | "resume"
    | "cover_letter"
    | "portfolio"
    | "id"
    | "certificate"
    | "custom";
  customDocumentType?: string;
  fileFormat: "all" | "pdf" | "doc" | "image" | "text" | "custom";
  customFileFormat?: string;
  autoProcess?: boolean;
  notifyUpload?: boolean;
}

export interface WebhookTriggerConfig extends BaseWorkflowConfig {
  event:
    | "all"
    | "candidate_created"
    | "status_changed"
    | "document_uploaded"
    | "interview_scheduled"
    | "custom";
  customEvent?: string;
  secretKey?: string;
  payloadFilter?: string;
  validateSignature?: boolean;
}

export interface DatabaseTriggerConfig extends BaseWorkflowConfig {
  table:
    | "candidates"
    | "jobs"
    | "applications"
    | "interviews"
    | "documents"
    | "custom";
  customTable?: string;
  operation: "all" | "insert" | "update" | "delete";
  filterCondition?: string;
  includeOldData?: boolean;
}

// Output/Action Configuration Types
export interface UpdateStatusConfig extends BaseWorkflowConfig {
  newStatus:
    | "screening"
    | "interview"
    | "technical"
    | "reference"
    | "offer"
    | "hired"
    | "rejected"
    | "on_hold"
    | "withdrawn"
    | "custom";
  customStatus?: string;
  statusNote?: string;
  notifyCandidate?: boolean;
  notifyTeam?: boolean;
  logActivity?: boolean;
}

export interface AddToPoolConfig extends BaseWorkflowConfig {
  poolName:
    | "engineering"
    | "design"
    | "marketing"
    | "sales"
    | "product"
    | "management"
    | "future"
    | "custom";
  customPoolName?: string;
  tags?: string;
  followUpDate?: string;
  notes?: string;
  notifyCandidate?: boolean;
}

export interface SendMessageConfig extends BaseWorkflowConfig {
  template:
    | "follow-up"
    | "status-update"
    | "interview-prep"
    | "offer-details"
    | "rejection"
    | "onboarding"
    | "custom";
  messageContent?: string;
  channel: "email" | "sms" | "in-app" | "whatsapp";
  sendImmediately?: boolean;
  scheduleDate?: string;
  trackOpens?: boolean;
}

export interface ExportDataConfig extends BaseWorkflowConfig {
  format: "csv" | "json" | "xlsx" | "pdf" | "xml";
  exportBasic?: boolean;
  exportContact?: boolean;
  exportSkills?: boolean;
  exportHistory?: boolean;
  destination: "download" | "email" | "storage" | "sftp" | "api";
  emailRecipients?: string;
  apiEndpoint?: string;
  includeHeaders?: boolean;
}

export interface ArchiveCandidateConfig extends BaseWorkflowConfig {
  reason:
    | "hired"
    | "rejected"
    | "withdrawn"
    | "duplicate"
    | "not-qualified"
    | "position-filled"
    | "custom";
  customReason?: string;
  note?: string;
  retentionPeriod: "30d" | "90d" | "6m" | "1y" | "2y" | "permanent";
  notifyCandidate?: boolean;
  archiveDocuments?: boolean;
}

export interface GenerateReportConfig extends BaseWorkflowConfig {
  reportType: "candidate" | "interview" | "assessment" | "hiring" | "custom";
  customReportName?: string;
  format: "pdf" | "docx" | "html" | "text";
  includeSummary?: boolean;
  includeDetails?: boolean;
  includeTimeline?: boolean;
  includeRecommendations?: boolean;
  distribution: "email" | "download" | "store" | "share";
  emailRecipients?: string;
}

// Transformation Configuration Types
export interface DataTransformConfig extends BaseWorkflowConfig {
  transformType: "map" | "format" | "calculate" | "extract" | "custom";
  fieldMapping?: string;
  formatTemplate?: string;
  outputFormat?: "text" | "html" | "markdown" | "json";
  calculationExpression?: string;
  outputField?: string;
  sourceField?: string;
  extractionMethod?: "regex" | "jsonpath" | "xpath" | "substring";
  extractionPattern?: string;
  customCode?: string;
  preserveOriginal?: boolean;
}

export interface DataFilterConfig extends BaseWorkflowConfig {
  filterType: "include" | "exclude" | "condition" | "custom";
  fields?: string;
  field?: string;
  operator?:
    | "equals"
    | "not-equals"
    | "contains"
    | "greater-than"
    | "less-than"
    | "exists"
    | "not-exists";
  value?: string;
  customExpression?: string;
  caseSensitive?: boolean;
  trimWhitespace?: boolean;
}

export interface DataMergeConfig extends BaseWorkflowConfig {
  mergeType: "simple" | "deep" | "array" | "custom";
  primarySource?: string;
  secondarySource?: string;
  customMergeLogic?: string;
  overwriteExisting?: boolean;
  includeNullValues?: boolean;
}

export interface DataLoopConfig extends BaseWorkflowConfig {
  loopSource?: string;
  loopVariable?: string;
  loopType: "for-each" | "for-of" | "while" | "do-while";
  loopCondition?: string;
  maxIterations?: string;
  parallelExecution?: boolean;
  continueOnError?: boolean;
}

export interface DelayConfig extends BaseWorkflowConfig {
  duration?: string;
  unit: "seconds" | "minutes" | "hours" | "days";
  delayType: "fixed" | "random" | "exponential";
  maxDuration?: string;
  baseDuration?: string;
  retryCount?: string;
  cancelOnStop?: boolean;
}

export interface ScheduleConfig extends BaseWorkflowConfig {
  scheduleType: "date" | "delay" | "cron" | "business-hours";
  date?: string;
  time?: string;
  duration?: string;
  unit?: "minutes" | "hours" | "days" | "weeks";
  cronExpression?: string;
  businessDays?: boolean[];
  businessHoursStart?: string;
  businessHoursEnd?: string;
  timeZone?: string;
  rescheduleOnConflict?: boolean;
}

// Union type for all configuration types
export type WorkflowNodeConfig =
  | LinkedInIntegrationConfig
  | JobBoardIntegrationConfig
  | ATSIntegrationConfig
  | CalendarIntegrationConfig
  | NewApplicationTriggerConfig
  | ApplicationStatusTriggerConfig
  | ScheduledTriggerConfig
  | DocumentUploadTriggerConfig
  | WebhookTriggerConfig
  | DatabaseTriggerConfig
  | UpdateStatusConfig
  | AddToPoolConfig
  | SendMessageConfig
  | ExportDataConfig
  | ArchiveCandidateConfig
  | GenerateReportConfig
  | DataTransformConfig
  | DataFilterConfig
  | DataMergeConfig
  | DataLoopConfig
  | DelayConfig
  | ScheduleConfig;

// Props interface for workflow configuration components
export interface WorkflowConfigProps<T extends WorkflowNodeConfig> {
  config: T;
  setConfig: (config: T) => void;
}
