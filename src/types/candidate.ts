export interface JobMatch {
  id: string;
  title: string;
  company: string;
  location: string; // Keep for backward compatibility
  location_name?: string; // New normalized field
  location_type?: string; // New normalized field
  location_city?: string; // New normalized field
  location_state?: string; // New normalized field
  location_is_remote?: boolean; // New normalized field
  match: number;
  department: string; // Keep for backward compatibility
  department_name?: string; // New normalized field
  salary: string;
  status: string;
  postedDate: string;
  requirements: string[]; // Keep for backward compatibility
  benefits: string[]; // Keep for backward compatibility
  normalized_requirements?: Array<{id: string; name: string; category?: string}>; // New normalized field
  normalized_benefits?: Array<{id: string; name: string; category?: string}>; // New normalized field
  description?: string; // Added optional description property
}

export interface RelationshipFactor {
  name: string;
  score: number;
}

export interface RelationshipAnalysis {
  score: number;
  factors: RelationshipFactor[];
  trend: "increasing" | "stable" | "decreasing";
}

export interface ScreeningQuestion {
  id: string;
  question: string;
  response?: string;
  category: "technical" | "behavioral" | "cultural" | "experience";
}

export interface Requirements {
  workAuthorization: "verified" | "pending" | "not_required";
  backgroundCheck: "verified" | "pending" | "not_required";
  drugScreening: "verified" | "pending" | "not_required";
  references: "verified" | "pending" | "not_required";
}

export interface Evaluation {
  category: string;
  score: number;
  notes: string;
}

export interface CandidateType {
  id: string;
  name: string;
  role: string;
  email: string;
  phone: string;
  // Database normalized fields
  location_id?: string; // Foreign key to locations table
  location_name?: string; // Denormalized from locations table
  location_type?: string; // Denormalized from locations table
  location_city?: string; // Denormalized from locations table
  location_state?: string; // Denormalized from locations table
  location_is_remote?: boolean; // Denormalized from locations table
  // Backward compatibility
  location?: string; // Keep for backward compatibility
  avatar: string;
  recruiter: {
    id: string;
    name: string;
    avatar: string | null;
  };
  // Database normalized fields
  recruiter_id?: string; // Foreign key to profiles table
  recruiter_name?: string; // Denormalized from profiles table
  recruiter_avatar?: string | null; // Denormalized from profiles table
  tags: string[];
  normalized_tags?: {
    id: string;
    name: string;
    color?: string;
  }[];
  socialLinks: {
    github?: string;
    linkedin?: string;
    twitter?: string;
  };
  relationshipScore: number;
  relationshipAnalysis?: RelationshipAnalysis;
  aiSummary?: string;
  matchedJobs?: JobMatch[];
  experience: string;
  industry: string;
  remotePreference: string;
  visaStatus: string;
  skills: {
    name: string;
    level: string;
    years: number;
    category?: string;
  }[];
  normalized_skills?: {
    name: string;
    level: string;
    years: number;
    category?: string;
  }[];
  createdAt: string;
  updatedAt: string;
  screening?: {
    notes?: string;
    dayToDay: string;
    skills: string[];
    location: string;
    compensation: {
      current: number;
      expected: number;
    };
    education: string[];
    questions?: ScreeningQuestion[];
    status: "pending" | "in_progress" | "completed";
    lastUpdated: string;
    requirements: Requirements;
    evaluations: Evaluation[];
  };
  activity?: Array<{
    type: string;
    description: string;
    date: string;
  }>;
  skill_names?: string[];
}
