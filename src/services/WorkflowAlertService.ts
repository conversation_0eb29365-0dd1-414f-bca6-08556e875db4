import { supabase } from "@/integrations/supabase/client";
import { workflowLogger, LogLevel } from "./WorkflowLogger";

export interface AlertConfig {
  workflowId: string;
  enableEmail: boolean;
  emailAddresses?: string[];
  enableSlack: boolean;
  slackWebhookUrl?: string;
  slackChannel?: string;
  alertOnFailure: boolean;
  alertOnSuccess: boolean;
  alertOnDurationThreshold: boolean;
  durationThresholdMs?: number;
  maxAlertsPerHour: number;
}

export interface AlertPayload {
  workflowId: string;
  workflowName: string;
  executionId: string;
  alertType: "failure" | "success" | "duration_exceeded";
  status: string;
  duration: number;
  failedNodes?: Array<{
    nodeId: string;
    nodeName: string;
    error: string;
  }>;
  errorMessage?: string;
  timestamp: Date;
  executionUrl?: string;
}

class WorkflowAlertService {
  private static instance: WorkflowAlertService;

  private constructor() {}

  public static getInstance(): WorkflowAlertService {
    if (!WorkflowAlertService.instance) {
      WorkflowAlertService.instance = new WorkflowAlertService();
    }
    return WorkflowAlertService.instance;
  }

  public async checkAndSendAlerts(
    workflowId: string,
    workflowName: string,
    executionId: string,
    status: "completed" | "failed",
    durationMs: number,
    failedNodes?: Array<{ nodeId: string; nodeName: string; error: string }>,
    errorMessage?: string,
  ): Promise<void> {
    try {
      // Get alert configuration for this workflow
      const { data: alertConfig, error } = await supabase
        .from("workflow_alerts")
        .select("*")
        .eq("workflow_id", workflowId)
        .eq("is_active", true)
        .single();

      if (error || !alertConfig) {
        await workflowLogger.log({
          level: LogLevel.DEBUG,
          message: "No active alert configuration found for workflow",
          workflowId,
          executionId,
        });
        return;
      }

      // Check rate limit
      const canSendAlert = await this.checkRateLimit(alertConfig.id);
      if (!canSendAlert) {
        await workflowLogger.log({
          level: LogLevel.WARN,
          message: "Alert rate limit exceeded",
          workflowId,
          executionId,
          metadata: { alertId: alertConfig.id },
        });
        return;
      }

      // Determine alert type
      let alertType: "failure" | "success" | "duration_exceeded" | null = null;

      if (status === "failed" && alertConfig.alert_on_failure) {
        alertType = "failure";
      } else if (status === "completed" && alertConfig.alert_on_success) {
        alertType = "success";
      } else if (
        alertConfig.alert_on_duration_threshold &&
        alertConfig.duration_threshold_ms &&
        durationMs > alertConfig.duration_threshold_ms
      ) {
        alertType = "duration_exceeded";
      }

      if (!alertType) {
        return;
      }

      // Prepare alert payload
      const alertPayload: AlertPayload = {
        workflowId,
        workflowName,
        executionId,
        alertType,
        status,
        duration: durationMs,
        failedNodes,
        errorMessage,
        timestamp: new Date(),
        executionUrl: `${window.location.origin}/ai-workflows?execution=${executionId}`,
      };

      // Send alerts
      const alertPromises: Promise<void>[] = [];

      if (alertConfig.enable_email && alertConfig.email_addresses?.length > 0) {
        alertPromises.push(this.sendEmailAlert(alertConfig, alertPayload));
      }

      if (alertConfig.enable_slack && alertConfig.slack_webhook_url) {
        alertPromises.push(this.sendSlackAlert(alertConfig, alertPayload));
      }

      await Promise.allSettled(alertPromises);
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to process workflow alerts",
        workflowId,
        executionId,
        error: error as Error,
      });
    }
  }

  private async checkRateLimit(alertId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc("check_alert_rate_limit", {
        p_alert_id: alertId,
      });

      return !error && data === true;
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to check alert rate limit",
        error: error as Error,
      });
      return false;
    }
  }

  private async sendEmailAlert(
    config: any,
    payload: AlertPayload,
  ): Promise<void> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      // Format email content
      const subject = this.formatEmailSubject(payload);
      const body = this.formatEmailBody(payload);

      // Send email through Supabase Edge Function
      const { error } = await supabase.functions.invoke("send-email", {
        body: {
          to: config.email_addresses,
          subject,
          html: body,
          from: "<EMAIL>",
        },
      });

      if (error) {
        throw error;
      }

      // Record alert history
      await this.recordAlertHistory(
        config.id,
        payload.executionId,
        payload.alertType,
        "email",
        config.email_addresses.join(","),
        subject,
        "sent",
      );

      await workflowLogger.log({
        level: LogLevel.INFO,
        message: "Email alert sent successfully",
        workflowId: payload.workflowId,
        executionId: payload.executionId,
        metadata: { recipients: config.email_addresses },
      });
    } catch (error) {
      await this.recordAlertHistory(
        config.id,
        payload.executionId,
        payload.alertType,
        "email",
        config.email_addresses.join(","),
        "Failed to send email",
        "failed",
        (error as Error).message,
      );

      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to send email alert",
        workflowId: payload.workflowId,
        executionId: payload.executionId,
        error: error as Error,
      });
    }
  }

  private async sendSlackAlert(
    config: any,
    payload: AlertPayload,
  ): Promise<void> {
    try {
      const slackMessage = this.formatSlackMessage(payload);

      // Send to Slack webhook
      const response = await fetch(config.slack_webhook_url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          channel: config.slack_channel,
          ...slackMessage,
        }),
      });

      if (!response.ok) {
        throw new Error(`Slack API returned ${response.status}`);
      }

      // Record alert history
      await this.recordAlertHistory(
        config.id,
        payload.executionId,
        payload.alertType,
        "slack",
        config.slack_channel || "default",
        JSON.stringify(slackMessage),
        "sent",
      );

      await workflowLogger.log({
        level: LogLevel.INFO,
        message: "Slack alert sent successfully",
        workflowId: payload.workflowId,
        executionId: payload.executionId,
        metadata: { channel: config.slack_channel },
      });
    } catch (error) {
      await this.recordAlertHistory(
        config.id,
        payload.executionId,
        payload.alertType,
        "slack",
        config.slack_channel || "default",
        "Failed to send Slack message",
        "failed",
        (error as Error).message,
      );

      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to send Slack alert",
        workflowId: payload.workflowId,
        executionId: payload.executionId,
        error: error as Error,
      });
    }
  }

  private async recordAlertHistory(
    alertId: string,
    executionId: string,
    alertType: string,
    alertChannel: string,
    recipient: string,
    alertMessage: string,
    alertStatus: "sent" | "failed",
    errorDetails?: string,
  ): Promise<void> {
    try {
      await supabase.from("workflow_alert_history").insert({
        alert_id: alertId,
        execution_id: executionId,
        alert_type: alertType,
        alert_channel: alertChannel,
        recipient,
        alert_message: alertMessage,
        alert_status: alertStatus,
        error_details: errorDetails,
        sent_at: alertStatus === "sent" ? new Date().toISOString() : null,
      });
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to record alert history",
        error: error as Error,
      });
    }
  }

  private formatEmailSubject(payload: AlertPayload): string {
    const emoji =
      payload.alertType === "failure"
        ? "❌"
        : payload.alertType === "success"
          ? "✅"
          : "⚠️";

    return `${emoji} Workflow Alert: ${payload.workflowName} - ${payload.alertType.replace("_", " ").toUpperCase()}`;
  }

  private formatEmailBody(payload: AlertPayload): string {
    let html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${payload.alertType === "failure" ? "#dc2626" : payload.alertType === "success" ? "#16a34a" : "#f59e0b"};">
          Workflow ${payload.alertType === "failure" ? "Failed" : payload.alertType === "success" ? "Succeeded" : "Duration Exceeded"}
        </h2>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Workflow:</strong> ${payload.workflowName}</p>
          <p><strong>Execution ID:</strong> ${payload.executionId}</p>
          <p><strong>Status:</strong> ${payload.status}</p>
          <p><strong>Duration:</strong> ${(payload.duration / 1000).toFixed(2)} seconds</p>
          <p><strong>Timestamp:</strong> ${payload.timestamp.toLocaleString()}</p>
        </div>
    `;

    if (payload.failedNodes && payload.failedNodes.length > 0) {
      html += `
        <h3 style="color: #dc2626;">Failed Nodes:</h3>
        <ul style="background-color: #fee2e2; padding: 15px 30px; border-radius: 8px;">
      `;

      for (const node of payload.failedNodes) {
        html += `
          <li style="margin: 10px 0;">
            <strong>${node.nodeName}</strong> (${node.nodeId})
            <br/>
            <span style="color: #991b1b; font-size: 14px;">${node.error}</span>
          </li>
        `;
      }

      html += "</ul>";
    }

    if (payload.errorMessage) {
      html += `
        <div style="background-color: #fee2e2; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #dc2626; margin-top: 0;">Error Details:</h3>
          <p style="color: #991b1b;">${payload.errorMessage}</p>
        </div>
      `;
    }

    html += `
        <div style="margin-top: 30px;">
          <a href="${payload.executionUrl}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px; display: inline-block;">
            View Execution Details
          </a>
        </div>
      </div>
    `;

    return html;
  }

  private formatSlackMessage(payload: AlertPayload): any {
    const color =
      payload.alertType === "failure"
        ? "danger"
        : payload.alertType === "success"
          ? "good"
          : "warning";

    const emoji =
      payload.alertType === "failure"
        ? ":x:"
        : payload.alertType === "success"
          ? ":white_check_mark:"
          : ":warning:";

    const attachment: any = {
      color,
      fallback: `${payload.workflowName} - ${payload.alertType}`,
      author_name: "Workflow System",
      title: `${emoji} ${payload.workflowName}`,
      title_link: payload.executionUrl,
      text: `Workflow ${payload.alertType === "failure" ? "failed" : payload.alertType === "success" ? "succeeded" : "duration exceeded"}`,
      fields: [
        {
          title: "Execution ID",
          value: payload.executionId,
          short: true,
        },
        {
          title: "Status",
          value: payload.status,
          short: true,
        },
        {
          title: "Duration",
          value: `${(payload.duration / 1000).toFixed(2)} seconds`,
          short: true,
        },
        {
          title: "Timestamp",
          value: payload.timestamp.toLocaleString(),
          short: true,
        },
      ],
      footer: "Workflow Alert System",
      ts: Math.floor(payload.timestamp.getTime() / 1000),
    };

    if (payload.failedNodes && payload.failedNodes.length > 0) {
      attachment.fields.push({
        title: "Failed Nodes",
        value: payload.failedNodes
          .map((node) => `• *${node.nodeName}*: ${node.error}`)
          .join("\n"),
        short: false,
      });
    }

    if (payload.errorMessage) {
      attachment.fields.push({
        title: "Error Message",
        value: payload.errorMessage,
        short: false,
      });
    }

    return {
      attachments: [attachment],
    };
  }
}

// Export singleton instance
export const workflowAlertService = WorkflowAlertService.getInstance();
