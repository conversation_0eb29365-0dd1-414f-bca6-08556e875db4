import { supabase } from "@/integrations/supabase/client";
import { BudgetAllocationService } from "./BudgetAllocationService";
import { JobExpenseService } from "./JobExpenseService";

export interface BudgetData {
  id: string;
  user_id: string;
  total_budget: number;
  spent_amount: number;
  category: string;
  amount: number;
  percentage: number;
  created_at: string;
  updated_at: string;
}

export interface CreateBudgetData {
  user_id: string;
  total_budget: number;
  spent_amount: number;
  category: string;
  amount: number;
  percentage: number;
}

export interface UpdateBudgetData {
  id: string;
  total_budget?: number;
  spent_amount?: number;
  amount?: number;
  percentage?: number;
}

export class BudgetService {
  static async getBudgetData(userId: string): Promise<BudgetData[]> {
    const { data, error } = await supabase
      .from("budget_data")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createBudgetData(
    budgetData: CreateBudgetData,
  ): Promise<BudgetData> {
    const { data, error } = await supabase
      .from("budget_data")
      .insert([budgetData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateBudgetData(
    updateData: UpdateBudgetData,
  ): Promise<BudgetData> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabase
      .from("budget_data")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteBudgetData(id: string): Promise<void> {
    const { error } = await supabase.from("budget_data").delete().eq("id", id);

    if (error) throw error;
  }

  static async getBudgetSummary(userId: string): Promise<{
    total_budget: number;
    spent_amount: number;
    categories: BudgetData[];
  }> {
    try {
      // Try to get data from the enhanced budget system first
      const budgetSummaries = await BudgetAllocationService.getBudgetSummary(userId);
      const expensesByCategory = await JobExpenseService.getExpenseTotalsByCategory(userId);

      if (budgetSummaries.length > 0) {
        // Use enhanced budget system data
        const totalBudget = budgetSummaries.reduce((sum, s) => sum + s.total_budget, 0);
        const totalSpent = budgetSummaries.reduce((sum, s) => sum + s.spent_amount, 0);

        // Convert to legacy format for backward compatibility
        const categories: BudgetData[] = expensesByCategory.map((expense, index) => ({
          id: `category-${index}`,
          user_id: userId,
          total_budget: totalBudget,
          spent_amount: totalSpent,
          category: expense.category,
          amount: expense.total_amount,
          percentage: totalBudget > 0 ? (expense.total_amount / totalBudget) * 100 : 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }));

        return {
          total_budget: totalBudget,
          spent_amount: totalSpent,
          categories,
        };
      }
    } catch (error) {
      console.log("Enhanced budget system not available, falling back to legacy system");
    }

    // Fallback to legacy budget_data table
    const budgetData = await this.getBudgetData(userId);

    if (budgetData.length === 0) {
      return { total_budget: 0, spent_amount: 0, categories: [] };
    }

    const totalBudget = budgetData[0]?.total_budget || 0;
    const spentAmount = budgetData[0]?.spent_amount || 0;

    return {
      total_budget: totalBudget,
      spent_amount: spentAmount,
      categories: budgetData,
    };
  }
}
