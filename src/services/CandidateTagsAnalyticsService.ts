import { supabase } from "@/integrations/supabase/client";

export interface CandidateTagsStatsData {
  id: string;
  user_id: string;
  tag_name: string;
  candidate_count: number;
  color: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCandidateTagsStatsData {
  user_id: string;
  tag_name: string;
  candidate_count: number;
  color: string;
}

export interface UpdateCandidateTagsStatsData {
  id: string;
  candidate_count?: number;
  color?: string;
}

export interface AnalyticsTagsData {
  id: string;
  user_id: string;
  tag: string;
  current_level: number;
  required_level: number;
  gap: number;
  recommendation: string;
  success_impact: number;
  created_at: string;
  updated_at: string;
  tag_id: string;
  job_id: string;
}

export interface CreateAnalyticsTagsData {
  user_id: string;
  tag: string;
  current_level: number;
  required_level: number;
  gap: number;
  recommendation: string;
  success_impact: number;
  tag_id?: string;
  job_id?: string;
}

// Transform data for charts
export interface TagsChartData {
  name: string;
  value: number;
  color: string;
}

export class CandidateTagsAnalyticsService {
  // Tags Stats
  static async getCandidateTagsStats(
    userId: string,
  ): Promise<CandidateTagsStatsData[]> {
    const { data, error } = await supabase
      .from("candidate_tags_stats")
      .select("*")
      .eq("user_id", userId)
      .order("candidate_count", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createCandidateTagsStats(
    tagData: CreateCandidateTagsStatsData,
  ): Promise<CandidateTagsStatsData> {
    const { data, error } = await supabase
      .from("candidate_tags_stats")
      .insert([tagData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateCandidateTagsStats(
    updateData: UpdateCandidateTagsStatsData,
  ): Promise<CandidateTagsStatsData> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabase
      .from("candidate_tags_stats")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteCandidateTagsStats(id: string): Promise<void> {
    const { error } = await supabase
      .from("candidate_tags_stats")
      .delete()
      .eq("id", id);

    if (error) throw error;
  }

  // Analytics Tags
  static async getAnalyticsTags(
    userId: string,
  ): Promise<AnalyticsTagsData[]> {
    const { data, error } = await supabase
      .from("analytics_tags")
      .select("*")
      .eq("user_id", userId)
      .order("success_impact", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createAnalyticsTags(
    analyticsData: CreateAnalyticsTagsData,
  ): Promise<AnalyticsTagsData> {
    const { data, error } = await supabase
      .from("analytics_tags")
      .insert([analyticsData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateAnalyticsTags(
    id: string,
    updates: Partial<CreateAnalyticsTagsData>,
  ): Promise<AnalyticsTagsData> {
    const { data, error } = await supabase
      .from("analytics_tags")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteAnalyticsTags(id: string): Promise<void> {
    const { error } = await supabase
      .from("analytics_tags")
      .delete()
      .eq("id", id);

    if (error) throw error;
  }

  // Transform data for charts
  static transformTagsDataForChart(
    data: CandidateTagsStatsData[],
  ): TagsChartData[] {
    return data.map((item) => ({
      name: item.tag_name,
      value: item.candidate_count,
      color: item.color || "#3b82f6",
    }));
  }

  // Initialize tags data
  static async initializeTagsData(
    userId: string,
  ): Promise<CandidateTagsStatsData[]> {
    try {
      // Get all tags used by candidates for this user
      const { data: tagStats, error } = await supabase
        .from("candidates_with_normalized_data")
        .select("normalized_tags")
        .eq("user_id", userId)
        .not("normalized_tags", "eq", "[]");

      if (error) throw error;

      // Count tags
      const tagCounts: Record<string, number> = {};
      const tagColors: Record<string, string> = {};

      tagStats?.forEach((candidate) => {
        if (candidate.normalized_tags) {
          candidate.normalized_tags.forEach((tag: any) => {
            if (tag.name) {
              tagCounts[tag.name] = (tagCounts[tag.name] || 0) + 1;
              tagColors[tag.name] = tag.color || "#3b82f6";
            }
          });
        }
      });

      // Create stats records
      const statsData: CreateCandidateTagsStatsData[] = Object.keys(
        tagCounts,
      ).map((tagName) => ({
        user_id: userId,
        tag_name: tagName,
        candidate_count: tagCounts[tagName],
        color: tagColors[tagName],
      }));

      // Insert stats
      const createdStats: CandidateTagsStatsData[] = [];
      for (const statData of statsData) {
        try {
          const created = await this.createCandidateTagsStats(statData);
          createdStats.push(created);
        } catch (error) {
          console.error(`Error creating tag stats for ${statData.tag_name}:`, error);
        }
      }

      return createdStats;
    } catch (error) {
      console.error("Error initializing tags data:", error);
      return [];
    }
  }
} 