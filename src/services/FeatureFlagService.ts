import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { workflowLogger, LogLevel } from "./WorkflowLogger";

export interface FeatureFlag {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  pilot_enabled: boolean;
  pilot_user_ids: string[];
  rollout_percentage: number;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface FeatureFlagAccess {
  user_id: string;
  flag_name: string;
  has_access: boolean;
  is_pilot_user: boolean;
  accessed_at: string;
}

class FeatureFlagService {
  private static instance: FeatureFlagService;
  private flags: Map<string, FeatureFlag> = new Map();
  private userAccessCache: Map<string, Map<string, boolean>> = new Map();
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes
  private lastFetch: number = 0;

  /**
   * Feature flag names - class-level constant
   * Access via FeatureFlagService.FLAGS or the exported FLAGS constant
   */
  public static readonly FLAGS = {
    AI_WORKFLOWS: "ai_workflows",
    WORKFLOW_TEMPLATES: "workflow_templates",
    ADVANCED_ANALYTICS: "advanced_analytics",
    REALTIME_COLLABORATION: "realtime_collaboration",
    CUSTOM_INTEGRATIONS: "custom_integrations",
    WORKFLOW_SCHEDULER: "workflow_scheduler",
    AI_SUGGESTIONS: "ai_suggestions",
    PERFORMANCE_INSIGHTS: "performance_insights",
  };

  private constructor() {
    this.initializeFlags();
  }

  public static getInstance(): FeatureFlagService {
    if (!FeatureFlagService.instance) {
      FeatureFlagService.instance = new FeatureFlagService();
    }
    return FeatureFlagService.instance;
  }

  private async initializeFlags() {
    await this.refreshFlags();
    // Set up realtime subscription for flag updates
    this.subscribeToFlagUpdates();
  }

  private async refreshFlags() {
    try {
      const { data, error } = await supabase.from("feature_flags").select("*");

      if (error) throw error;

      this.flags.clear();
      data?.forEach((flag) => {
        this.flags.set(flag.name, flag);
      });

      this.lastFetch = Date.now();

      await workflowLogger.log({
        level: LogLevel.INFO,
        message: `Loaded ${this.flags.size} feature flags`,
        metadata: { flags: Array.from(this.flags.keys()) },
      });
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to load feature flags",
        error: error as Error,
      });
    }
  }

  private subscribeToFlagUpdates() {
    supabase
      .channel("feature_flags_changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "feature_flags" },
        async (payload) => {
          await workflowLogger.log({
            level: LogLevel.INFO,
            message: "Feature flag updated",
            metadata: { flag: payload.new },
          });

          // Clear caches and refresh
          this.userAccessCache.clear();
          await this.refreshFlags();
        },
      )
      .subscribe();
  }

  public async isEnabled(flagName: string, userId?: string): Promise<boolean> {
    // Check if flags need refresh
    if (Date.now() - this.lastFetch > this.cacheTimeout) {
      await this.refreshFlags();
    }

    const flag = this.flags.get(flagName);
    if (!flag) {
      return false;
    }

    // If no user ID provided, check general availability
    if (!userId) {
      return flag.enabled;
    }

    // Check user access cache
    if (this.userAccessCache.has(userId)) {
      const userCache = this.userAccessCache.get(userId)!;
      if (userCache.has(flagName)) {
        return userCache.get(flagName)!;
      }
    }

    // Determine access
    let hasAccess = false;

    // Check if globally enabled
    if (flag.enabled) {
      hasAccess = true;
    }
    // Check if user is in pilot group
    else if (flag.pilot_enabled && flag.pilot_user_ids.includes(userId)) {
      hasAccess = true;
    }
    // Check rollout percentage
    else if (flag.rollout_percentage > 0) {
      const userHash = this.hashUserId(userId);
      hasAccess = userHash % 100 < flag.rollout_percentage;
    }

    // Cache the result
    if (!this.userAccessCache.has(userId)) {
      this.userAccessCache.set(userId, new Map());
    }
    this.userAccessCache.get(userId)!.set(flagName, hasAccess);

    // Log access
    await this.logFeatureAccess(
      userId,
      flagName,
      hasAccess,
      flag.pilot_user_ids.includes(userId),
    );

    return hasAccess;
  }

  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private async logFeatureAccess(
    userId: string,
    flagName: string,
    hasAccess: boolean,
    isPilotUser: boolean,
  ) {
    try {
      await supabase.from("feature_flag_access_logs").insert({
        user_id: userId,
        flag_name: flagName,
        has_access: hasAccess,
        is_pilot_user: isPilotUser,
        accessed_at: new Date().toISOString(),
      });
    } catch (error) {
      // Don't fail the access check if logging fails (non-critical)
    }
  }

  public async addPilotUser(
    flagName: string,
    userId: string,
  ): Promise<boolean> {
    try {
      const flag = this.flags.get(flagName);
      if (!flag) {
        throw new Error(`Feature flag ${flagName} not found`);
      }

      const updatedPilotUsers = [...new Set([...flag.pilot_user_ids, userId])];

      const { error } = await supabase
        .from("feature_flags")
        .update({
          pilot_user_ids: updatedPilotUsers,
          updated_at: new Date().toISOString(),
        })
        .eq("name", flagName);

      if (error) throw error;

      // Clear user cache
      this.userAccessCache.delete(userId);

      await workflowLogger.log({
        level: LogLevel.INFO,
        message: `Added pilot user to feature flag`,
        metadata: { flagName, userId },
      });

      return true;
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to add pilot user",
        error: error as Error,
        metadata: { flagName, userId },
      });
      return false;
    }
  }

  public async removePilotUser(
    flagName: string,
    userId: string,
  ): Promise<boolean> {
    try {
      const flag = this.flags.get(flagName);
      if (!flag) {
        throw new Error(`Feature flag ${flagName} not found`);
      }

      const updatedPilotUsers = flag.pilot_user_ids.filter(
        (id) => id !== userId,
      );

      const { error } = await supabase
        .from("feature_flags")
        .update({
          pilot_user_ids: updatedPilotUsers,
          updated_at: new Date().toISOString(),
        })
        .eq("name", flagName);

      if (error) throw error;

      // Clear user cache
      this.userAccessCache.delete(userId);

      await workflowLogger.log({
        level: LogLevel.INFO,
        message: `Removed pilot user from feature flag`,
        metadata: { flagName, userId },
      });

      return true;
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to remove pilot user",
        error: error as Error,
        metadata: { flagName, userId },
      });
      return false;
    }
  }

  public async enablePilot(flagName: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("feature_flags")
        .update({
          pilot_enabled: true,
          updated_at: new Date().toISOString(),
        })
        .eq("name", flagName);

      if (error) throw error;

      await workflowLogger.log({
        level: LogLevel.INFO,
        message: `Enabled pilot for feature flag`,
        metadata: { flagName },
      });

      return true;
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to enable pilot",
        error: error as Error,
        metadata: { flagName },
      });
      return false;
    }
  }

  public async setRolloutPercentage(
    flagName: string,
    percentage: number,
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("feature_flags")
        .update({
          rollout_percentage: Math.max(0, Math.min(100, percentage)),
          updated_at: new Date().toISOString(),
        })
        .eq("name", flagName);

      if (error) throw error;

      // Clear all user caches as rollout affects everyone
      this.userAccessCache.clear();

      await workflowLogger.log({
        level: LogLevel.INFO,
        message: `Updated rollout percentage for feature flag`,
        metadata: { flagName, percentage },
      });

      return true;
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to set rollout percentage",
        error: error as Error,
        metadata: { flagName, percentage },
      });
      return false;
    }
  }

  public async getFeatureUsageStats(flagName: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from("feature_flag_access_logs")
        .select("*")
        .eq("flag_name", flagName)
        .gte(
          "accessed_at",
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        );

      if (error) throw error;

      const stats = {
        totalAccesses: data?.length || 0,
        uniqueUsers: new Set(data?.map((log) => log.user_id)).size,
        pilotUsers: data?.filter((log) => log.is_pilot_user).length || 0,
        regularUsers: data?.filter((log) => !log.is_pilot_user).length || 0,
        accessRate:
          data?.filter((log) => log.has_access).length ||
          0 / (data?.length || 1),
        dailyAccess: this.groupByDay(data || []),
      };

      return stats;
    } catch (error) {
      await workflowLogger.log({
        level: LogLevel.ERROR,
        message: "Failed to get feature usage stats",
        error: error as Error,
        metadata: { flagName },
      });
      return null;
    }
  }

  private groupByDay(logs: FeatureFlagAccess[]): Record<string, number> {
    const grouped: Record<string, number> = {};

    logs.forEach((log) => {
      const day = new Date(log.accessed_at).toISOString().split("T")[0];
      grouped[day] = (grouped[day] || 0) + 1;
    });

    return grouped;
  }

  public getAllFlags(): FeatureFlag[] {
    return Array.from(this.flags.values());
  }

  public clearCache(userId?: string) {
    if (userId) {
      this.userAccessCache.delete(userId);
    } else {
      this.userAccessCache.clear();
    }
  }
}

// Export singleton instance
export const featureFlagService = FeatureFlagService.getInstance();

// Export FLAGS for easy access by consumers
// Note: FLAGS is a class-level constant, not instance-level
export const FLAGS = FeatureFlagService.FLAGS;

// Optimized hook for React components with real-time flag updates
export function useFeatureFlag(flagName: string): boolean {
  const [isEnabled, setIsEnabled] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let mounted = true;

    const checkFlag = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user && mounted) {
        const enabled = await featureFlagService.isEnabled(flagName, user.id);
        setIsEnabled(enabled);
      }
      if (mounted) {
        setLoading(false);
      }
    };

    checkFlag();

    // Subscribe to auth changes
    const {
      data: { subscription: authSubscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      if (session?.user) {
        const enabled = await featureFlagService.isEnabled(
          flagName,
          session.user.id,
        );
        setIsEnabled(enabled);
      } else {
        setIsEnabled(false);
      }
    });

    // Subscribe to real-time feature flag changes
    const flagSubscription = supabase
      .channel(`feature_flag_${flagName}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "feature_flags",
          filter: `name=eq.${flagName}`,
        },
        async (payload) => {
          if (!mounted) return;

          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (user) {
            // Clear cache and re-check flag
            featureFlagService.clearCache(user.id);
            const enabled = await featureFlagService.isEnabled(
              flagName,
              user.id,
            );
            setIsEnabled(enabled);
          }
        },
      )
      .subscribe();

    return () => {
      mounted = false;
      authSubscription.unsubscribe();
      flagSubscription.unsubscribe();
    };
  }, [flagName]);

  return loading ? false : isEnabled;
}
