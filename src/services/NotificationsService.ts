import { supabase } from "@/integrations/supabase/client";

export interface NotificationData {
  id: string;
  user_id: string;
  type: "interview" | "message" | "candidate" | "deadline" | "system";
  title: string;
  message: string;
  read: boolean;
  metadata: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface CreateNotificationData {
  user_id: string;
  type: "interview" | "message" | "candidate" | "deadline" | "system";
  title: string;
  message: string;
  metadata?: Record<string, unknown>;
}

export interface UpdateNotificationData {
  id: string;
  read?: boolean;
  metadata?: Record<string, unknown>;
}

export class NotificationsService {
  static async getNotifications(
    userId: string,
    limit = 50,
  ): Promise<NotificationData[]> {
    const { data, error } = await supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching notifications:", error);
      throw error;
    }

    return data || [];
  }

  static async getUnreadNotifications(
    userId: string,
  ): Promise<NotificationData[]> {
    const { data, error } = await supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .eq("read", false)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching unread notifications:", error);
      throw error;
    }

    return data || [];
  }

  static async createNotification(
    notificationData: CreateNotificationData,
  ): Promise<NotificationData> {
    const { data, error } = await supabase
      .from("notifications")
      .insert([
        {
          ...notificationData,
          metadata: notificationData.metadata || {},
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating notification:", error);
      throw error;
    }

    return data;
  }

  static async updateNotification(
    notificationData: UpdateNotificationData,
  ): Promise<NotificationData> {
    const { id, ...updateData } = notificationData;

    const { data, error } = await supabase
      .from("notifications")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating notification:", error);
      throw error;
    }

    return data;
  }

  static async markAsRead(notificationId: string): Promise<NotificationData> {
    return this.updateNotification({
      id: notificationId,
      read: true,
    });
  }

  static async markAllAsRead(userId: string): Promise<void> {
    const { error } = await supabase
      .from("notifications")
      .update({ read: true })
      .eq("user_id", userId)
      .eq("read", false);

    if (error) {
      console.error("Error marking all notifications as read:", error);
      throw error;
    }
  }

  static async deleteNotification(id: string): Promise<void> {
    const { error } = await supabase
      .from("notifications")
      .delete()
      .eq("id", id);

    if (error) {
      console.error("Error deleting notification:", error);
      throw error;
    }
  }

  static async initializeDefaultNotifications(
    userId: string,
  ): Promise<NotificationData[]> {
    // Check if user already has notifications
    const existing = await this.getNotifications(userId, 5);
    if (existing.length > 0) {
      return existing;
    }

    // Create sample notifications
    const defaultNotifications: CreateNotificationData[] = [
      {
        user_id: userId,
        type: "interview",
        title: "Upcoming Interview",
        message: "Interview with John Doe scheduled for today at 2:00 PM",
        metadata: { candidateId: "sample-candidate-1" },
      },
      {
        user_id: userId,
        type: "message",
        title: "New Message",
        message: "Sarah Johnson sent you a message about the Frontend position",
        metadata: { senderId: "sample-user-1" },
      },
      {
        user_id: userId,
        type: "candidate",
        title: "New Application",
        message: "Michael Chen applied for Senior React Developer position",
        metadata: { candidateId: "sample-candidate-2", jobId: "sample-job-1" },
      },
      {
        user_id: userId,
        type: "deadline",
        title: "Application Deadline",
        message: "Frontend Developer position closes in 3 days",
        metadata: { jobId: "sample-job-2" },
      },
    ];

    const createdNotifications = await Promise.all(
      defaultNotifications.map((notification) =>
        this.createNotification(notification),
      ),
    );

    return createdNotifications;
  }

  static async getNotificationsByType(
    userId: string,
    type: string,
  ): Promise<NotificationData[]> {
    const { data, error } = await supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .eq("type", type)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching notifications by type:", error);
      throw error;
    }

    return data || [];
  }

  static async getUnreadCount(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from("notifications")
      .select("*", { count: "exact", head: true })
      .eq("user_id", userId)
      .eq("read", false);

    if (error) {
      console.error("Error getting unread count:", error);
      throw error;
    }

    return count || 0;
  }
}
