import { supabase } from "@/integrations/supabase/client";

export interface RetentionData {
  id: string;
  user_id: string;
  department: string;
  score: number;
  risk_level: "Low" | "Medium" | "High";
  created_at: string;
  updated_at: string;
}

export interface CreateRetentionData {
  user_id: string;
  department: string;
  score: number;
  risk_level: "Low" | "Medium" | "High";
}

export interface UpdateRetentionData {
  id: string;
  department?: string;
  score?: number;
  risk_level?: "Low" | "Medium" | "High";
}

export class RetentionService {
  static async getRetentionPredictions(
    userId: string,
  ): Promise<RetentionData[]> {
    const { data, error } = await supabase
      .from("retention_predictions")
      .select("*")
      .eq("user_id", userId)
      .order("score", { ascending: true }); // Show highest risk first

    if (error) throw error;
    return data || [];
  }

  static async createRetentionPrediction(
    retentionData: CreateRetentionData,
  ): Promise<RetentionData> {
    const { data, error } = await supabase
      .from("retention_predictions")
      .insert([retentionData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateRetentionPrediction(
    updateData: UpdateRetentionData,
  ): Promise<RetentionData> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabase
      .from("retention_predictions")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteRetentionPrediction(id: string): Promise<void> {
    const { error } = await supabase
      .from("retention_predictions")
      .delete()
      .eq("id", id);

    if (error) throw error;
  }

  static async getHighRiskDepartments(
    userId: string,
  ): Promise<RetentionData[]> {
    const { data, error } = await supabase
      .from("retention_predictions")
      .select("*")
      .eq("user_id", userId)
      .eq("risk_level", "High")
      .order("score", { ascending: true });

    if (error) throw error;
    return data || [];
  }
}
