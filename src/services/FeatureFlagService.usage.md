# FeatureFlagService FLAGS Usage Guide

## Overview

The `FLAGS` constant in FeatureFlagService is a **class-level** constant that provides centralized feature flag names. It is not instance-level, meaning you don't need an instance of the service to access it.

## Usage Options

### Option A: Using the Exported FLAGS Constant (Recommended)

This approach has the least churn and is the preferred method:

```typescript
// Import from barrel export
import { useFeatureFlag, FLAGS } from '@/services';

// Usage in component
const MyComponent = () => {
  const hasAIWorkflows = useFeatureFlag(FLAGS.AI_WORKFLOWS);
  const hasTemplates = useFeatureFlag(FLAGS.WORKFLOW_TEMPLATES);

  if (!hasAIWorkflows) {
    return <div>AI Workflows not available</div>;
  }

  // ... rest of component
};
```

### Option B: Direct Class Reference

You can also import the class and reference FLAGS directly:

```typescript
// Import the class
import { FeatureFlagService, useFeatureFlag } from "@/services";

// Usage in component
const MyComponent = () => {
  const hasAIWorkflows = useFeatureFlag(FeatureFlagService.FLAGS.AI_WORKFLOWS);

  // ... rest of component
};
```

## Available Feature Flags

- `FLAGS.AI_WORKFLOWS` - AI workflow functionality
- `FLAGS.WORKFLOW_TEMPLATES` - Workflow template features
- `FLAGS.ADVANCED_ANALYTICS` - Advanced analytics features
- `FLAGS.REALTIME_COLLABORATION` - Real-time collaboration features
- `FLAGS.CUSTOM_INTEGRATIONS` - Custom integration capabilities
- `FLAGS.WORKFLOW_SCHEDULER` - Workflow scheduling features
- `FLAGS.AI_SUGGESTIONS` - AI-powered suggestions
- `FLAGS.PERFORMANCE_INSIGHTS` - Performance insights and analytics

## Best Practices

1. **Always use the FLAGS constant** for feature flag names instead of hardcoding strings
2. **Import from the barrel export** (`@/services`) for cleaner imports
3. **Use the React hook** (`useFeatureFlag`) in components for automatic updates
4. **Check feature access early** in your component to avoid unnecessary processing

## Example: Conditional Feature Rendering

```typescript
import { useFeatureFlag, FLAGS } from '@/services';
import { AdvancedAnalytics } from '@/components/analytics';
import { BasicAnalytics } from '@/components/analytics/BasicAnalytics';

const AnalyticsPage = () => {
  const hasAdvancedAnalytics = useFeatureFlag(FLAGS.ADVANCED_ANALYTICS);

  return (
    <div>
      <h1>Analytics</h1>
      {hasAdvancedAnalytics ? (
        <AdvancedAnalytics />
      ) : (
        <BasicAnalytics />
      )}
    </div>
  );
};
```
