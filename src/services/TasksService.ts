/**
 * TasksService
 * Centralized service for all task-related database operations
 */

import { supabase } from "@/integrations/supabase/client";
import {
  Tables,
  TablesInsert,
  TablesUpdate,
} from "@/integrations/supabase/types";

export type Task = Tables<"tasks">;
export type CreateTaskData = TablesInsert<"tasks">;
export type UpdateTaskData = TablesUpdate<"tasks">;

export interface TaskFilters {
  status?: "pending" | "in-progress" | "completed";
  priority?: "low" | "medium" | "high";
  category?:
    | "recruitment"
    | "screening"
    | "interview"
    | "onboarding"
    | "general";
  assignee?: string;
  due_before?: string;
  due_after?: string;
}

export class TasksService {
  /**
   * Get all tasks for a user
   */
  static async getTasks(
    userId: string,
    filters?: TaskFilters,
  ): Promise<Task[]> {
    let query = supabase
      .from("tasks")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    // Apply filters
    if (filters?.status) {
      query = query.eq("status", filters.status);
    }

    if (filters?.priority) {
      query = query.eq("priority", filters.priority);
    }

    if (filters?.category) {
      query = query.eq("category", filters.category);
    }

    if (filters?.assignee) {
      query = query.ilike("assignee", `%${filters.assignee}%`);
    }

    if (filters?.due_before) {
      query = query.lte("due_date", filters.due_before);
    }

    if (filters?.due_after) {
      query = query.gte("due_date", filters.due_after);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching tasks:", error);
      throw new Error(`Failed to fetch tasks: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a single task by ID
   */
  static async getTask(taskId: string): Promise<Task | null> {
    const { data, error } = await supabase
      .from("tasks")
      .select("*")
      .eq("id", taskId)
      .single();

    if (error) {
      console.error("Error fetching task:", error);
      throw new Error(`Failed to fetch task: ${error.message}`);
    }

    return data;
  }

  /**
   * Create a new task
   */
  static async createTask(taskData: CreateTaskData): Promise<Task> {
    // Validate required fields
    if (!taskData.title?.trim()) {
      throw new Error("Task title is required");
    }

    if (!taskData.user_id) {
      throw new Error("User ID is required");
    }

    const { data, error } = await supabase
      .from("tasks")
      .insert({
        ...taskData,
        status: taskData.status || "pending",
        priority: taskData.priority || "medium",
        category: taskData.category || "general",
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating task:", error);
      throw new Error(`Failed to create task: ${error.message}`);
    }

    return data;
  }

  /**
   * Update an existing task
   */
  static async updateTask(
    taskId: string,
    updateData: UpdateTaskData,
  ): Promise<Task> {
    const { data, error } = await supabase
      .from("tasks")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", taskId)
      .select()
      .single();

    if (error) {
      console.error("Error updating task:", error);
      throw new Error(`Failed to update task: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete a task
   */
  static async deleteTask(taskId: string): Promise<void> {
    const { error } = await supabase.from("tasks").delete().eq("id", taskId);

    if (error) {
      console.error("Error deleting task:", error);
      throw new Error(`Failed to delete task: ${error.message}`);
    }
  }

  /**
   * Mark task as completed
   */
  static async completeTask(taskId: string): Promise<Task> {
    return this.updateTask(taskId, {
      status: "completed",
      updated_at: new Date().toISOString(),
    });
  }

  /**
   * Assign task to someone
   */
  static async assignTask(taskId: string, assignee: string): Promise<Task> {
    return this.updateTask(taskId, {
      assignee,
      updated_at: new Date().toISOString(),
    });
  }

  /**
   * Update task priority
   */
  static async updateTaskPriority(
    taskId: string,
    priority: "low" | "medium" | "high",
  ): Promise<Task> {
    return this.updateTask(taskId, {
      priority,
      updated_at: new Date().toISOString(),
    });
  }

  /**
   * Get overdue tasks
   */
  static async getOverdueTasks(userId: string): Promise<Task[]> {
    const now = new Date().toISOString();

    const { data, error } = await supabase
      .from("tasks")
      .select("*")
      .eq("user_id", userId)
      .neq("status", "completed")
      .not("due_date", "is", null)
      .lt("due_date", now)
      .order("due_date", { ascending: true });

    if (error) {
      console.error("Error fetching overdue tasks:", error);
      throw new Error(`Failed to fetch overdue tasks: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get tasks due today
   */
  static async getTasksDueToday(userId: string): Promise<Task[]> {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0)).toISOString();
    const endOfDay = new Date(today.setHours(23, 59, 59, 999)).toISOString();

    const { data, error } = await supabase
      .from("tasks")
      .select("*")
      .eq("user_id", userId)
      .neq("status", "completed")
      .gte("due_date", startOfDay)
      .lte("due_date", endOfDay)
      .order("priority", { ascending: false });

    if (error) {
      console.error("Error fetching tasks due today:", error);
      throw new Error(`Failed to fetch tasks due today: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get task statistics
   */
  static async getTaskStats(userId: string): Promise<{
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    overdue: number;
  }> {
    const [allTasks, overdueTasks] = await Promise.all([
      this.getTasks(userId),
      this.getOverdueTasks(userId),
    ]);

    const stats = {
      total: allTasks.length,
      pending: allTasks.filter((t) => t.status === "pending").length,
      inProgress: allTasks.filter((t) => t.status === "in-progress").length,
      completed: allTasks.filter((t) => t.status === "completed").length,
      overdue: overdueTasks.length,
    };

    return stats;
  }

  /**
   * Bulk update task status
   */
  static async bulkUpdateTaskStatus(
    taskIds: string[],
    status: "pending" | "in-progress" | "completed",
  ): Promise<void> {
    const { error } = await supabase
      .from("tasks")
      .update({
        status,
        updated_at: new Date().toISOString(),
      })
      .in("id", taskIds);

    if (error) {
      console.error("Error bulk updating tasks:", error);
      throw new Error(`Failed to bulk update tasks: ${error.message}`);
    }
  }

  /**
   * Create task from template (for common recruitment tasks)
   */
  static async createTaskFromTemplate(
    userId: string,
    templateName: string,
    candidateName?: string,
    jobTitle?: string,
  ): Promise<Task> {
    const templates: Record<string, Partial<CreateTaskData>> = {
      "initial-screening": {
        title: candidateName
          ? `Initial Screening: ${candidateName}`
          : "Initial Screening",
        description: `Review resume and conduct initial screening call`,
        category: "screening",
        priority: "high",
      },
      "schedule-interview": {
        title: candidateName
          ? `Schedule Interview: ${candidateName}`
          : "Schedule Interview",
        description: `Coordinate and schedule interview${jobTitle ? ` for ${jobTitle} position` : ""}`,
        category: "interview",
        priority: "medium",
      },
      "send-offer": {
        title: candidateName ? `Send Offer: ${candidateName}` : "Send Offer",
        description: `Prepare and send job offer letter`,
        category: "recruitment",
        priority: "high",
      },
      "onboard-candidate": {
        title: candidateName ? `Onboard: ${candidateName}` : "Onboard New Hire",
        description: `Complete onboarding process for new team member`,
        category: "onboarding",
        priority: "medium",
      },
    };

    const template = templates[templateName];
    if (!template) {
      throw new Error(`Unknown template: ${templateName}`);
    }

    return this.createTask({
      ...template,
      user_id: userId,
      title: template.title!,
    } as CreateTaskData);
  }
}
