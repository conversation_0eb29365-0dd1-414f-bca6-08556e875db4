# Service Modules

This directory contains centralized service modules that wrap all Supabase database operations. These services provide a clean, consistent API for interacting with the database tables.

## Services

### MessagingService

Handles all operations related to the `messages` table:

- `getMessages(userId)` - Get all messages for a user
- `getMessage(messageId)` - Get a single message
- `createMessage(messageData)` - Create a new message
- `updateMessage(messageId, updateData)` - Update an existing message
- `deleteMessage(messageId)` - Delete a message
- `markAsRead(messageId)` - Mark message as read
- `markAsUnread(messageId)` - Mark message as unread
- `archiveMessage(messageId)` - Archive a message
- `toggleStar(messageId, isStarred)` - Toggle star status
- `getMessageTemplate(category)` - Get message template by category
- `createWorkflowMessage(userId, content, additionalData?)` - Create a workflow-generated message

### EventsService

Handles all operations related to the `events` table:

- `getEvents(userId)` - Get all events for a user
- `getEventsByDateRange(userId, startDate, endDate)` - Get events within a date range
- `getEvent(eventId)` - Get a single event
- `createEvent(eventData)` - Create a new event
- `updateEvent(eventId, updateData)` - Update an existing event
- `deleteEvent(eventId)` - Delete an event
- `createInterviewEvent(...)` - Create an interview event with specific parameters
- `getUpcomingEvents(userId, limit?)` - Get upcoming events
- `getEventsByType(userId, eventType)` - Get events by type
- `getEventsByCategory(userId, category)` - Get events by category

### CandidatesService

Handles all operations related to the `candidates` table:

- `getCandidates(userId)` - Get all candidates for a user
- `getCandidate(candidateId)` - Get a single candidate
- `createCandidate(candidateData)` - Create a new candidate
- `updateCandidate(candidateId, updateData)` - Update an existing candidate
- `deleteCandidate(candidateId)` - Delete a candidate
- `addToPool(candidateId, poolName, tags?)` - Add candidate to talent pool

### ActivityService

Handles all operations related to the `candidate_activities` table:

- `createActivityEntry(entryData)` - Create an activity entry
- `getCandidateActivities(candidateId)` - Get activity entries for a candidate
- `getUserActivities(userId)` - Get activity entries for a user
- `updateActivityEntry(id, updates)` - Update an activity entry
- `deleteActivityEntry(id)` - Delete an activity entry
- `getActivitiesByType(candidateId, activityType)` - Get activities by type
- `getRecentActivities(candidateId, limit?)` - Get recent activities

## Usage

Import the services in your components or executors:

```typescript
import {
  MessagingService,
  EventsService,
  CandidatesService,
  ActivityService,
} from "@/services";

// Example: Get all messages for a user
const messages = await MessagingService.getMessages(userId);

// Example: Create a new candidate
const candidate = await CandidatesService.createCandidate({
  name: "John Doe",
  email: "<EMAIL>",
  role: "Software Engineer",
  user_id: userId,
});

// Example: Create an activity entry
await ActivityService.createActivityEntry({
  candidate_id: candidateId,
  user_id: userId,
  activity_type: "status_change",
  title: "Status Update",
  description: "Candidate moved to interview stage",
  metadata: {
    new_status: "interview",
    status: "completed",
  },
});
```

## Benefits

1. **Centralized Logic**: All database operations are in one place
2. **Consistent Error Handling**: Services handle errors consistently
3. **Type Safety**: All methods are strongly typed
4. **Reusability**: No duplicate code across executors and hooks
5. **Maintainability**: Easy to update database operations in one place
6. **Validation**: Built-in validation for required fields and business rules
