import { supabase } from "@/integrations/supabase/client";

export interface PipelineTrendsData {
  month: string;
  applied: number;
  phone_screen: number;
  interview: number;
  final_round: number;
  offer: number;
  hired: number;
}

export interface PipelineStageData {
  stage: string;
  candidate_count: number;
  month: string;
}

export interface CreatePipelineTrendsData {
  user_id: string;
  month: string;
  stage: string;
  candidate_count: number;
}

export class PipelineTrendsService {
  // Get pipeline trends data aggregated by month and stage
  static async getPipelineTrends(
    userId: string,
    year?: number,
  ): Promise<PipelineTrendsData[]> {
    const currentYear = year || new Date().getFullYear();

    const { data, error } = await supabase
      .from("pipeline_candidates")
      .select("stage, created_at")
      .eq("user_id", userId)
      .gte("created_at", `${currentYear}-01-01`)
      .lte("created_at", `${currentYear}-12-31`);

    if (error) throw error;

    // Aggregate data by month and stage
    const monthlyData = new Map<string, Map<string, number>>();
    
    data?.forEach((candidate) => {
      const month = new Date(candidate.created_at).toLocaleDateString('en-US', { month: 'short' });
      const stage = candidate.stage;
      
      if (!monthlyData.has(month)) {
        monthlyData.set(month, new Map());
      }
      
      const stageMap = monthlyData.get(month)!;
      stageMap.set(stage, (stageMap.get(stage) || 0) + 1);
    });

    // Convert to array format
    const result: PipelineTrendsData[] = [];
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    months.forEach(month => {
      const stageMap = monthlyData.get(month) || new Map();
      result.push({
        month,
        applied: stageMap.get('Applied') || 0,
        phone_screen: stageMap.get('Phone Screen') || 0,
        interview: stageMap.get('Interview') || 0,
        final_round: stageMap.get('Final Round') || 0,
        offer: stageMap.get('Offer') || 0,
        hired: stageMap.get('Hired') || 0,
      });
    });

    return result;
  }

  // Get pipeline stage distribution for current month
  static async getCurrentPipelineDistribution(
    userId: string,
  ): Promise<PipelineStageData[]> {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const { data, error } = await supabase
      .from("pipeline_candidates")
      .select("stage, created_at")
      .eq("user_id", userId)
      .gte("created_at", `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-01`)
      .lte("created_at", `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-31`);

    if (error) throw error;

    // Aggregate by stage
    const stageCounts = new Map<string, number>();
    data?.forEach((candidate) => {
      const stage = candidate.stage;
      stageCounts.set(stage, (stageCounts.get(stage) || 0) + 1);
    });

    return Array.from(stageCounts.entries()).map(([stage, count]) => ({
      stage,
      candidate_count: count,
      month: new Date().toLocaleDateString('en-US', { month: 'short' }),
    }));
  }

  // Get pipeline conversion rates
  static async getPipelineConversionRates(
    userId: string,
  ): Promise<{ stage: string; conversion_rate: number }[]> {
    const { data, error } = await supabase
      .from("pipeline_candidates")
      .select("stage")
      .eq("user_id", userId);

    if (error) throw error;

    const stageCounts = new Map<string, number>();
    data?.forEach((candidate) => {
      const stage = candidate.stage;
      stageCounts.set(stage, (stageCounts.get(stage) || 0) + 1);
    });

    const totalCandidates = data?.length || 0;
    const conversionRates: { stage: string; conversion_rate: number }[] = [];

    stageCounts.forEach((count, stage) => {
      conversionRates.push({
        stage,
        conversion_rate: totalCandidates > 0 ? (count / totalCandidates) * 100 : 0,
      });
    });

    return conversionRates.sort((a, b) => b.conversion_rate - a.conversion_rate);
  }
} 