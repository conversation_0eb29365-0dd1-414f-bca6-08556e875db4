import { supabase } from "@/integrations/supabase/client";

export interface JobExpense {
  id: string;
  user_id: string;
  job_id: string;
  expense_type: 'job_posting' | 'agency_fee' | 'tool_subscription' | 'event_cost' | 'advertising' | 'other';
  description?: string;
  amount: number;
  expense_date: string;
  vendor?: string;
  is_recurring: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateJobExpenseData {
  user_id: string;
  job_id: string;
  expense_type: JobExpense['expense_type'];
  description?: string;
  amount: number;
  expense_date: string;
  vendor?: string;
  is_recurring?: boolean;
}

export interface UpdateJobExpenseData {
  id: string;
  expense_type?: JobExpense['expense_type'];
  description?: string;
  amount?: number;
  expense_date?: string;
  vendor?: string;
  is_recurring?: boolean;
}

export interface JobExpenseSummary {
  job_id: string;
  job_title: string;
  total_expenses: number;
  expense_breakdown: {
    expense_type: string;
    amount: number;
    count: number;
  }[];
}

export class JobExpenseService {
  /**
   * Get all job expenses for a user
   */
  static async getJobExpenses(userId: string): Promise<JobExpense[]> {
    const { data, error } = await supabase
      .from("job_expenses")
      .select("*")
      .eq("user_id", userId)
      .order("expense_date", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Get job expenses for a specific job
   */
  static async getJobExpensesByJob(userId: string, jobId: string): Promise<JobExpense[]> {
    const { data, error } = await supabase
      .from("job_expenses")
      .select("*")
      .eq("user_id", userId)
      .eq("job_id", jobId)
      .order("expense_date", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Create a new job expense
   */
  static async createJobExpense(expenseData: CreateJobExpenseData): Promise<JobExpense> {
    const { data, error } = await supabase
      .from("job_expenses")
      .insert([expenseData])
      .select()
      .single();

    if (error) throw error;

    // Trigger budget recalculation
    await this.triggerBudgetRecalculation(expenseData.user_id);

    return data;
  }

  /**
   * Update a job expense
   */
  static async updateJobExpense(updateData: UpdateJobExpenseData): Promise<JobExpense> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabase
      .from("job_expenses")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    // Get user_id for budget recalculation
    const { data: expense } = await supabase
      .from("job_expenses")
      .select("user_id")
      .eq("id", id)
      .single();

    if (expense?.user_id) {
      await this.triggerBudgetRecalculation(expense.user_id);
    }

    return data;
  }

  /**
   * Delete a job expense
   */
  static async deleteJobExpense(id: string): Promise<void> {
    // Get user_id before deletion for budget recalculation
    const { data: expense } = await supabase
      .from("job_expenses")
      .select("user_id")
      .eq("id", id)
      .single();

    const { error } = await supabase
      .from("job_expenses")
      .delete()
      .eq("id", id);

    if (error) throw error;

    if (expense?.user_id) {
      await this.triggerBudgetRecalculation(expense.user_id);
    }
  }

  /**
   * Get expense summary by job
   */
  static async getJobExpenseSummary(userId: string): Promise<JobExpenseSummary[]> {
    const { data, error } = await supabase
      .from("job_expenses")
      .select(`
        job_id,
        expense_type,
        amount,
        jobs!inner(title)
      `)
      .eq("user_id", userId);

    if (error) throw error;

    // Group by job and calculate summaries
    const summaryMap = new Map<string, JobExpenseSummary>();

    data?.forEach((expense: any) => {
      const jobId = expense.job_id;
      const jobTitle = expense.jobs.title;
      const amount = parseFloat(expense.amount) || 0; // Ensure numeric conversion

      if (!summaryMap.has(jobId)) {
        summaryMap.set(jobId, {
          job_id: jobId,
          job_title: jobTitle,
          total_expenses: 0,
          expense_breakdown: [],
        });
      }

      const summary = summaryMap.get(jobId)!;
      summary.total_expenses += amount; // Use converted numeric amount

      // Update expense breakdown
      const existingBreakdown = summary.expense_breakdown.find(
        (b) => b.expense_type === expense.expense_type
      );

      if (existingBreakdown) {
        existingBreakdown.amount += amount; // Use converted numeric amount
        existingBreakdown.count += 1;
      } else {
        summary.expense_breakdown.push({
          expense_type: expense.expense_type,
          amount: amount, // Use converted numeric amount
          count: 1,
        });
      }
    });

    // Sort by total expenses descending to get top spending jobs first
    return Array.from(summaryMap.values()).sort((a, b) => b.total_expenses - a.total_expenses);
  }

  /**
   * Trigger budget recalculation after expense changes
   */
  private static async triggerBudgetRecalculation(userId: string): Promise<void> {
    try {
      await supabase.rpc("recalculate_budget_data", { p_user_id: userId });
    } catch (error) {
      console.error("Error triggering budget recalculation:", error);
      // Don't throw - this is a background operation
    }
  }

  /**
   * Get expense totals by category for budget tracking
   */
  static async getExpenseTotalsByCategory(userId: string): Promise<{
    category: string;
    total_amount: number;
    expense_count: number;
  }[]> {
    const { data, error } = await supabase
      .from("job_expenses")
      .select("expense_type, amount")
      .eq("user_id", userId);

    if (error) throw error;

    // Group by category
    const categoryMap = new Map<string, { total_amount: number; expense_count: number }>();

    data?.forEach((expense) => {
      const category = this.mapExpenseTypeToCategory(expense.expense_type);
      const amount = parseFloat(expense.amount) || 0; // Ensure numeric conversion

      if (!categoryMap.has(category)) {
        categoryMap.set(category, { total_amount: 0, expense_count: 0 });
      }

      const categoryData = categoryMap.get(category)!;
      categoryData.total_amount += amount; // Use converted numeric amount
      categoryData.expense_count += 1;
    });

    return Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      total_amount: data.total_amount,
      expense_count: data.expense_count,
    }));
  }

  /**
   * Map expense types to budget categories
   */
  private static mapExpenseTypeToCategory(expenseType: string): string {
    switch (expenseType) {
      case 'job_posting':
        return 'Job Postings';
      case 'agency_fee':
        return 'Agency Fees';
      case 'tool_subscription':
      case 'advertising':
        return 'Tools & Software';
      case 'event_cost':
        return 'Events';
      default:
        return 'Other';
    }
  }
}
