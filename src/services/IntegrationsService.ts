import { supabase } from "@/integrations/supabase/client";

export interface IntegrationData {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  category: "communication" | "calendar" | "database" | "analytics" | "other";
  status: "connected" | "available" | "coming_soon";
  settings: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface CreateIntegrationData {
  name: string;
  description?: string;
  category: "communication" | "calendar" | "database" | "analytics" | "other";
  status: "connected" | "available" | "coming_soon";
  settings?: Record<string, unknown>;
  user_id: string;
}

export interface UpdateIntegrationData {
  id: string;
  name?: string;
  description?: string;
  status?: "connected" | "available" | "coming_soon";
  settings?: Record<string, unknown>;
}

export class IntegrationsService {
  static async getIntegrations(userId: string): Promise<IntegrationData[]> {
    const { data, error } = await supabase
      .from("integrations")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createIntegration(
    integrationData: CreateIntegrationData,
  ): Promise<IntegrationData> {
    const { data, error } = await supabase
      .from("integrations")
      .insert([integrationData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateIntegration(
    updateData: UpdateIntegrationData,
  ): Promise<IntegrationData> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabase
      .from("integrations")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteIntegration(id: string): Promise<void> {
    const { error } = await supabase.from("integrations").delete().eq("id", id);

    if (error) throw error;
  }

  static async getIntegrationsByCategory(
    userId: string,
    category: string,
  ): Promise<IntegrationData[]> {
    const { data, error } = await supabase
      .from("integrations")
      .select("*")
      .eq("user_id", userId)
      .eq("category", category)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getConnectedIntegrations(
    userId: string,
  ): Promise<IntegrationData[]> {
    const { data, error } = await supabase
      .from("integrations")
      .select("*")
      .eq("user_id", userId)
      .eq("status", "connected")
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Initialize default integrations for a user
   */
  static async initializeDefaultIntegrations(userId: string): Promise<void> {
    const defaultIntegrations = [
      {
        name: "Slack",
        description: "Send notifications and updates to Slack channels",
        category: "communication" as const,
        status: "available" as const,
        settings: {},
      },
      {
        name: "Microsoft Teams",
        description: "Integrate with Microsoft Teams for notifications",
        category: "communication" as const,
        status: "available" as const,
        settings: {},
      },
      {
        name: "Google Calendar",
        description: "Sync interviews and events with Google Calendar",
        category: "calendar" as const,
        status: "available" as const,
        settings: {},
      },
      {
        name: "Outlook Calendar",
        description: "Sync interviews and events with Outlook Calendar",
        category: "calendar" as const,
        status: "available" as const,
        settings: {},
      },
      {
        name: "Zapier",
        description: "Connect with 3000+ apps through Zapier automation",
        category: "other" as const,
        status: "available" as const,
        settings: {},
      },
      {
        name: "Webhooks",
        description: "Send real-time data to external systems via webhooks",
        category: "other" as const,
        status: "available" as const,
        settings: {},
      },
      {
        name: "Google Analytics",
        description: "Track job posting performance and candidate sources",
        category: "analytics" as const,
        status: "available" as const,
        settings: {},
      },
      {
        name: "Salesforce",
        description: "Sync candidate data with Salesforce CRM",
        category: "database" as const,
        status: "available" as const,
        settings: {},
      },
    ];

    // Check which integrations already exist
    const existingIntegrations = await this.getIntegrations(userId);
    const existingNames = existingIntegrations.map((i) => i.name);

    // Create only new integrations
    const newIntegrations = defaultIntegrations
      .filter((integration) => !existingNames.includes(integration.name))
      .map((integration) => ({
        ...integration,
        user_id: userId,
      }));

    if (newIntegrations.length > 0) {
      const { error } = await supabase
        .from("integrations")
        .insert(newIntegrations);

      if (error) throw error;
    }
  }

  /**
   * Connect an integration (change status to connected)
   */
  static async connectIntegration(
    userId: string,
    integrationName: string,
    settings: Record<string, unknown>,
  ): Promise<void> {
    const { error } = await supabase
      .from("integrations")
      .update({
        status: "connected",
        settings,
        updated_at: new Date().toISOString(),
      })
      .eq("user_id", userId)
      .eq("name", integrationName);

    if (error) throw error;
  }

  /**
   * Disconnect an integration (change status to available)
   */
  static async disconnectIntegration(
    userId: string,
    integrationName: string,
  ): Promise<void> {
    const { error } = await supabase
      .from("integrations")
      .update({
        status: "available",
        settings: {},
        updated_at: new Date().toISOString(),
      })
      .eq("user_id", userId)
      .eq("name", integrationName);

    if (error) throw error;
  }
}
