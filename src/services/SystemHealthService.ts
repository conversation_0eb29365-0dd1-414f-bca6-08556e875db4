import { supabase } from "@/integrations/supabase/client";

export interface SystemHealthData {
  id: string;
  user_id: string;
  name: string;
  status: "operational" | "degraded" | "down";
  uptime: number;
  response_time: number;
  last_check: string;
  created_at: string;
  updated_at: string;
}

export interface CreateSystemHealthData {
  name: string;
  status: "operational" | "degraded" | "down";
  uptime: number;
  response_time: number;
  user_id: string;
}

export interface UpdateSystemHealthData {
  id: string;
  status?: "operational" | "degraded" | "down";
  uptime?: number;
  response_time?: number;
}

export class SystemHealthService {
  static async getSystemHealth(userId: string): Promise<SystemHealthData[]> {
    const { data, error } = await supabase
      .from("system_health")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching system health:", error);
      throw error;
    }

    return data || [];
  }

  static async createSystemHealth(
    healthData: CreateSystemHealthData,
  ): Promise<SystemHealthData> {
    const { data, error } = await supabase
      .from("system_health")
      .insert([healthData])
      .select()
      .single();

    if (error) {
      console.error("Error creating system health:", error);
      throw error;
    }

    return data;
  }

  static async updateSystemHealth(
    healthData: UpdateSystemHealthData,
  ): Promise<SystemHealthData> {
    const { id, ...updateData } = healthData;

    const { data, error } = await supabase
      .from("system_health")
      .update({
        ...updateData,
        last_check: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating system health:", error);
      throw error;
    }

    return data;
  }

  static async deleteSystemHealth(id: string): Promise<void> {
    const { error } = await supabase
      .from("system_health")
      .delete()
      .eq("id", id);

    if (error) {
      console.error("Error deleting system health:", error);
      throw error;
    }
  }

  static async initializeDefaultSystemHealth(
    userId: string,
  ): Promise<SystemHealthData[]> {
    // Check if user already has system health data
    const existing = await this.getSystemHealth(userId);
    if (existing.length > 0) {
      return existing;
    }

    // Create default system health entries
    const defaultSystems = [
      {
        name: "Web Application",
        status: "operational" as const,
        uptime: 99.9,
        response_time: 120,
        user_id: userId,
      },
      {
        name: "Database",
        status: "operational" as const,
        uptime: 99.8,
        response_time: 45,
        user_id: userId,
      },
      {
        name: "API Server",
        status: "operational" as const,
        uptime: 99.7,
        response_time: 85,
        user_id: userId,
      },
      {
        name: "Email Service",
        status: "degraded" as const,
        uptime: 98.5,
        response_time: 450,
        user_id: userId,
      },
    ];

    const { data, error } = await supabase
      .from("system_health")
      .insert(defaultSystems)
      .select();

    if (error) {
      console.error("Error initializing system health:", error);
      throw error;
    }

    return data || [];
  }

  static async updateSystemHealthMetrics(
    userId: string,
  ): Promise<SystemHealthData[]> {
    const systems = await this.getSystemHealth(userId);

    // Simulate real-time updates
    const updatedSystems = await Promise.all(
      systems.map(async (system) => {
        const responseTimeVariation = (Math.random() - 0.5) * 20;
        const newResponseTime = Math.max(
          10,
          system.response_time + responseTimeVariation,
        );

        return this.updateSystemHealth({
          id: system.id,
          response_time: Math.round(newResponseTime),
        });
      }),
    );

    return updatedSystems;
  }
}
