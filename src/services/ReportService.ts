/**
 * ReportService
 * Centralized service for all report-related database operations and file management
 */

import { supabase } from "@/integrations/supabase/client";

// Report Types
export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  template_config: Record<string, unknown>;
  query_template?: string;
  visualization_config?: Record<string, unknown>;
  created_by?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface ScheduledReport {
  id: string;
  name: string;
  description?: string;
  template_id?: string;
  schedule_config: {
    cron?: string;
    timezone?: string;
    [key: string]: unknown;
  };
  parameters?: Record<string, unknown>;
  recipients?: string[];
  output_format: "pdf" | "csv" | "excel" | "json";
  is_active: boolean;
  last_run_at?: string;
  next_run_at?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface GeneratedReport {
  id: string;
  scheduled_report_id?: string;
  template_id?: string;
  name: string;
  description?: string;
  parameters_used?: Record<string, unknown>;
  file_path?: string;
  file_size?: number;
  format: "pdf" | "csv" | "excel" | "json";
  status: "pending" | "generating" | "completed" | "failed" | "expired";
  error_message?: string;
  metadata?: {
    page_count?: number;
    row_count?: number;
    generation_time_ms?: number;
    [key: string]: unknown;
  };
  generated_by?: string;
  generated_at: string;
  expires_at?: string;
  accessed_at?: string;
  access_count: number;
}

// Data Transfer Objects
export interface CreateGeneratedReportData {
  name: string;
  description?: string;
  template_id?: string;
  scheduled_report_id?: string;
  format: "pdf" | "csv" | "excel" | "json";
  parameters_used?: Record<string, any>;
  generated_by: string;
}

export interface CreateScheduledReportData {
  name: string;
  description?: string;
  template_id?: string;
  schedule_config: {
    cron?: string;
    timezone?: string;
    [key: string]: any;
  };
  parameters?: Record<string, any>;
  recipients?: string[];
  output_format?: "pdf" | "csv" | "excel" | "json";
  created_by: string;
}

export interface UpdateScheduledReportData {
  name?: string;
  description?: string;
  template_id?: string;
  schedule_config?: {
    cron?: string;
    timezone?: string;
    [key: string]: any;
  };
  parameters?: Record<string, any>;
  recipients?: string[];
  output_format?: "pdf" | "csv" | "excel" | "json";
  is_active?: boolean;
  next_run_at?: string;
}

export interface UpdateGenerationStatusData {
  status: "pending" | "generating" | "completed" | "failed" | "expired";
  error_message?: string;
  file_path?: string;
  file_size?: number;
  metadata?: Record<string, any>;
}

export class ReportService {
  /**
   * Create a new generated report
   */
  static async createGeneratedReport(
    reportData: CreateGeneratedReportData,
  ): Promise<GeneratedReport> {
    const insertData = {
      ...reportData,
      status: "pending" as const,
      access_count: 0,
      generated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("generated_reports")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error("Error creating generated report:", error);
      throw new Error(`Failed to create generated report: ${error.message}`);
    }

    return data;
  }

  /**
   * Get all generated reports for a user
   */
  static async getGeneratedReports(userId: string): Promise<GeneratedReport[]> {
    const { data, error } = await supabase
      .from("generated_reports")
      .select("*")
      .eq("generated_by", userId)
      .order("generated_at", { ascending: false });

    if (error) {
      console.error("Error fetching generated reports:", error);
      throw new Error(`Failed to fetch generated reports: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a single report by ID
   */
  static async getReport(reportId: string): Promise<GeneratedReport | null> {
    // First get the current report
    const { data: currentReport, error: fetchError } = await supabase
      .from("generated_reports")
      .select("*")
      .eq("id", reportId)
      .single();

    if (fetchError) {
      console.error("Error fetching report:", fetchError);
      throw new Error(`Failed to fetch report: ${fetchError.message}`);
    }

    if (!currentReport) {
      return null;
    }

    // Update accessed_at and increment access_count
    const { error: updateError } = await supabase
      .from("generated_reports")
      .update({
        accessed_at: new Date().toISOString(),
        access_count: (currentReport.access_count || 0) + 1,
      })
      .eq("id", reportId);

    if (updateError) {
      console.error("Error updating report access:", updateError);
    }

    // Return the report with updated access count
    return {
      ...currentReport,
      access_count: (currentReport.access_count || 0) + 1,
      accessed_at: new Date().toISOString(),
    };
  }

  /**
   * Delete a generated report
   */
  static async deleteReport(reportId: string): Promise<void> {
    // First get the report to delete associated file
    const report = await this.getReport(reportId);

    if (report?.file_path) {
      // Delete file from storage
      const { error: storageError } = await supabase.storage
        .from("reports")
        .remove([report.file_path]);

      if (storageError) {
        console.error("Error deleting report file:", storageError);
      }
    }

    const { error } = await supabase
      .from("generated_reports")
      .delete()
      .eq("id", reportId);

    if (error) {
      console.error("Error deleting report:", error);
      throw new Error(`Failed to delete report: ${error.message}`);
    }
  }

  /**
   * Create a new scheduled report
   */
  static async createScheduledReport(
    reportData: CreateScheduledReportData,
  ): Promise<ScheduledReport> {
    const insertData = {
      ...reportData,
      output_format: reportData.output_format || "pdf",
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("scheduled_reports")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error("Error creating scheduled report:", error);
      throw new Error(`Failed to create scheduled report: ${error.message}`);
    }

    return data;
  }

  /**
   * Update an existing scheduled report
   */
  static async updateScheduledReport(
    reportId: string,
    updateData: UpdateScheduledReportData,
  ): Promise<ScheduledReport> {
    const { data, error } = await supabase
      .from("scheduled_reports")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", reportId)
      .select()
      .single();

    if (error) {
      console.error("Error updating scheduled report:", error);
      throw new Error(`Failed to update scheduled report: ${error.message}`);
    }

    return data;
  }

  /**
   * Get all scheduled reports for a user
   */
  static async getScheduledReports(userId: string): Promise<ScheduledReport[]> {
    const { data, error } = await supabase
      .from("scheduled_reports")
      .select("*")
      .eq("created_by", userId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching scheduled reports:", error);
      throw new Error(`Failed to fetch scheduled reports: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Update generation status of a report
   */
  static async updateGenerationStatus(
    reportId: string,
    statusData: UpdateGenerationStatusData,
  ): Promise<GeneratedReport> {
    const updateData: any = {
      status: statusData.status,
      error_message: statusData.error_message,
      file_path: statusData.file_path,
      file_size: statusData.file_size,
      metadata: statusData.metadata,
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    const { data, error } = await supabase
      .from("generated_reports")
      .update(updateData)
      .eq("id", reportId)
      .select()
      .single();

    if (error) {
      console.error("Error updating report status:", error);
      throw new Error(`Failed to update report status: ${error.message}`);
    }

    return data;
  }

  /**
   * Upload report file to Supabase Storage
   */
  static async uploadReportFile(
    userId: string,
    reportId: string,
    file: File | Blob,
    fileName: string,
  ): Promise<{ path: string; publicUrl: string }> {
    const filePath = `${userId}/${reportId}/${fileName}`;

    const { data, error } = await supabase.storage
      .from("reports")
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
      });

    if (error) {
      console.error("Error uploading report file:", error);
      throw new Error(`Failed to upload report file: ${error.message}`);
    }

    const {
      data: { publicUrl },
    } = supabase.storage.from("reports").getPublicUrl(filePath);

    return {
      path: data.path,
      publicUrl,
    };
  }

  /**
   * Get report file URL from Supabase Storage
   */
  static async getReportFileUrl(filePath: string): Promise<string> {
    const { data } = supabase.storage.from("reports").getPublicUrl(filePath);

    return data.publicUrl;
  }

  /**
   * Download report file from Supabase Storage
   */
  static async downloadReportFile(filePath: string): Promise<Blob> {
    const { data, error } = await supabase.storage
      .from("reports")
      .download(filePath);

    if (error) {
      console.error("Error downloading report file:", error);
      throw new Error(`Failed to download report file: ${error.message}`);
    }

    return data;
  }

  /**
   * Get expired reports (helper for cleanup jobs)
   */
  static async getExpiredReports(): Promise<GeneratedReport[]> {
    const { data, error } = await supabase
      .from("generated_reports")
      .select("*")
      .lt("expires_at", new Date().toISOString())
      .not("expires_at", "is", null);

    if (error) {
      console.error("Error fetching expired reports:", error);
      throw new Error(`Failed to fetch expired reports: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Delete expired reports and their files
   */
  static async cleanupExpiredReports(): Promise<number> {
    const expiredReports = await this.getExpiredReports();
    let deletedCount = 0;

    for (const report of expiredReports) {
      try {
        await this.deleteReport(report.id);
        deletedCount++;
      } catch (error) {
        console.error(`Failed to delete expired report ${report.id}:`, error);
      }
    }

    return deletedCount;
  }

  /**
   * Get scheduled report by ID
   */
  static async getScheduledReport(
    reportId: string,
  ): Promise<ScheduledReport | null> {
    const { data, error } = await supabase
      .from("scheduled_reports")
      .select("*")
      .eq("id", reportId)
      .single();

    if (error) {
      console.error("Error fetching scheduled report:", error);
      throw new Error(`Failed to fetch scheduled report: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete a scheduled report
   */
  static async deleteScheduledReport(reportId: string): Promise<void> {
    const { error } = await supabase
      .from("scheduled_reports")
      .delete()
      .eq("id", reportId);

    if (error) {
      console.error("Error deleting scheduled report:", error);
      throw new Error(`Failed to delete scheduled report: ${error.message}`);
    }
  }

  /**
   * Get reports by status
   */
  static async getReportsByStatus(
    userId: string,
    status: GeneratedReport["status"],
  ): Promise<GeneratedReport[]> {
    const { data, error } = await supabase
      .from("generated_reports")
      .select("*")
      .eq("generated_by", userId)
      .eq("status", status)
      .order("generated_at", { ascending: false });

    if (error) {
      console.error("Error fetching reports by status:", error);
      throw new Error(`Failed to fetch reports by status: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get active scheduled reports
   */
  static async getActiveScheduledReports(
    userId: string,
  ): Promise<ScheduledReport[]> {
    const { data, error } = await supabase
      .from("scheduled_reports")
      .select("*")
      .eq("created_by", userId)
      .eq("is_active", true)
      .order("next_run_at", { ascending: true });

    if (error) {
      console.error("Error fetching active scheduled reports:", error);
      throw new Error(
        `Failed to fetch active scheduled reports: ${error.message}`,
      );
    }

    return data || [];
  }

  /**
   * Update last run timestamp for scheduled report
   */
  static async updateScheduledReportLastRun(
    reportId: string,
    nextRunAt?: string,
  ): Promise<void> {
    const updateData: any = {
      last_run_at: new Date().toISOString(),
    };

    if (nextRunAt) {
      updateData.next_run_at = nextRunAt;
    }

    const { error } = await supabase
      .from("scheduled_reports")
      .update(updateData)
      .eq("id", reportId);

    if (error) {
      console.error("Error updating scheduled report last run:", error);
      throw new Error(
        `Failed to update scheduled report last run: ${error.message}`,
      );
    }
  }

  /**
   * Get reports generated from a specific template
   */
  static async getReportsByTemplate(
    templateId: string,
    userId: string,
  ): Promise<GeneratedReport[]> {
    const { data, error } = await supabase
      .from("generated_reports")
      .select("*")
      .eq("template_id", templateId)
      .eq("generated_by", userId)
      .order("generated_at", { ascending: false });

    if (error) {
      console.error("Error fetching reports by template:", error);
      throw new Error(`Failed to fetch reports by template: ${error.message}`);
    }

    return data || [];
  }
}
