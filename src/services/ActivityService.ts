/**
 * ActivityService
 * Centralized service for all candidate activity-related database operations
 */

import { supabase } from "@/integrations/supabase/client";

export interface ActivityEntry {
  id: string;
  candidate_id: string;
  user_id: string;
  activity_type: string;
  title: string;
  description?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface CreateActivityEntryData {
  candidate_id: string;
  user_id: string;
  activity_type: string;
  title: string;
  description?: string;
  metadata?: any;
}

export class ActivityService {
  /**
   * Create an activity entry
   */
  static async createActivityEntry(
    entryData: CreateActivityEntryData,
  ): Promise<ActivityEntry> {
    const { data, error } = await supabase
      .from("candidate_activities")
      .insert(entryData)
      .select()
      .single();

    if (error) {
      console.error("Error creating activity entry:", error);
      throw new Error(`Failed to create activity entry: ${error.message}`);
    }

    return data;
  }

  /**
   * Get activity entries for a candidate
   */
  static async getCandidateActivities(
    candidateId: string,
  ): Promise<ActivityEntry[]> {
    const { data, error } = await supabase
      .from("candidate_activities")
      .select("*")
      .eq("candidate_id", candidateId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching candidate activities:", error);
      throw new Error(`Failed to fetch candidate activities: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get activity entries for a user
   */
  static async getUserActivities(userId: string): Promise<ActivityEntry[]> {
    const { data, error } = await supabase
      .from("candidate_activities")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching user activities:", error);
      throw new Error(`Failed to fetch user activities: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Update an activity entry
   */
  static async updateActivityEntry(
    id: string,
    updates: Partial<CreateActivityEntryData>,
  ): Promise<ActivityEntry> {
    const { data, error } = await supabase
      .from("candidate_activities")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating activity entry:", error);
      throw new Error(`Failed to update activity entry: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete an activity entry
   */
  static async deleteActivityEntry(id: string): Promise<void> {
    const { error } = await supabase
      .from("candidate_activities")
      .delete()
      .eq("id", id);

    if (error) {
      console.error("Error deleting activity entry:", error);
      throw new Error(`Failed to delete activity entry: ${error.message}`);
    }
  }

  /**
   * Get activity entries by activity type
   */
  static async getActivitiesByType(
    candidateId: string,
    activityType: string,
  ): Promise<ActivityEntry[]> {
    const { data, error } = await supabase
      .from("candidate_activities")
      .select("*")
      .eq("candidate_id", candidateId)
      .eq("activity_type", activityType)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching activities by type:", error);
      throw new Error(`Failed to fetch activities by type: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get recent activity entries
   */
  static async getRecentActivities(
    candidateId: string,
    limit: number = 10,
  ): Promise<ActivityEntry[]> {
    const { data, error } = await supabase
      .from("candidate_activities")
      .select("*")
      .eq("candidate_id", candidateId)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching recent activities:", error);
      throw new Error(`Failed to fetch recent activities: ${error.message}`);
    }

    return data || [];
  }
} 