/**
 * ReportsService
 * Centralized service for all report-related database operations
 */

import { supabase } from "@/integrations/supabase/client";
import {
  Tables,
  TablesInsert,
  TablesUpdate,
  Json,
} from "@/integrations/supabase/types";

export type ReportTemplate = Tables<"report_templates">;
export type ScheduledReport = Tables<"scheduled_reports">;
export type GeneratedReport = Tables<"generated_reports">;

export type CreateReportTemplateData = TablesInsert<"report_templates">;
export type CreateScheduledReportData = TablesInsert<"scheduled_reports">;
export type UpdateReportTemplateData = TablesUpdate<"report_templates">;
export type UpdateScheduledReportData = TablesUpdate<"scheduled_reports">;

export interface ReportParameters {
  date_range?: {
    start: string;
    end: string;
  };
  department?: string;
  job_type?: string;
  candidate_status?: string;
  include_archived?: boolean;
  [key: string]: unknown;
}

export class ReportsService {
  /**
   * Get all report templates
   */
  static async getReportTemplates(userId: string): Promise<ReportTemplate[]> {
    const { data, error } = await supabase
      .from("report_templates")
      .select("*")
      .or(`created_by.eq.${userId},is_active.eq.true`)
      .eq("is_active", true)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching report templates:", error);
      throw new Error(`Failed to fetch report templates: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a specific report template
   */
  static async getReportTemplate(
    templateId: string,
  ): Promise<ReportTemplate | null> {
    const { data, error } = await supabase
      .from("report_templates")
      .select("*")
      .eq("id", templateId)
      .single();

    if (error) {
      console.error("Error fetching report template:", error);
      throw new Error(`Failed to fetch report template: ${error.message}`);
    }

    return data;
  }

  /**
   * Create a new report template
   */
  static async createReportTemplate(
    templateData: CreateReportTemplateData,
  ): Promise<ReportTemplate> {
    if (!templateData.name?.trim()) {
      throw new Error("Report template name is required");
    }

    if (!templateData.template_config) {
      throw new Error("Template configuration is required");
    }

    const { data, error } = await supabase
      .from("report_templates")
      .insert({
        ...templateData,
        is_active: templateData.is_active ?? true,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating report template:", error);
      throw new Error(`Failed to create report template: ${error.message}`);
    }

    return data;
  }

  /**
   * Update a report template
   */
  static async updateReportTemplate(
    templateId: string,
    updateData: UpdateReportTemplateData,
  ): Promise<ReportTemplate> {
    const { data, error } = await supabase
      .from("report_templates")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", templateId)
      .select()
      .single();

    if (error) {
      console.error("Error updating report template:", error);
      throw new Error(`Failed to update report template: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete a report template (soft delete)
   */
  static async deleteReportTemplate(templateId: string): Promise<void> {
    const { error } = await supabase
      .from("report_templates")
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq("id", templateId);

    if (error) {
      console.error("Error deleting report template:", error);
      throw new Error(`Failed to delete report template: ${error.message}`);
    }
  }

  /**
   * Generate a report from a template
   */
  static async generateReport(
    templateId: string,
    parameters?: ReportParameters,
    format: string = "pdf",
  ): Promise<string> {
    try {
      const { data, error } = await supabase.rpc("generate_report", {
        p_template_id: templateId,
        p_parameters: parameters || {},
        p_format: format,
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error generating report:", error);
      throw new Error(`Failed to generate report: ${error}`);
    }
  }

  /**
   * Get all scheduled reports for a user
   */
  static async getScheduledReports(userId: string): Promise<ScheduledReport[]> {
    const { data, error } = await supabase
      .from("scheduled_reports")
      .select("*")
      .eq("created_by", userId)
      .eq("is_active", true)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching scheduled reports:", error);
      throw new Error(`Failed to fetch scheduled reports: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Create a scheduled report
   */
  static async createScheduledReport(
    scheduleData: CreateScheduledReportData,
  ): Promise<ScheduledReport> {
    if (!scheduleData.name?.trim()) {
      throw new Error("Scheduled report name is required");
    }

    if (!scheduleData.template_id) {
      throw new Error("Template ID is required");
    }

    if (!scheduleData.schedule_config) {
      throw new Error("Schedule configuration is required");
    }

    const { data, error } = await supabase
      .from("scheduled_reports")
      .insert({
        ...scheduleData,
        is_active: scheduleData.is_active ?? true,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating scheduled report:", error);
      throw new Error(`Failed to create scheduled report: ${error.message}`);
    }

    return data;
  }

  /**
   * Update a scheduled report
   */
  static async updateScheduledReport(
    scheduleId: string,
    updateData: UpdateScheduledReportData,
  ): Promise<ScheduledReport> {
    const { data, error } = await supabase
      .from("scheduled_reports")
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", scheduleId)
      .select()
      .single();

    if (error) {
      console.error("Error updating scheduled report:", error);
      throw new Error(`Failed to update scheduled report: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete a scheduled report (soft delete)
   */
  static async deleteScheduledReport(scheduleId: string): Promise<void> {
    const { error } = await supabase
      .from("scheduled_reports")
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq("id", scheduleId);

    if (error) {
      console.error("Error deleting scheduled report:", error);
      throw new Error(`Failed to delete scheduled report: ${error.message}`);
    }
  }

  /**
   * Get generated reports for a user
   */
  static async getGeneratedReports(
    userId: string,
    limit: number = 50,
  ): Promise<GeneratedReport[]> {
    const { data, error } = await supabase
      .from("generated_reports")
      .select("*")
      .eq("generated_by", userId)
      .order("generated_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching generated reports:", error);
      throw new Error(`Failed to fetch generated reports: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a specific generated report
   */
  static async getGeneratedReport(
    reportId: string,
  ): Promise<GeneratedReport | null> {
    const { data, error } = await supabase
      .from("generated_reports")
      .select("*")
      .eq("id", reportId)
      .single();

    if (error) {
      console.error("Error fetching generated report:", error);
      throw new Error(`Failed to fetch generated report: ${error.message}`);
    }

    return data;
  }

  /**
   * Get report access URL
   */
  static async getReportAccessUrl(reportId: string): Promise<string> {
    try {
      const { data, error } = await supabase.rpc("get_report_access_url", {
        report_id: reportId,
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error getting report access URL:", error);
      throw new Error(`Failed to get report access URL: ${error}`);
    }
  }

  /**
   * Track report access
   */
  static async trackReportAccess(reportId: string): Promise<void> {
    try {
      const { error } = await supabase.rpc("track_report_access", {
        p_report_id: reportId,
      });

      if (error) throw error;
    } catch (error) {
      console.error("Error tracking report access:", error);
      // Don't throw error for tracking as it's not critical
    }
  }

  /**
   * Schedule a report using the database function
   */
  static async scheduleReportWithFunction(
    name: string,
    templateId: string,
    cronExpression: string,
    parameters?: ReportParameters,
    recipients?: Array<{ email: string; name?: string }>,
    outputFormat: string = "pdf",
  ): Promise<string> {
    try {
      const { data, error } = await supabase.rpc("schedule_report", {
        p_name: name,
        p_template_id: templateId,
        p_cron_expression: cronExpression,
        p_parameters: parameters || {},
        p_recipients: recipients || [],
        p_output_format: outputFormat,
      });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error scheduling report:", error);
      throw new Error(`Failed to schedule report: ${error}`);
    }
  }

  /**
   * Get predefined report templates with configurations
   */
  static getPredefinedTemplates(): Array<{
    name: string;
    description: string;
    template_config: Json;
    visualization_config?: Json;
  }> {
    return [
      {
        name: "Candidate Pipeline Report",
        description:
          "Overview of candidates in different stages of the recruitment pipeline",
        template_config: {
          tables: ["candidates", "candidate_timeline"],
          metrics: ["total_candidates", "by_status", "conversion_rates"],
          groupBy: ["status", "department"],
          dateRange: true,
        },
        visualization_config: {
          charts: [
            { type: "funnel", field: "status" },
            { type: "bar", field: "department" },
          ],
        },
      },
      {
        name: "Hiring Performance Report",
        description: "Analysis of hiring metrics and time-to-hire statistics",
        template_config: {
          tables: ["jobs", "candidates", "candidate_interviews"],
          metrics: [
            "time_to_hire",
            "interview_success_rate",
            "offer_acceptance_rate",
          ],
          groupBy: ["department_name", "job_type_name"],
          dateRange: true,
        },
        visualization_config: {
          charts: [
            { type: "line", field: "time_to_hire" },
            { type: "gauge", field: "success_rate" },
          ],
        },
      },
      {
        name: "Recruiter Activity Report",
        description: "Summary of recruiter activities and performance",
        template_config: {
          tables: ["candidates", "messages", "events"],
          metrics: [
            "candidates_sourced",
            "interviews_scheduled",
            "response_time",
          ],
          groupBy: ["recruiter_name"],
          dateRange: true,
        },
      },
      {
        name: "Job Posting Analytics",
        description:
          "Performance analysis of job postings and application sources",
        template_config: {
          tables: ["jobs", "analytics_sources", "analytics_applications"],
          metrics: [
            "applications_per_job",
            "source_effectiveness",
            "job_fill_rate",
          ],
          groupBy: ["source", "job_type_name"],
          dateRange: true,
        },
      },
      {
        name: "Interview Summary Report",
        description:
          "Comprehensive overview of interview activities and outcomes",
        template_config: {
          tables: ["candidate_interviews", "candidates"],
          metrics: ["total_interviews", "success_rate", "feedback_scores"],
          groupBy: ["interview_type", "interviewer"],
          dateRange: true,
        },
      },
    ];
  }

  /**
   * Create predefined report templates
   */
  static async createPredefinedTemplates(
    userId: string,
  ): Promise<ReportTemplate[]> {
    const templates = this.getPredefinedTemplates();
    const createdTemplates: ReportTemplate[] = [];

    for (const template of templates) {
      try {
        const created = await this.createReportTemplate({
          ...template,
          created_by: userId,
          is_active: true,
        });
        createdTemplates.push(created);
      } catch (error) {
        console.error(`Failed to create template ${template.name}:`, error);
      }
    }

    return createdTemplates;
  }

  /**
   * Get all due scheduled reports (view)
   */
  static async getDueScheduledReports(): Promise<any[]> {
    const { data, error } = await supabase
      .from("due_scheduled_reports")
      .select("*");
    if (error) {
      console.error("Error fetching due scheduled reports:", error);
      throw new Error(
        `Failed to fetch due scheduled reports: ${error.message}`,
      );
    }
    return data || [];
  }

  /**
   * Get my report history (view)
   */
  static async getMyReportHistory(userId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from("my_report_history")
      .select("*");
    if (error) {
      console.error("Error fetching my report history:", error);
      throw new Error(`Failed to fetch my report history: ${error.message}`);
    }
    return data || [];
  }

  /**
   * Get report generation queue (view)
   */
  static async getReportGenerationQueue(): Promise<any[]> {
    const { data, error } = await supabase
      .from("report_generation_queue")
      .select(
        "id,name,format,status,generated_by,generated_at,template_name,query_template,parameters_used,user_email",
      );
    if (error) {
      console.error("Error fetching report generation queue:", error);
      throw new Error(
        `Failed to fetch report generation queue: ${error.message}`,
      );
    }
    return data || [];
  }
}
