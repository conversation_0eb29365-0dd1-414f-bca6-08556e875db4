import { supabase } from "@/integrations/supabase/client";

export interface BudgetAllocation {
  id: string;
  user_id: string;
  name: string;
  total_budget: number;
  budget_period_start: string;
  budget_period_end: string;
  department?: string;
  job_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateBudgetAllocationData {
  user_id: string;
  name: string;
  total_budget: number;
  budget_period_start: string;
  budget_period_end: string;
  department?: string;
  job_id?: string;
}

export interface UpdateBudgetAllocationData {
  id: string;
  name?: string;
  total_budget?: number;
  budget_period_start?: string;
  budget_period_end?: string;
  department?: string;
  job_id?: string;
  is_active?: boolean;
}

export interface BudgetSummaryData {
  allocation_id: string;
  budget_name: string;
  total_budget: number;
  spent_amount: number;
  remaining_amount: number;
  percentage_used: number;
  expense_count: number;
  department?: string;
  job_id?: string;
  budget_period_start: string;
  budget_period_end: string;
}

export class BudgetAllocationService {
  /**
   * Get all budget allocations for a user
   */
  static async getBudgetAllocations(userId: string): Promise<BudgetAllocation[]> {
    const { data, error } = await supabase
      .from("budget_allocations")
      .select("*")
      .eq("user_id", userId)
      .eq("is_active", true)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Create a new budget allocation
   */
  static async createBudgetAllocation(
    allocationData: CreateBudgetAllocationData
  ): Promise<BudgetAllocation> {
    const { data, error } = await supabase
      .from("budget_allocations")
      .insert([allocationData])
      .select()
      .single();

    if (error) throw error;

    // Trigger budget recalculation
    await this.triggerBudgetRecalculation(allocationData.user_id);

    return data;
  }

  /**
   * Update a budget allocation
   */
  static async updateBudgetAllocation(
    updateData: UpdateBudgetAllocationData
  ): Promise<BudgetAllocation> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabase
      .from("budget_allocations")
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    // Get user_id for budget recalculation
    const { data: allocation } = await supabase
      .from("budget_allocations")
      .select("user_id")
      .eq("id", id)
      .single();

    if (allocation?.user_id) {
      await this.triggerBudgetRecalculation(allocation.user_id);
    }

    return data;
  }

  /**
   * Delete a budget allocation (soft delete by setting is_active = false)
   */
  static async deleteBudgetAllocation(id: string): Promise<void> {
    // Get user_id before deletion for budget recalculation
    const { data: allocation } = await supabase
      .from("budget_allocations")
      .select("user_id")
      .eq("id", id)
      .single();

    const { error } = await supabase
      .from("budget_allocations")
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq("id", id);

    if (error) throw error;

    if (allocation?.user_id) {
      await this.triggerBudgetRecalculation(allocation.user_id);
    }
  }

  /**
   * Get budget summary from the view
   */
  static async getBudgetSummary(userId: string): Promise<BudgetSummaryData[]> {
    const { data, error } = await supabase
      .from("budget_summary_view")
      .select("*")
      .eq("user_id", userId);

    if (error) throw error;
    return data || [];
  }

  /**
   * Get current period budget allocation for a department
   */
  static async getCurrentDepartmentBudget(
    userId: string, 
    department: string
  ): Promise<BudgetAllocation | null> {
    const currentDate = new Date().toISOString().split('T')[0];

    const { data, error } = await supabase
      .from("budget_allocations")
      .select("*")
      .eq("user_id", userId)
      .eq("department", department)
      .eq("is_active", true)
      .lte("budget_period_start", currentDate)
      .gte("budget_period_end", currentDate)
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
    return data;
  }

  /**
   * Get current period budget allocation for a specific job
   */
  static async getCurrentJobBudget(
    userId: string, 
    jobId: string
  ): Promise<BudgetAllocation | null> {
    const currentDate = new Date().toISOString().split('T')[0];

    const { data, error } = await supabase
      .from("budget_allocations")
      .select("*")
      .eq("user_id", userId)
      .eq("job_id", jobId)
      .eq("is_active", true)
      .lte("budget_period_start", currentDate)
      .gte("budget_period_end", currentDate)
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  /**
   * Initialize default budget allocations for a new user
   */
  static async initializeDefaultBudgetAllocations(userId: string): Promise<BudgetAllocation[]> {
    const currentYear = new Date().getFullYear();
    const startDate = `${currentYear}-01-01`;
    const endDate = `${currentYear}-12-31`;

    const defaultAllocations: CreateBudgetAllocationData[] = [
      {
        user_id: userId,
        name: `${currentYear} Annual Recruitment Budget`,
        total_budget: 500000,
        budget_period_start: startDate,
        budget_period_end: endDate,
      },
      {
        user_id: userId,
        name: `${currentYear} Engineering Recruitment`,
        total_budget: 200000,
        budget_period_start: startDate,
        budget_period_end: endDate,
        department: "Engineering",
      },
      {
        user_id: userId,
        name: `${currentYear} Product Recruitment`,
        total_budget: 150000,
        budget_period_start: startDate,
        budget_period_end: endDate,
        department: "Product",
      },
    ];

    const createdAllocations = await Promise.all(
      defaultAllocations.map((allocation) =>
        this.createBudgetAllocation(allocation)
      )
    );

    return createdAllocations;
  }

  /**
   * Trigger budget recalculation
   */
  private static async triggerBudgetRecalculation(userId: string): Promise<void> {
    try {
      await supabase.rpc("recalculate_budget_data", { p_user_id: userId });
    } catch (error) {
      console.error("Error triggering budget recalculation:", error);
      // Don't throw - this is a background operation
    }
  }

  /**
   * Get total budget utilization across all allocations
   */
  static async getTotalBudgetUtilization(userId: string): Promise<{
    total_allocated: number;
    total_spent: number;
    utilization_percentage: number;
    active_allocations: number;
  }> {
    const summaries = await this.getBudgetSummary(userId);

    const totalAllocated = summaries.reduce((sum, s) => sum + s.total_budget, 0);
    const totalSpent = summaries.reduce((sum, s) => sum + s.spent_amount, 0);
    const utilizationPercentage = totalAllocated > 0 ? (totalSpent / totalAllocated) * 100 : 0;

    return {
      total_allocated: totalAllocated,
      total_spent: totalSpent,
      utilization_percentage: utilizationPercentage,
      active_allocations: summaries.length,
    };
  }
}
