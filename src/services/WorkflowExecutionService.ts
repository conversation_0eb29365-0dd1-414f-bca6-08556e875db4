import { supabase } from "@/integrations/supabase/client";

export interface WorkflowExecutionOptions {
  workflowId: string;
  userId: string;
  context?: Record<string, unknown>;
  trigger?: "manual" | "webhook" | "schedule";
  triggerId?: string;
}

export class WorkflowExecutionService {
  /**
   * Execute a workflow by creating an execution record and calling the edge function
   */
  static async executeWorkflow(options: WorkflowExecutionOptions): Promise<{
    success: boolean;
    executionId?: string;
    error?: string;
  }> {
    try {
      // Create execution record
      const { data: execution, error: executionError } = await supabase
        .from("workflow_executions")
        .insert({
          workflow_id: options.workflowId,
          status: "pending",
          execution_data: {
            trigger: options.trigger || "manual",
            trigger_id: options.triggerId,
            context: options.context || {},
          },
          created_by: options.userId,
        })
        .select()
        .single();

      if (executionError) {
        console.error("Failed to create workflow execution:", executionError);
        return {
          success: false,
          error: "Failed to create workflow execution",
        };
      }

      // Call the edge function to execute the workflow
      const { data, error } = await supabase.functions.invoke(
        "workflow-executor",
        {
          body: {
            execution_id: execution.id,
            workflow_id: options.workflowId,
            user_id: options.userId,
            context: options.context || {},
          },
        },
      );

      if (error) {
        console.error("Failed to invoke workflow executor:", error);

        // Update execution status to failed
        await supabase
          .from("workflow_executions")
          .update({
            status: "failed",
            completed_at: new Date().toISOString(),
            execution_data: {
              ...execution.execution_data,
              error: error.message,
            },
          })
          .eq("id", execution.id);

        return {
          success: false,
          executionId: execution.id,
          error: "Failed to execute workflow",
        };
      }

      return {
        success: true,
        executionId: execution.id,
      };
    } catch (error) {
      console.error("Workflow execution error:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get execution status and logs
   */
  static async getExecutionStatus(executionId: string): Promise<{
    status: string;
    logs: any[];
    error?: string;
  } | null> {
    try {
      const { data, error } = await supabase
        .from("workflow_executions")
        .select("*")
        .eq("id", executionId)
        .single();

      if (error) {
        console.error("Failed to fetch execution status:", error);
        return null;
      }

      return {
        status: data.status,
        logs: data.logs || [],
        error: data.execution_data?.error,
      };
    } catch (error) {
      console.error("Error fetching execution status:", error);
      return null;
    }
  }

  /**
   * Subscribe to execution updates using Realtime
   */
  static subscribeToExecution(
    executionId: string,
    onUpdate: (status: string, logs?: any[]) => void,
  ) {
    const channel = supabase
      .channel(`execution-${executionId}`)
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "workflow_executions",
          filter: `id=eq.${executionId}`,
        },
        (payload) => {
          const { status, logs } = payload.new;
          onUpdate(status, logs);
        },
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }

  /**
   * Trigger a scheduled workflow manually
   */
  static async triggerScheduledWorkflow(scheduleId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Fetch the schedule
      const { data: schedule, error: scheduleError } = await supabase
        .from("workflow_schedules")
        .select("*, workflow_configurations(*)")
        .eq("id", scheduleId)
        .single();

      if (scheduleError || !schedule) {
        return {
          success: false,
          error: "Schedule not found",
        };
      }

      // Execute the workflow
      const result = await this.executeWorkflow({
        workflowId: schedule.workflow_id,
        userId: schedule.created_by,
        context: schedule.context || {},
        trigger: "schedule",
        triggerId: scheduleId,
      });

      if (result.success) {
        // Update last run time
        await supabase
          .from("workflow_schedules")
          .update({
            last_run: new Date().toISOString(),
          })
          .eq("id", scheduleId);
      }

      return result;
    } catch (error) {
      console.error("Error triggering scheduled workflow:", error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }
}
