/**
 * EventsService
 * Centralized service for all event-related database operations
 */

import { supabase } from "@/integrations/supabase/client";

export interface Event {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string; // Keep for backward compatibility
  location_name?: string; // New normalized field
  location_type?: string; // New normalized field
  location_city?: string; // New normalized field
  location_state?: string; // New normalized field
  location_is_remote?: boolean; // New normalized field
  meeting_link?: string;
  event_type: "meeting" | "interview" | "call" | "presentation" | "other"; // Keep for backward compatibility
  event_type_name?: string; // New normalized field
  category: "general" | "recruitment" | "client" | "internal";
  priority: "low" | "medium" | "high"; // Keep for backward compatibility
  priority_name?: string; // New normalized field
  priority_level?: number; // New normalized field
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateEventData {
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  meeting_link?: string;
  event_type: "meeting" | "interview" | "call" | "presentation" | "other";
  category: "general" | "recruitment" | "client" | "internal";
  priority: "low" | "medium" | "high";
  user_id: string;
  // Note: The normalized fields (event_type_name, priority_name, etc.) 
  // are populated automatically by the database views
}

export interface UpdateEventData {
  title?: string;
  description?: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  meeting_link?: string;
  event_type?: "meeting" | "interview" | "call" | "presentation" | "other";
  category?: "general" | "recruitment" | "client" | "internal";
  priority?: "low" | "medium" | "high";
  // Note: The normalized fields (event_type_name, priority_name, etc.) 
  // are populated automatically by the database views
}

export class EventsService {
  /**
   * Get all events for a user
   */
  static async getEvents(userId: string): Promise<Event[]> {
    const { data, error } = await supabase
      .from("events_with_normalized_data")
      .select("*")
      .eq("user_id", userId)
      .order("start_time", { ascending: true });

    if (error) {
      console.error("Error fetching events:", error);
      throw new Error(`Failed to fetch events: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get events within a date range
   */
  static async getEventsByDateRange(
    userId: string,
    startDate: string,
    endDate: string,
  ): Promise<Event[]> {
    const { data, error } = await supabase
      .from("events_with_normalized_data")
      .select("*")
      .eq("user_id", userId)
      .gte("start_time", startDate)
      .lte("end_time", endDate)
      .order("start_time", { ascending: true });

    if (error) {
      console.error("Error fetching events by date range:", error);
      throw new Error(`Failed to fetch events: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get a single event by ID
   */
  static async getEvent(eventId: string): Promise<Event | null> {
    const { data, error } = await supabase
      .from("events_with_normalized_data")
      .select("*")
      .eq("id", eventId)
      .single();

    if (error) {
      console.error("Error fetching event:", error);
      throw new Error(`Failed to fetch event: ${error.message}`);
    }

    return data;
  }

  /**
   * Create a new event
   */
  static async createEvent(eventData: CreateEventData): Promise<Event> {
    // Validate required fields
    if (!eventData.title) {
      throw new Error("Event title is required");
    }

    if (!eventData.start_time || !eventData.end_time) {
      throw new Error("Event start and end times are required");
    }

    // Ensure end time is after start time
    if (new Date(eventData.end_time) <= new Date(eventData.start_time)) {
      throw new Error("End time must be after start time");
    }

    if (!eventData.user_id) {
      throw new Error("User ID is required");
    }

    // Get the foreign key IDs for the normalized fields
    let eventTypeId: string | null = null;
    let priorityId: string | null = null;
    let locationId: string | null = null;

    // Get event_type_id
    if (eventData.event_type) {
      const { data: eventTypeData } = await supabase
        .from("event_types")
        .select("id")
        .eq("name", eventData.event_type)
        .single();
      eventTypeId = eventTypeData?.id || null;
    }

    // Get priority_id
    if (eventData.priority) {
      const { data: priorityData } = await supabase
        .from("priorities")
        .select("id")
        .eq("name", eventData.priority)
        .single();
      priorityId = priorityData?.id || null;
    }

    // Get location_id if location is provided
    if (eventData.location) {
      const { data: locationData } = await supabase
        .from("locations")
        .select("id")
        .eq("name", eventData.location)
        .single();
      locationId = locationData?.id || null;
    }

    // Prepare the data for insertion
    const insertData = {
      title: eventData.title,
      description: eventData.description,
      start_time: eventData.start_time,
      end_time: eventData.end_time,
      meeting_link: eventData.meeting_link,
      category: eventData.category,
      user_id: eventData.user_id,
      event_type_id: eventTypeId,
      priority_id: priorityId,
      location_id: locationId,
    };

    const { data, error } = await supabase
      .from("events")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error("Error creating event:", error);
      throw new Error(`Failed to create event: ${error.message}`);
    }

    return data;
  }

  /**
   * Update an existing event
   */
  static async updateEvent(
    eventId: string,
    updateData: UpdateEventData,
  ): Promise<Event> {
    // Validate dates if both are provided
    if (updateData.start_time && updateData.end_time) {
      if (new Date(updateData.end_time) <= new Date(updateData.start_time)) {
        throw new Error("End time must be after start time");
      }
    }

    // Get the foreign key IDs for the normalized fields if they're being updated
    let eventTypeId: string | null = undefined;
    let priorityId: string | null = undefined;
    let locationId: string | null = undefined;

    if (updateData.event_type) {
      const { data: eventTypeData } = await supabase
        .from("event_types")
        .select("id")
        .eq("name", updateData.event_type)
        .single();
      eventTypeId = eventTypeData?.id || null;
    }

    if (updateData.priority) {
      const { data: priorityData } = await supabase
        .from("priorities")
        .select("id")
        .eq("name", updateData.priority)
        .single();
      priorityId = priorityData?.id || null;
    }

    if (updateData.location) {
      const { data: locationData } = await supabase
        .from("locations")
        .select("id")
        .eq("name", updateData.location)
        .single();
      locationId = locationData?.id || null;
    }

    // Prepare the update data
    const updateFields: any = {
      title: updateData.title,
      description: updateData.description,
      start_time: updateData.start_time,
      end_time: updateData.end_time,
      meeting_link: updateData.meeting_link,
      category: updateData.category,
    };

    // Only include foreign key fields if they were provided
    if (eventTypeId !== undefined) updateFields.event_type_id = eventTypeId;
    if (priorityId !== undefined) updateFields.priority_id = priorityId;
    if (locationId !== undefined) updateFields.location_id = locationId;

    const { data, error } = await supabase
      .from("events")
      .update(updateFields)
      .eq("id", eventId)
      .select()
      .single();

    if (error) {
      console.error("Error updating event:", error);
      throw new Error(`Failed to update event: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete an event
   */
  static async deleteEvent(eventId: string): Promise<void> {
    const { error } = await supabase
      .from("events")
      .delete()
      .eq("id", eventId);

    if (error) {
      console.error("Error deleting event:", error);
      throw new Error(`Failed to delete event: ${error.message}`);
    }
  }

  /**
   * Create an interview event
   */
  static async createInterviewEvent(
    userId: string,
    candidateName: string,
    interviewType: string,
    startTime: Date,
    durationMinutes: number,
    location?: string,
    meetingLink?: string,
  ): Promise<Event> {
    const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);

    return this.createEvent({
      title: `Interview with ${candidateName}`,
      description: `${interviewType} interview with ${candidateName}`,
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      location: location || "",
      meeting_link: meetingLink || "",
      event_type: "interview",
      category: "recruitment",
      priority: "medium",
      user_id: userId,
    });
  }

  /**
   * Get upcoming events for a user
   */
  static async getUpcomingEvents(
    userId: string,
    limit: number = 10,
  ): Promise<Event[]> {
    const now = new Date().toISOString();

    const { data, error } = await supabase
      .from("events_with_normalized_data")
      .select("*")
      .eq("user_id", userId)
      .gte("start_time", now)
      .order("start_time", { ascending: true })
      .limit(limit);

    if (error) {
      console.error("Error fetching upcoming events:", error);
      throw new Error(`Failed to fetch upcoming events: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get events by type
   */
  static async getEventsByType(
    userId: string,
    eventType: Event["event_type"],
  ): Promise<Event[]> {
    const { data, error } = await supabase
      .from("events_with_normalized_data")
      .select("*")
      .eq("user_id", userId)
      .eq("event_type", eventType)
      .order("start_time", { ascending: true });

    if (error) {
      console.error("Error fetching events by type:", error);
      throw new Error(`Failed to fetch events by type: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get events by category
   */
  static async getEventsByCategory(
    userId: string,
    category: Event["category"],
  ): Promise<Event[]> {
    const { data, error } = await supabase
      .from("events_with_normalized_data")
      .select("*")
      .eq("user_id", userId)
      .eq("category", category)
      .order("start_time", { ascending: true });

    if (error) {
      console.error("Error fetching events by category:", error);
      throw new Error(`Failed to fetch events by category: ${error.message}`);
    }

    return data || [];
  }
}
