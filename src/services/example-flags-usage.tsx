// Example: Using FLAGS with Option A (Recommended)
import { FLAGS, useFeatureFlag } from "@/services";

// Simple usage example
export const checkFeatureAccess = async (userId: string) => {
  // Direct FLAGS usage
  const flagName = FLAGS.AI_WORKFLOWS;
  // Checking access for flag

  // All available flags
  const allFlags = [
    FLAGS.AI_WORKFLOWS,
    FLAGS.WORKFLOW_TEMPLATES,
    FLAGS.ADVANCED_ANALYTICS,
    FLAGS.REALTIME_COLLABORATION,
    FLAGS.CUSTOM_INTEGRATIONS,
    FLAGS.WORKFLOW_SCHEDULER,
    FLAGS.AI_SUGGESTIONS,
    FLAGS.PERFORMANCE_INSIGHTS,
  ];

  // Available feature flags loaded
};

// Example: Using FLAGS with Option B

export const checkFeatureAccessOptionB = async (userId: string) => {
  // Using FLAGS constant (same as Option A)
  const flagName = FLAGS.AI_WORKFLOWS;
  // Checking access for flag
};

// React component example
import React from "react";

export const FeatureGatedComponent: React.FC = () => {
  // Using the hook with FLAGS constant
  const hasAIWorkflows = useFeatureFlag(FLAGS.AI_WORKFLOWS);
  const hasScheduler = useFeatureFlag(FLAGS.WORKFLOW_SCHEDULER);

  if (!hasAIWorkflows) {
    return <div>AI Workflows feature is not available</div>;
  }

  return (
    <div>
      <h2>AI Workflows</h2>
      {hasScheduler && <p>Scheduler is also available!</p>}
    </div>
  );
};
