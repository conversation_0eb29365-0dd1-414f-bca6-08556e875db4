import { useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeRecord } from "@/hooks/useRealtimeSubscription";

// Add avatar upload functionality
export const useUploadAvatar = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (file: File): Promise<string> => {
      if (!user) throw new Error("User not authenticated");

      console.log("Starting avatar upload for user:", user.id);
      console.log("File details:", { name: file.name, size: file.size, type: file.type });

      // Create a unique file path
      const fileExt = file.name.split(".").pop();
      const fileName = `${user.id}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${user.id}/${fileName}`; // Path without bucket name prefix
      const fullPath = `avatars/${user.id}/${fileName}`; // Full path for logging

      console.log("Upload path:", filePath);
      console.log("Full path:", fullPath);

      // Upload to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, file, {
          upsert: true,
          contentType: file.type,
        });

      if (uploadError) {
        console.error("Upload error details:", uploadError);
        throw uploadError;
      }

      console.log("Upload successful:", uploadData);

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from("avatars").getPublicUrl(filePath);

      console.log("Public URL:", publicUrl);

      return publicUrl;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Avatar Updated",
        description: "Your profile picture has been updated successfully.",
      });
    },
    onError: (error) => {
      console.error("Error uploading avatar:", error);
      toast({
        title: "Error",
        description: "Failed to upload avatar. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  company: string | null;
  role: string | null;
  avatar_url: string | null;
  company_id?: string | null;
  created_at: string;
  updated_at: string;
}

export const useProfile = () => {
  const { user } = useAuth();
  const { record: profile, isLoading } = useRealtimeRecord(
    "profiles",
    user?.id || "",
    "public",
  );

  // Handle profile initialization when profile is null but user exists
  useEffect(() => {
    const initializeProfile = async () => {
      if (user && !isLoading && !profile) {
        console.log(
          "No profile found, creating default profile for user:",
          user.id,
        );
        await createDefaultProfile(user.id);
      }
    };

    initializeProfile();
  }, [user, profile, isLoading]);

  return {
    data: profile,
    isLoading,
    error: null,
  };
};

// Helper function to create a default profile
async function createDefaultProfile(userId: string): Promise<Profile | null> {
  try {
    const defaultProfile = {
      id: userId,
      first_name: null,
      last_name: null,
      company: "Your Company",
      role: "Recruiter",
      avatar_url: null,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("profiles")
      .insert(defaultProfile)
      .select()
      .single();

    if (error) {
      console.error("Error creating default profile:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error creating default profile:", error);
    return null;
  }
}
export const useUpdateProfile = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (updatedProfile: Partial<Profile>) => {
      if (!user) throw new Error("User not authenticated");
      
      console.log("Updating profile for user:", user.id);
      console.log("Update data:", updatedProfile);
      
      // Validate profile data
      if (updatedProfile.first_name === "") {
        updatedProfile.first_name = null;
      }

      if (updatedProfile.last_name === "") {
        updatedProfile.last_name = null;
      }

      // Remove id from the update payload if it exists
      const { id, ...updateData } = updatedProfile;
      
      // Update profile - the trigger issue has been fixed
      const { data, error } = await supabase
        .from("profiles")
        .update({
          first_name: updateData.first_name,
          last_name: updateData.last_name,
          company: updateData.company,
          role: updateData.role,
          avatar_url: updateData.avatar_url,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        console.error("Profile update error:", error);
        throw error;
      }
      
      console.log("Profile updated successfully:", data);
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    },
  });
};
