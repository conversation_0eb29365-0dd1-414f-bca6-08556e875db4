import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Location } from "./useLocations";

interface CreateLocationData {
  name: string;
}

export const useCreateLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateLocationData): Promise<Location> => {
      const { data: newLocation, error } = await supabase
        .from("locations")
        .insert([data])
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return newLocation;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['locations'] });
    },
  });
}; 