import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { MessagingService } from "@/services";

export interface Message {
  id: string;
  sender_name: string;
  sender_email: string;
  sender_role?: string;
  sender_avatar?: string;
  content: string;
  status: "unread" | "read" | "archived";
  is_starred: boolean;
  follow_up: boolean;
  reminder: boolean;
  created_at: string;
  updated_at: string;
}

export const useMessages = () => {
  const { user } = useAuth();

  // Real-time messages subscription
  const { records: messages = [], isLoading } = useRealtimeCollection(
    "messages",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      try {
        const existingMessages = await MessagingService.getMessages(user.id);

        // If no messages exist, create sample messages
        if (!existingMessages || existingMessages.length === 0) {
          return createSampleMessages(user.id);
        }

        return existingMessages;
      } catch (error) {
        console.error("Error in useMessages:", error);

        // If table doesn't exist or other error, create sample messages
        if (error instanceof Error && error.message.includes("42P01")) {
          return createSampleMessages(user.id);
        }

        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: messages, isLoading, error: null };
};

// Helper function to create sample messages if none exist
async function createSampleMessages(userId: string): Promise<Message[]> {
  const sampleMessages = [
    {
      sender_name: "John Smith",
      sender_email: "<EMAIL>",
      sender_role: "Hiring Manager",
      sender_avatar: "https://i.pravatar.cc/150?u=1",
      content:
        "Hello, I wanted to discuss the Frontend Developer position. We have several candidates that look promising.",
      status: "unread" as const,
      is_starred: false,
      follow_up: false,
      reminder: false,
      user_id: userId,
    },
    {
      sender_name: "Sarah Johnson",
      sender_email: "<EMAIL>",
      sender_role: "Senior Developer",
      sender_avatar: "https://i.pravatar.cc/150?u=2",
      content:
        "I've reviewed the technical assessment for Michael Chen. He did very well on the coding challenge.",
      status: "read" as const,
      is_starred: true,
      follow_up: true,
      reminder: false,
      user_id: userId,
    },
  ];

  try {
    const createdMessages = [];
    for (const messageData of sampleMessages) {
      const message = await MessagingService.createMessage(messageData);
      createdMessages.push(message);
    }
    return createdMessages;
  } catch (error) {
    console.error("Error creating sample messages:", error);
    return [];
  }
}

// Export the useUpdateMessage hook
export { useUpdateMessage } from "./useUpdateMessage";
