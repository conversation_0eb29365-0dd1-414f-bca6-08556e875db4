import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, waitFor } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useUpdateCandidate } from "../useUpdateCandidate";
import { CandidatesService } from "@/services";
import { useToast } from "@/hooks/use-toast";
import React from "react";

// Mock the services and hooks
vi.mock("@/services", () => ({
  CandidatesService: {
    updateCandidate: vi.fn(),
  },
}));

vi.mock("@/hooks/use-toast", () => ({
  useToast: vi.fn(),
}));

describe("useUpdateCandidate", () => {
  let queryClient: QueryClient;
  let mockToast: vi.MockedFunction<any>;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create a new QueryClient for each test
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    // Mock toast function
    mockToast = vi.fn();
    (useToast as any).mockReturnValue({ toast: mockToast });
  });

  const wrapper = ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);

  it("should successfully update a candidate", async () => {
    const mockCandidate = {
      id: "test-id",
      name: "John Doe",
      email: "<EMAIL>",
      role: "Software Engineer",
    };

    const updateData = {
      name: "John Updated",
      email: "<EMAIL>",
      role: "Senior Software Engineer",
    };

    // Mock successful update
    (CandidatesService.updateCandidate as any).mockResolvedValueOnce({
      ...mockCandidate,
      ...updateData,
    });

    const { result } = renderHook(() => useUpdateCandidate(), { wrapper });

    // Execute the mutation
    result.current.mutate({
      id: mockCandidate.id,
      data: updateData,
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Verify the service was called correctly
    expect(CandidatesService.updateCandidate).toHaveBeenCalledWith(
      mockCandidate.id,
      updateData,
    );

    // Verify success toast was shown
    expect(mockToast).toHaveBeenCalledWith({
      title: "Success",
      description: "Candidate updated successfully",
    });
  });

  it("should handle errors when updating a candidate", async () => {
    const errorMessage = "Failed to update candidate";
    const mockError = new Error(errorMessage);

    // Mock failed update
    (CandidatesService.updateCandidate as any).mockRejectedValueOnce(mockError);

    const invalidateQueriesSpy = vi.spyOn(queryClient, "invalidateQueries");
    const { result } = renderHook(() => useUpdateCandidate(), { wrapper });

    // Execute the mutation
    result.current.mutate({
      id: "test-id",
      data: { name: "Test" },
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    // Verify error toast was shown
    expect(mockToast).toHaveBeenCalledWith({
      title: "Error",
      description: `Failed to update candidate: ${errorMessage}`,
      variant: "destructive",
    });
  });

  it("should validate candidate ID before updating", async () => {
    const { result } = renderHook(() => useUpdateCandidate(), { wrapper });

    // Execute the mutation without ID
    result.current.mutate({
      id: "",
      data: { name: "Test" },
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    // Verify the service was not called
    expect(CandidatesService.updateCandidate).not.toHaveBeenCalled();

    // Verify error toast was shown
    expect(mockToast).toHaveBeenCalledWith({
      title: "Error",
      description: "Failed to update candidate: Candidate ID is required",
      variant: "destructive",
    });
  });

  it("should handle partial updates correctly", async () => {
    const candidateId = "test-id";
    const partialUpdate = {
      phone: "+1234567890",
      location: "New York, NY",
      socialLinks: {
        linkedin: "https://linkedin.com/in/johndoe",
      },
    };

    (CandidatesService.updateCandidate as any).mockResolvedValueOnce({
      id: candidateId,
      ...partialUpdate,
    });

    const { result } = renderHook(() => useUpdateCandidate(), { wrapper });

    result.current.mutate({
      id: candidateId,
      data: partialUpdate,
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(CandidatesService.updateCandidate).toHaveBeenCalledWith(
      candidateId,
      partialUpdate,
    );
  });

  it("should handle non-Error objects in error cases", async () => {
    // Mock failed update with non-Error object
    (CandidatesService.updateCandidate as any).mockRejectedValueOnce(
      "Unknown error",
    );

    const { result } = renderHook(() => useUpdateCandidate(), { wrapper });

    result.current.mutate({
      id: "test-id",
      data: { name: "Test" },
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    // Verify error toast was shown with generic message
    expect(mockToast).toHaveBeenCalledWith({
      title: "Error",
      description: "Failed to update candidate: Unknown error",
      variant: "destructive",
    });
  });

  it("should properly invalidate queries on success", async () => {
    const candidateId = "test-123";

    (CandidatesService.updateCandidate as any).mockResolvedValueOnce({
      id: candidateId,
      name: "Updated Name",
    });

    const invalidateQueriesSpy = vi.spyOn(queryClient, "invalidateQueries");

    const { result } = renderHook(() => useUpdateCandidate(), { wrapper });

    result.current.mutate({
      id: candidateId,
      data: { name: "Updated Name" },
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Check that specific queries were invalidated
    expect(invalidateQueriesSpy).toHaveBeenCalledWith({
      queryKey: ["candidate", candidateId],
    });
    expect(invalidateQueriesSpy).toHaveBeenCalledWith({
      queryKey: ["candidates"],
    });
  });
});
