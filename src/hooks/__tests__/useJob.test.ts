import { renderHook, waitFor } from "@testing-library/react";
import { useJob } from "../useJob";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { supabase } from "@/integrations/supabase/client";

// Mock the Supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(),
  },
}));

describe("useJob", () => {
  const mockFrom = supabase.from as any;
  const mockSelect = vi.fn();
  const mockEq = vi.fn();
  const mockSingle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockFrom.mockReturnValue({ select: mockSelect });
    mockSelect.mockReturnValue({ eq: mockEq });
    mockEq.mockReturnValue({ single: mockSingle });
  });

  it("should return initial state when no jobId is provided", () => {
    const { result } = renderHook(() => useJob(undefined));

    expect(result.current.data).toBeNull();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("should fetch job data successfully", async () => {
    const mockJob = {
      id: "123",
      title: "Software Engineer",
      department: "Engineering",
      description: "Job description",
      location: "Remote",
      job_type: "Full-time",
      created_at: "2024-01-01",
      updated_at: "2024-01-01",
      user_id: "user123",
      is_active: true,
      is_urgent: false,
      applicant_count: 10,
      benefits: ["Health", "Dental"],
      requirements: ["React", "TypeScript"],
      salary_range: "$100k-$150k",
      experience_required: "3+ years",
      search_vector: null,
    };

    mockSingle.mockResolvedValue({ data: mockJob, error: null });

    const { result } = renderHook(() => useJob("123"));

    // Initial loading state
    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBeNull();
    expect(result.current.error).toBeNull();

    // Wait for the hook to finish loading
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Check final state
    expect(result.current.data).toEqual(mockJob);
    expect(result.current.error).toBeNull();
    expect(mockFrom).toHaveBeenCalledWith("jobs");
    expect(mockSelect).toHaveBeenCalledWith("*");
    expect(mockEq).toHaveBeenCalledWith("id", "123");
    expect(mockSingle).toHaveBeenCalled();
  });

  it("should handle error from Supabase", async () => {
    const mockError = { message: "Job not found" };
    mockSingle.mockResolvedValue({ data: null, error: mockError });

    const { result } = renderHook(() => useJob("123"));

    // Initial loading state
    expect(result.current.loading).toBe(true);

    // Wait for the hook to finish loading
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Check error state
    expect(result.current.data).toBeNull();
    expect(result.current.error).toBe("Job not found");
  });

  it("should handle unexpected errors", async () => {
    mockSingle.mockRejectedValue(new Error("Network error"));

    const { result } = renderHook(() => useJob("123"));

    // Wait for the hook to finish loading
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Check error state
    expect(result.current.data).toBeNull();
    expect(result.current.error).toBe("Network error");
  });

  it("should handle non-Error exceptions", async () => {
    mockSingle.mockRejectedValue("Something went wrong");

    const { result } = renderHook(() => useJob("123"));

    // Wait for the hook to finish loading
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Check error state
    expect(result.current.data).toBeNull();
    expect(result.current.error).toBe("An unexpected error occurred");
  });

  it("should reset state when jobId changes to undefined", async () => {
    const mockJob = {
      id: "123",
      title: "Software Engineer",
      department: "Engineering",
      description: "Job description",
      location: "Remote",
      job_type: "Full-time",
      created_at: "2024-01-01",
      updated_at: "2024-01-01",
      user_id: "user123",
      is_active: true,
      is_urgent: false,
      applicant_count: 10,
      benefits: ["Health", "Dental"],
      requirements: ["React", "TypeScript"],
      salary_range: "$100k-$150k",
      experience_required: "3+ years",
      search_vector: null,
    };

    mockSingle.mockResolvedValue({ data: mockJob, error: null });

    const { result, rerender } = renderHook(({ jobId }) => useJob(jobId), {
      initialProps: { jobId: "123" },
    });

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockJob);

    // Change jobId to undefined
    rerender({ jobId: undefined });

    // Check that state is reset
    expect(result.current.data).toBeNull();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("should refetch when jobId changes", async () => {
    const mockJob1 = {
      id: "123",
      title: "Software Engineer",
      department: "Engineering",
      description: "Job description",
      location: "Remote",
      job_type: "Full-time",
      created_at: "2024-01-01",
      updated_at: "2024-01-01",
      user_id: "user123",
      is_active: true,
      is_urgent: false,
      applicant_count: 10,
      benefits: ["Health", "Dental"],
      requirements: ["React", "TypeScript"],
      salary_range: "$100k-$150k",
      experience_required: "3+ years",
      search_vector: null,
    };

    const mockJob2 = {
      ...mockJob1,
      id: "456",
      title: "Product Manager",
      department: "Product",
    };

    mockSingle
      .mockResolvedValueOnce({ data: mockJob1, error: null })
      .mockResolvedValueOnce({ data: mockJob2, error: null });

    const { result, rerender } = renderHook(({ jobId }) => useJob(jobId), {
      initialProps: { jobId: "123" },
    });

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockJob1);

    // Change jobId
    rerender({ jobId: "456" });

    // Wait for refetch
    await waitFor(() => {
      expect(result.current.data?.id).toBe("456");
    });

    expect(result.current.data).toEqual(mockJob2);
    expect(mockEq).toHaveBeenLastCalledWith("id", "456");
  });
});
