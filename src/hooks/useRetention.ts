import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useMutation } from "@tanstack/react-query";
import {
  RetentionService,
  RetentionData,
  CreateRetentionData,
  UpdateRetentionData,
} from "@/services/RetentionService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

export const useRetention = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Real-time retention data subscription
  const { records: retentionData = [], isLoading } = useRealtimeCollection(
    "retention_predictions",
    async () => {
      if (!user?.id) throw new Error("User not authenticated");

      // Try to get existing data, or initialize with defaults
      const existingData = await RetentionService.getRetentionPredictions(
        user.id,
      );
      if (existingData.length > 0) {
        return existingData;
      }

      // Initialize with default retention data if none exists
      const defaultRetentionData: CreateRetentionData[] = [
        {
          user_id: user.id,
          department: "Engineering",
          score: 85,
          risk_level: "Low",
        },
        {
          user_id: user.id,
          department: "Sales",
          score: 65,
          risk_level: "Medium",
        },
        {
          user_id: user.id,
          department: "Marketing",
          score: 78,
          risk_level: "Low",
        },
        {
          user_id: user.id,
          department: "Product",
          score: 58,
          risk_level: "High",
        },
      ];

      // Create default retention data
      const createdData = await Promise.all(
        defaultRetentionData.map((retention) =>
          RetentionService.createRetentionPrediction(retention),
        ),
      );

      return createdData;
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: retentionData, isLoading, error: null };
};

export const useHighRiskDepartments = () => {
  const { user } = useAuth();

  // Real-time high risk departments subscription
  const { records: highRiskData = [], isLoading } = useRealtimeCollection(
    "retention_predictions",
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      return RetentionService.getHighRiskDepartments(user.id);
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: highRiskData, isLoading, error: null };
};

export const useCreateRetention = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateRetentionData) =>
      RetentionService.createRetentionPrediction(data),
    onSuccess: () => {
      toast({
        title: "Retention Created",
        description: "Retention prediction has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateRetention = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: UpdateRetentionData) =>
      RetentionService.updateRetentionPrediction(data),
    onSuccess: () => {
      toast({
        title: "Retention Updated",
        description: "Retention prediction has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
