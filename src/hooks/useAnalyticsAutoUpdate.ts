import { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { AnalyticsDataService } from "@/services/AnalyticsDataService";

/**
 * Hook that initializes analytics data when the user first loads the app
 * This ensures analytics have some initial data without causing infinite loops
 */
export const useAnalyticsAutoUpdate = () => {
  const { user } = useAuth();

  // Initialize analytics data once when user loads the app
  useEffect(() => {
    if (!user) return;

    let isMounted = true;

    const initializeAnalytics = async () => {
      try {
        // Only update analytics once on app load, not on every data change
        await AnalyticsDataService.updateAnalyticsTables(user.id);
        if (isMounted) {
          console.log("Analytics data initialized");
        }
      } catch (error) {
        if (isMounted) {
          console.error("Failed to initialize analytics:", error);
        }
      }
    };

    // Initialize analytics after a short delay to let the app settle
    const timeoutId = setTimeout(initializeAnalytics, 2000);

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [user]); // Only depend on user, not on data changes

  return {
    isUpdating: false,
  };
};
