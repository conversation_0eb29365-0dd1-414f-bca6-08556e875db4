import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export interface Benefit {
  id: string;
  name: string;
  category?: string;
}

export function useBenefits() {
  return useQuery({
    queryKey: ["benefits"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("benefits")
        .select("*")
        .order("name");

      if (error) throw error;

      return data.map((benefit) => ({
        id: benefit.id,
        name: benefit.name,
        category: benefit.category,
      })) as Benefit[];
    },
  });
}

export function useCreateBenefit() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ name, category }: { name: string; category?: string }) => {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("benefits")
        .insert({
          name,
          category,
          user_id: userData.user.id,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        name: data.name,
        category: data.category,
      } as Benefit;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["benefits"] });
      toast({
        title: "Success",
        description: "Benefit created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useUpdateBenefit() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      id,
      name,
      category,
    }: {
      id: string;
      name: string;
      category?: string;
    }) => {
      const { error } = await supabase
        .from("benefits")
        .update({ name, category })
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["benefits"] });
      toast({
        title: "Success",
        description: "Benefit updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useDeleteBenefit() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("benefits")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["benefits"] });
      toast({
        title: "Success",
        description: "Benefit deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useJobBenefits(jobId: string) {
  return useQuery({
    queryKey: ["job-benefits", jobId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("job_benefits")
        .select("benefit_id, benefits!inner(id, name, category)")
        .eq("job_id", jobId);

      if (error) throw error;

      return data.map((item: any) => ({
        id: item.benefits.id,
        name: item.benefits.name,
        category: item.benefits.category,
      })) as Benefit[];
    },
    enabled: !!jobId,
  });
}

export function useUpdateJobBenefits() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      jobId,
      benefitIds,
    }: {
      jobId: string;
      benefitIds: string[];
    }) => {
      // Delete existing benefits
      const { error: deleteError } = await supabase
        .from("job_benefits")
        .delete()
        .eq("job_id", jobId);

      if (deleteError) throw deleteError;

      // Insert new benefits
      if (benefitIds.length > 0) {
        const { error: insertError } = await supabase
          .from("job_benefits")
          .insert(
            benefitIds.map((benefitId) => ({
              job_id: jobId,
              benefit_id: benefitId,
            })),
          );

        if (insertError) throw insertError;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["job-benefits", variables.jobId],
      });
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
      toast({
        title: "Success",
        description: "Benefits updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
} 