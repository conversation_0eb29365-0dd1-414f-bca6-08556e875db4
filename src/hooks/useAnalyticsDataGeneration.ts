import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { AnalyticsDataService } from "@/services/AnalyticsDataService";

/**
 * Hook for generating and updating analytics data
 */
export const useGenerateAnalyticsData = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      if (!user) throw new Error("User must be authenticated");
      return await AnalyticsDataService.updateAnalyticsTables(user.id);
    },
    onSuccess: () => {
      toast({
        title: "Analytics Updated",
        description:
          "Analytics data has been refreshed with the latest information.",
      });
    },
    onError: (error) => {
      console.error("Error generating analytics data:", error);
      toast({
        title: "Update Failed",
        description: "Failed to update analytics data. Please try again.",
        variant: "destructive",
      });
    },
  });
};

/**
 * Hook for getting job analytics data
 */
export const useJobAnalytics = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      if (!user) throw new Error("User must be authenticated");
      return await AnalyticsDataService.generateJobAnalytics(user.id);
    },
    onError: (error) => {
      console.error("Error getting job analytics:", error);
      toast({
        title: "Analytics Error",
        description: "Failed to load job analytics data.",
        variant: "destructive",
      });
    },
  });
};

/**
 * Hook for getting candidate analytics data
 */
export const useCandidateAnalytics = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      if (!user) throw new Error("User must be authenticated");
      return await AnalyticsDataService.generateCandidateAnalytics(user.id);
    },
    onError: (error) => {
      console.error("Error getting candidate analytics:", error);
      toast({
        title: "Analytics Error",
        description: "Failed to load candidate analytics data.",
        variant: "destructive",
      });
    },
  });
};
