import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface JobType {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export const useJobTypes = () => {
  return useQuery({
    queryKey: ['job_types'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("job_types")
        .select("*")
        .order("name");

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    },
  });
}; 