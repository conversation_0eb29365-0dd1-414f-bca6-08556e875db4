import { useAuth } from "@/contexts/AuthContext";
import { CandidateType } from "@/types/candidate";
import { CandidatesService } from "@/services";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export const useCandidates = () => {
  const { user } = useAuth();

  // Real-time candidates subscription
  const { records: candidates = [], isLoading } = useRealtimeCollection(
    "candidates",
    async () => {
      if (!user) {
        console.log("No user found, returning empty array");
        return [];
      }

      console.log("🔍 Fetching candidates for user:", user.id);

      try {
        return await CandidatesService.getCandidates(user.id);
      } catch (error) {
        console.error("Error fetching candidates:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: candidates, isLoading, error: null };
};
