import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { Benefit } from "@/hooks/useBenefits";
import { Requirement } from "@/hooks/useRequirements";

export interface Job {
  id: string;
  title: string;
  // Database normalized fields
  department_id?: string; // Foreign key to departments table
  department_name?: string; // Denormalized from departments table
  location_id?: string; // Foreign key to locations table
  location_name?: string; // Denormalized from locations table
  location_type?: string; // Denormalized from locations table
  location_city?: string; // Denormalized from locations table
  location_state?: string; // Denormalized from locations table
  location_is_remote?: boolean; // Denormalized from locations table
  job_type_id?: string; // Foreign key to job_types table
  job_type_name?: string; // Denormalized from job_types table
  // Backward compatibility
  department: string; // Keep for backward compatibility
  location: string; // Keep for backward compatibility
  job_type: string; // Keep for backward compatibility
  salary_range?: string;
  experience_required?: string;
  description: string;
  requirements?: string[]; // Keep for backward compatibility
  benefits?: string[]; // Keep for backward compatibility
  normalized_requirements?: Requirement[]; // New normalized field
  normalized_benefits?: Benefit[]; // New normalized field
  is_urgent: boolean;
  is_active: boolean;
  applicant_count: number;
  created_at: string;
  updated_at: string;
  user_id: string;
}

export const useJobs = () => {
  const { user } = useAuth();

  // Real-time jobs subscription
  const { records: jobs = [], isLoading } = useRealtimeCollection(
    "jobs",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      const { data, error } = await supabase
        .from("jobs_with_normalized_data")
        .select("*")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching jobs:", error);
        throw error;
      }

      return data || [];
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: jobs, isLoading, error: null };
};
