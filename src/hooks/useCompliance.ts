import { useMutation } from "@tanstack/react-query";
import {
  ComplianceService,
  ComplianceData,
  CreateComplianceData,
  UpdateComplianceData,
} from "@/services/ComplianceService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export const useCompliance = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Real-time compliance data subscription
  const { records: complianceData = [], isLoading } = useRealtimeCollection(
    "compliance_data",
    async () => {
      if (!user?.id) return [];

      try {
        // Try to get existing data, or initialize with defaults
        const existingData = await ComplianceService.getComplianceData(user.id);
        if (existingData) {
          return [existingData]; // Wrap in array for useRealtimeCollection
        }

        // Initialize with default compliance data if none exists
        const defaultComplianceData: CreateComplianceData = {
          user_id: user.id,
          overall_score: 92,
          documentation_compliance: 98,
          equal_opportunity: 100,
          interview_process: 85,
        };

        // Create default compliance data
        const createdData = await ComplianceService.createComplianceData(
          defaultComplianceData,
        );
        return [createdData]; // Wrap in array for useRealtimeCollection
      } catch (error) {
        console.error("Error in useCompliance:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: complianceData[0] || null, isLoading, error: null };
};

export const useCreateCompliance = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (data: Omit<CreateComplianceData, "user_id">) => {
      if (!user?.id) throw new Error("User not authenticated");
      return ComplianceService.createComplianceData({
        ...data,
        user_id: user.id,
      });
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Compliance Data Created",
        description: "Compliance data has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateCompliance = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: UpdateComplianceData) =>
      ComplianceService.updateComplianceData(data),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Compliance Updated",
        description: "Compliance data has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
