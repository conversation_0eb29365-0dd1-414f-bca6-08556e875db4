import React from "react";
import { useMutation } from "@tanstack/react-query";
import {
  WorkflowTemplatesService,
  type WorkflowTemplateData,
  type CreateWorkflowTemplateData,
  type UpdateWorkflowTemplateData,
} from "@/services/WorkflowTemplatesService";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

// Workflow Templates Hooks
export const useWorkflowTemplates = (userId?: string) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  // Real-time workflow templates subscription
  const { records: templates = [], isLoading } = useRealtimeCollection(
    "workflow_templates",
    async () => {
      if (!targetUserId) {
        console.log("No user ID provided, returning empty array");
        return [];
      }

      try {
        return await WorkflowTemplatesService.getWorkflowTemplates(
          targetUserId,
        );
      } catch (error) {
        console.error("Error in useWorkflowTemplates:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${targetUserId}`,
  );

  return { data: templates, isLoading, error: null };
};

export const useWorkflowTemplatesByCategory = (
  userId?: string,
  category?: string,
) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  // Real-time workflow templates by category subscription
  const { records: templates = [], isLoading } = useRealtimeCollection(
    "workflow_templates",
    async () => {
      if (!targetUserId || !category) {
        console.log("No user ID or category provided, returning empty array");
        return [];
      }

      try {
        return await WorkflowTemplatesService.getWorkflowTemplatesByCategory(
          targetUserId,
          category,
        );
      } catch (error) {
        console.error("Error in useWorkflowTemplatesByCategory:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${targetUserId}`,
  );

  // Filter by category in real-time
  const filteredTemplates = React.useMemo(() => {
    if (!category) return templates;
    return templates.filter((template) => template.category === category);
  }, [templates, category]);

  return { data: filteredTemplates, isLoading, error: null };
};

export const useCreateWorkflowTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (templateData: CreateWorkflowTemplateData) =>
      WorkflowTemplatesService.createWorkflowTemplate(templateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Template Created",
        description: "Your workflow template has been successfully created.",
      });
    },
    onError: (error) => {
      console.error("Error creating workflow template:", error);
      toast({
        title: "Error",
        description: "Failed to create workflow template. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateWorkflowTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (updateData: UpdateWorkflowTemplateData) =>
      WorkflowTemplatesService.updateWorkflowTemplate(updateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Template Updated",
        description: "Your workflow template has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error("Error updating workflow template:", error);
      toast({
        title: "Error",
        description: "Failed to update workflow template. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteWorkflowTemplate = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) =>
      WorkflowTemplatesService.deleteWorkflowTemplate(id),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Template Deleted",
        description: "Your workflow template has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error("Error deleting workflow template:", error);
      toast({
        title: "Error",
        description: "Failed to delete workflow template. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Initialization hook
export const useInitializeWorkflowTemplates = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (userId: string) =>
      WorkflowTemplatesService.initializeWorkflowTemplates(userId),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Templates Initialized",
        description:
          "Default workflow templates have been created successfully.",
      });
    },
    onError: (error) => {
      console.error("Error initializing workflow templates:", error);
      toast({
        title: "Error",
        description:
          "Failed to initialize workflow templates. Please try again.",
        variant: "destructive",
      });
    },
  });
};

// Helper hooks for template categories
export const useWorkflowTemplateCategories = (userId?: string) => {
  const { data: templates } = useWorkflowTemplates(userId);

  const categories = React.useMemo(() => {
    if (!templates) return [];

    const categorySet = new Set(templates.map((template) => template.category));
    return Array.from(categorySet);
  }, [templates]);

  return categories;
};
