import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { PipelineService } from "@/services/PipelineService";
import { useCreateActivityEntry } from "./useCreateActivityEntry";

interface UpdatePipelineStageParams {
  candidateId: string;
  candidateName: string;
  fromStage: string;
  toStage: string;
}

export const useUpdatePipelineStage = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const createActivityEntry = useCreateActivityEntry();

  return useMutation({
    mutationFn: async ({
      candidateId,
      fromStage,
      toStage,
    }: UpdatePipelineStageParams) => {
      // Update the pipeline candidate's stage
      return await PipelineService.updatePipelineCandidate({
        id: candidateId,
        stage: toStage,
        last_activity: new Date().toISOString(),
      });
    },
    onSuccess: async (data, variables) => {
      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ["pipeline-candidates"] });
      queryClient.invalidateQueries({ queryKey: ["pipeline-stages"] });

      // Create activity entry if we have candidate_id
      if (data.candidate_id) {
        try {
          await createActivityEntry.mutateAsync({
            candidate_id: data.candidate_id,
            activity_type: "status_change",
            title: "Pipeline Stage Updated",
            description: `Moved from ${variables.fromStage} to ${variables.toStage}`,
            metadata: {
              from_stage: variables.fromStage,
              to_stage: variables.toStage,
              status: "completed",
            },
          });
        } catch (activityError) {
          // Log the error but don't show a toast - the main operation succeeded
          console.warn("Failed to create activity entry for pipeline update:", activityError);
        }
      }

      toast({
        title: "Stage Updated",
        description: `${variables.candidateName} moved to ${variables.toStage}`,
      });
    },
    onError: (error) => {
      console.error("Error updating pipeline stage:", error);
      toast({
        title: "Update Failed",
        description: "Failed to update pipeline stage. Please try again.",
        variant: "destructive",
      });
    },
  });
};
