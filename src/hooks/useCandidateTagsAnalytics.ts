import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import {
  CandidateTagsAnalyticsService,
  CandidateTagsStatsData,
  CreateCandidateTagsStatsData,
  UpdateCandidateTagsStatsData,
  TagsChartData,
} from "@/services/CandidateTagsAnalyticsService";

// Tags Stats Hooks
export const useCandidateTagsStats = (userId: string) => {
  // Real-time candidate tags stats subscription
  const { records: tagsStatsData = [], isLoading } = useRealtimeCollection(
    "candidate_tags_stats",
    async () => {
      if (!userId) throw new Error("User ID is required");
      return CandidateTagsAnalyticsService.getCandidateTagsStats(userId);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: tagsStatsData, isLoading, error: null };
};

export const useCandidateTagsChartData = (userId: string) => {
  // Real-time candidate tags chart data subscription
  const { records: chartData = [], isLoading } = useRealtimeCollection(
    "candidate_tags_stats",
    async () => {
      if (!userId) throw new Error("User ID is required");
      const data =
        await CandidateTagsAnalyticsService.getCandidateTagsStats(userId);
      return CandidateTagsAnalyticsService.transformTagsDataForChart(data);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: chartData, isLoading, error: null };
};

export const useCreateCandidateTagsStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (tagData: CreateCandidateTagsStatsData) =>
      CandidateTagsAnalyticsService.createCandidateTagsStats(tagData),
    onSuccess: () => {
      toast({
        title: "Tags Stats Created",
        description:
          "Candidate tags statistics have been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateCandidateTagsStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (updateData: UpdateCandidateTagsStatsData) =>
      CandidateTagsAnalyticsService.updateCandidateTagsStats(updateData),
    onSuccess: () => {
      toast({
        title: "Tags Stats Updated",
        description:
          "Candidate tags statistics have been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteCandidateTagsStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) =>
      CandidateTagsAnalyticsService.deleteCandidateTagsStats(id),
    onSuccess: () => {
      toast({
        title: "Tags Stats Deleted",
        description:
          "Candidate tags statistics have been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Analytics Tags Hooks
export const useAnalyticsTags = (userId: string) => {
  // Real-time analytics tags subscription
  const { records: analyticsTagsData = [], isLoading } = useRealtimeCollection(
    "analytics_tags",
    async () => {
      if (!userId) throw new Error("User ID is required");
      return CandidateTagsAnalyticsService.getAnalyticsTags(userId);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: analyticsTagsData, isLoading, error: null };
};

export const useCreateAnalyticsTags = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (analyticsData: any) =>
      CandidateTagsAnalyticsService.createAnalyticsTags(analyticsData),
    onSuccess: () => {
      toast({
        title: "Analytics Tags Created",
        description:
          "Analytics tags have been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateAnalyticsTags = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: any }) =>
      CandidateTagsAnalyticsService.updateAnalyticsTags(id, updates),
    onSuccess: () => {
      toast({
        title: "Analytics Tags Updated",
        description:
          "Analytics tags have been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteAnalyticsTags = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) =>
      CandidateTagsAnalyticsService.deleteAnalyticsTags(id),
    onSuccess: () => {
      toast({
        title: "Analytics Tags Deleted",
        description:
          "Analytics tags have been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Initialize tags analytics data
export const useInitializeCandidateTagsAnalytics = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (userId: string) => {
      if (!userId) throw new Error("User ID is required");
      return CandidateTagsAnalyticsService.initializeTagsData(userId);
    },
    onSuccess: (data) => {
      toast({
        title: "Tags Analytics Initialized",
        description: `Successfully initialized ${data.length} tag statistics.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}; 