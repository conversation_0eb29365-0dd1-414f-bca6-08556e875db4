import { useState, useEffect } from "react";
import { useDebounce } from "./useDebounce";
import { searchMessages } from "@/utils/searchMessages";
import { Message } from "./useMessages";

interface SearchFilters {
  status?: "unread" | "read" | "archived";
  isStarred?: boolean;
  followUp?: boolean;
  reminder?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export const useMessageSearch = (initialQuery = "", initialFilters: SearchFilters = {}) => {
  const [query, setQuery] = useState(initialQuery);
  const [filters, setFilters] = useState<SearchFilters>(initialFilters);
  const [results, setResults] = useState<Message[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Debounce search query
  const debouncedQuery = useDebounce(query, 300);

  useEffect(() => {
    const performSearch = async () => {
      // Skip search if query is empty and no filters are applied
      if (!debouncedQuery.trim() && Object.keys(filters).length === 0) {
        setResults([]);
        return;
      }

      setIsSearching(true);
      setError(null);

      try {
        const searchResults = await searchMessages(debouncedQuery, filters);
        setResults(searchResults);
      } catch (err) {
        console.error("Message search error:", err);
        setError(err instanceof Error ? err : new Error("Search failed"));
        setResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedQuery, filters]);

  return {
    query,
    setQuery,
    filters,
    setFilters,
    results,
    isSearching,
    error,
  };
};
