import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Tables } from "@/integrations/supabase/types";
import { Benefit } from "@/hooks/useBenefits";
import { Requirement } from "@/hooks/useRequirements";

type JobUpdateData = Partial<Tables<"jobs">> & {
  id: string;
  department?: string;
  job_type?: string;
  location?: string;
  department_name?: string;
  job_type_name?: string;
  location_name?: string;
  location_type?: string;
  location_city?: string;
  location_state?: string;
  location_is_remote?: boolean;
  normalized_requirements?: Requirement[];
  normalized_benefits?: Benefit[];
};

export const useUpdateJob = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updatedJob: JobUpdateData) => {
      // Get the foreign key IDs for the normalized fields if they're being updated
      let departmentData: { id: string } | null = null;
      let jobTypeData: { id: string } | null = null;
      let locationData: { id: string } | null = null;

      // Handle department normalization
      if ('department' in updatedJob && updatedJob.department) {
        console.log("🔍 Looking up department:", updatedJob.department);
        const { data: deptData, error: deptError } = await supabase
          .from("departments")
          .select("id")
          .eq("name", updatedJob.department)
          .single();
        
        if (deptError && deptError.code === 'PGRST116') {
          // Department doesn't exist, create it
          console.log("🔍 Creating new department:", updatedJob.department);
          const { data: newDept, error: createError } = await supabase
            .from("departments")
            .insert({ name: updatedJob.department })
            .select("id")
            .single();
          
          if (createError) {
            console.error("❌ Error creating department:", createError);
          } else {
            departmentData = newDept;
            console.log("✅ New department created:", newDept);
          }
        } else if (deptError) {
          console.error("❌ Department lookup error:", deptError);
        } else {
          departmentData = deptData;
          console.log("✅ Department ID found:", deptData?.id);
        }
      }

      // Handle job_type normalization
      if ('job_type' in updatedJob && updatedJob.job_type) {
        console.log("🔍 Looking up job_type:", updatedJob.job_type);
        const { data: jobTypeResult, error: jobTypeError } = await supabase
          .from("job_types")
          .select("id")
          .eq("name", updatedJob.job_type)
          .single();
        
        if (jobTypeError && jobTypeError.code === 'PGRST116') {
          // Job type doesn't exist, create it
          console.log("🔍 Creating new job type:", updatedJob.job_type);
          const { data: newJobType, error: createError } = await supabase
            .from("job_types")
            .insert({ name: updatedJob.job_type })
            .select("id")
            .single();
          
          if (createError) {
            console.error("❌ Error creating job type:", createError);
          } else {
            jobTypeData = newJobType;
            console.log("✅ New job type created:", newJobType);
          }
        } else if (jobTypeError) {
          console.error("❌ Job type lookup error:", jobTypeError);
        } else {
          jobTypeData = jobTypeResult;
          console.log("✅ Job type ID found:", jobTypeResult?.id);
        }
      }

      // Handle location normalization
      if ('location' in updatedJob && updatedJob.location) {
        console.log("🔍 Looking up location:", updatedJob.location);
        const { data: locData, error: locationError } = await supabase
          .from("locations")
          .select("id")
          .eq("name", updatedJob.location)
          .single();
        
        if (locationError && locationError.code === 'PGRST116') {
          // Location doesn't exist, create it
          console.log("🔍 Creating new location:", updatedJob.location);
          const { data: newLocation, error: createError } = await supabase
            .from("locations")
            .insert({ 
              name: updatedJob.location,
              type: "geographic",
              country: "USA",
              is_remote: false
            })
            .select("id")
            .single();
          
          if (createError) {
            console.error("❌ Error creating location:", createError);
          } else {
            locationData = newLocation;
            console.log("✅ New location created:", newLocation);
          }
        } else if (locationError) {
          console.error("❌ Location lookup error:", locationError);
        } else {
          locationData = locData;
          console.log("✅ Location ID found:", locData?.id);
        }
      }

      // Prepare the update data, excluding the old text fields and view-specific fields
      const { 
        department, 
        job_type, 
        location, 
        department_name, 
        job_type_name, 
        location_name,
        location_type,
        location_city,
        location_state,
        location_is_remote,
        normalized_requirements,
        normalized_benefits,
        search_vector,
        ...updateFields 
      } = updatedJob;
      
      // Add foreign key fields if they were provided
      if (departmentData?.id) (updateFields as any).department_id = departmentData.id;
      if (jobTypeData?.id) (updateFields as any).job_type_id = jobTypeData.id;
      if (locationData?.id) (updateFields as any).location_id = locationData.id;

      const { data, error } = await supabase
        .from("jobs")
        .update({
          ...updateFields,
          updated_at: new Date().toISOString(),
        })
        .eq("id", updatedJob.id)
        .select()
        .single();

      if (error) {
        console.error("❌ Job update error:", error);
        throw error;
      }

      // Update junction table entries for benefits
      if (normalized_benefits !== undefined) {
        // Delete existing benefits
        const { error: deleteBenefitsError } = await supabase
          .from("job_benefits")
          .delete()
          .eq("job_id", updatedJob.id);

        if (deleteBenefitsError) throw deleteBenefitsError;

        // Insert new benefits
        if (normalized_benefits.length > 0) {
          const { error: insertBenefitsError } = await supabase
            .from("job_benefits")
            .insert(
              normalized_benefits.map((benefit) => ({
                job_id: updatedJob.id,
                benefit_id: benefit.id,
              }))
            );

          if (insertBenefitsError) throw insertBenefitsError;
        }
      }

      // Update junction table entries for requirements
      if (normalized_requirements !== undefined) {
        // Delete existing requirements
        const { error: deleteRequirementsError } = await supabase
          .from("job_requirements")
          .delete()
          .eq("job_id", updatedJob.id);

        if (deleteRequirementsError) throw deleteRequirementsError;

        // Insert new requirements
        if (normalized_requirements.length > 0) {
          const { error: insertRequirementsError } = await supabase
            .from("job_requirements")
            .insert(
              normalized_requirements.map((requirement) => ({
                job_id: updatedJob.id,
                requirement_id: requirement.id,
              }))
            );

          if (insertRequirementsError) throw insertRequirementsError;
        }
      }

      return data;
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch job data
      queryClient.invalidateQueries({ queryKey: ['job', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['jobs'] });
      
      // Force a refetch of the jobs list by triggering a custom event
      // This helps with real-time subscription timing issues
      window.dispatchEvent(new CustomEvent('job-updated', { 
        detail: { jobId: variables.id } 
      }));
      
      toast({
        title: "Job Updated",
        description: "The job posting has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error("Error updating job:", error);
      toast({
        title: "Error",
        description: "Failed to update job. Please try again.",
        variant: "destructive",
      });
    },
  });
};
