import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export interface WorkflowSchedule {
  id: string;
  workflow_id: string;
  cron_schedule: string;
  context: any;
  is_active: boolean;
  last_run: string | null;
  next_run: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export const useWorkflowSchedules = (workflowId?: string) => {
  const { user } = useAuth();

  // Real-time workflow schedules subscription
  const { records: schedules = [], isLoading } = useRealtimeCollection(
    "workflow_schedules",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      try {
        let query = supabase
          .from("workflow_schedules")
          .select("*")
          .eq("created_by", user.id);

        if (workflowId) {
          query = query.eq("workflow_id", workflowId);
        }

        const { data, error } = await query
          .eq("is_active", true)
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching workflow schedules:", error);
          return [];
        }

        return data || [];
      } catch (error) {
        console.error("Error in useWorkflowSchedules:", error);
        return [];
      }
    },
    "public",
    workflowId ? `workflow_id=eq.${workflowId}` : `created_by=eq.${user?.id}`,
  );

  return { data: schedules, isLoading, error: null };
};

export const useCreateWorkflowSchedule = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (scheduleData: {
      workflow_id: string;
      cron_schedule: string;
      context?: any;
      is_active?: boolean;
    }) => {
      if (!user) {
        throw new Error(
          "User must be authenticated to create a workflow schedule",
        );
      }

      // Calculate next run time (simplified)
      const nextRun = new Date();
      nextRun.setDate(nextRun.getDate() + 1); // Just set to tomorrow for simplicity

      // Parse cron expression to get a more accurate next run time
      try {
        // Simple cron parsing for common patterns
        const cronParts = scheduleData.cron_schedule.split(" ");
        if (cronParts.length === 5) {
          const [minute, hour, dayOfMonth, month, dayOfWeek] = cronParts;

          // For daily at specific time (e.g., "0 9 * * *")
          if (
            dayOfMonth === "*" &&
            month === "*" &&
            dayOfWeek === "*" &&
            hour !== "*"
          ) {
            nextRun.setHours(parseInt(hour, 10));
            nextRun.setMinutes(parseInt(minute, 10));
            nextRun.setSeconds(0);

            // If the time has already passed today, set to tomorrow
            if (nextRun < new Date()) {
              nextRun.setDate(nextRun.getDate() + 1);
            }
          }
        }
      } catch (error) {
        console.error("Error parsing cron expression:", error);
        // Fall back to default next run time
      }

      const { data, error } = await supabase
        .from("workflow_schedules")
        .insert({
          workflow_id: scheduleData.workflow_id,
          cron_schedule: scheduleData.cron_schedule,
          context: scheduleData.context || {},
          is_active:
            scheduleData.is_active !== undefined
              ? scheduleData.is_active
              : true,
          next_run: nextRun.toISOString(),
          created_by: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Schedule Created",
        description: "Your workflow schedule has been successfully created.",
      });
    },
    onError: (error) => {
      console.error("Error creating workflow schedule:", error);
      toast({
        title: "Error",
        description: "Failed to create workflow schedule. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateWorkflowSchedule = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (scheduleData: {
      id: string;
      cron_schedule?: string;
      context?: any;
      is_active?: boolean;
      next_run?: string;
    }) => {
      const { id, ...updateData } = scheduleData;

      const { data, error } = await supabase
        .from("workflow_schedules")
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Schedule Updated",
        description: "Your workflow schedule has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error("Error updating workflow schedule:", error);
      toast({
        title: "Error",
        description: "Failed to update workflow schedule. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteWorkflowSchedule = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("workflow_schedules")
        .delete()
        .eq("id", id);

      if (error) throw error;
      return id;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Schedule Deleted",
        description: "Your workflow schedule has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error("Error deleting workflow schedule:", error);
      toast({
        title: "Error",
        description: "Failed to delete workflow schedule. Please try again.",
        variant: "destructive",
      });
    },
  });
};
