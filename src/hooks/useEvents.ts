import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useAuth } from "@/contexts/AuthContext";
import { EventsService } from "@/services";

export interface Event {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string; // Keep for backward compatibility
  location_name?: string; // New normalized field
  location_type?: string; // New normalized field
  location_city?: string; // New normalized field
  location_state?: string; // New normalized field
  location_is_remote?: boolean; // New normalized field
  meeting_link?: string;
  event_type: "meeting" | "interview" | "call" | "presentation" | "other"; // Keep for backward compatibility
  event_type_name?: string; // New normalized field
  category: "general" | "recruitment" | "client" | "internal";
  priority: "low" | "medium" | "high"; // Keep for backward compatibility
  priority_name?: string; // New normalized field
  priority_level?: number; // New normalized field
  created_at: string;
  updated_at: string;
}

export const useEvents = () => {
  const { user } = useAuth();

  // Real-time events subscription
  const { records: events = [], isLoading } = useRealtimeCollection(
    "events",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      try {
        return await EventsService.getEvents(user.id);
      } catch (error) {
        console.error("Error in useEvents:", error);
        // Return empty array instead of throwing to prevent UI crashes
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: events, isLoading, error: null };
};

// Export the useCreateEvent hook
export { useCreateEvent } from "./useCreateEvent";
