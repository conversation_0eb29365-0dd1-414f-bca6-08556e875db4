import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { JobType } from "./useJobTypes";

interface CreateJobTypeData {
  name: string;
}

export const useCreateJobType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateJobTypeData): Promise<JobType> => {
      const { data: newJobType, error } = await supabase
        .from("job_types")
        .insert([data])
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return newJobType;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['job_types'] });
    },
  });
}; 