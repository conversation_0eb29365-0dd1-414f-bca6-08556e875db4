import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface AnalyticsSource {
  id: string;
  user_id: string;
  source: string;
  effectiveness: number;
  hires: number;
  created_at: string;
  updated_at: string;
}

const transformSourcesForChart = (sources: AnalyticsSource[]) => {
  if (!sources || !Array.isArray(sources)) return [];

  return sources.map((source) => ({
    source: source.source,
    effectiveness: source.effectiveness,
    hires: source.hires,
    candidates: source.hires * 2, // Estimate based on hire count
    qualified: source.hires,
    qualification_rate: source.effectiveness.toFixed(1),
  }));
};

export const useAnalyticsSources = () => {
  const { user } = useAuth();

  // Real-time sources analytics subscription
  const { records: sourcesData = [], isLoading } = useRealtimeCollection(
    "analytics_sources",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      const { data, error } = await supabase
        .from("analytics_sources")
        .select("*")
        .eq("user_id", user.id);

      if (error) {
        console.error("Error fetching analytics sources:", error);
        throw error;
      }

      return transformSourcesForChart(data || []);
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return {
    data: sourcesData,
    isLoading,
    error: null,
  };
};
