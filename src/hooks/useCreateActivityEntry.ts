import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

interface CreateActivityEntryData {
  candidate_id: string;
  activity_type: string;
  title: string;
  description?: string;
  metadata?: any;
}

export const useCreateActivityEntry = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (entryData: CreateActivityEntryData) => {
      if (!user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("candidate_activities")
        .insert({
          candidate_id: entryData.candidate_id,
          user_id: user.id,
          activity_type: entryData.activity_type,
          title: entryData.title,
          description: entryData.description,
          metadata: entryData.metadata || {},
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      // Success handled by real-time subscription
    },
    onError: (error) => {
      console.error("Error creating activity entry:", error);
      toast({
        title: "Error",
        description: "Failed to create activity entry. Please try again.",
        variant: "destructive",
      });
    },
  });
}; 