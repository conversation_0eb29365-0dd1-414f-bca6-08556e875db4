/**
 * React hook for subscribing to global invalidation events
 *
 * This hook allows components to automatically refresh their data
 * when related changes occur elsewhere in the application.
 */

import { useEffect, useCallback, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  globalInvalidationService,
  InvalidationEvent,
  InvalidationSubscriber,
} from "@/services/GlobalInvalidationService";

interface UseGlobalInvalidationOptions {
  scopes: string[];
  onInvalidate?: (event: InvalidationEvent) => void;
  debounceMs?: number;
  enabled?: boolean;
}

interface UseGlobalInvalidationReturn {
  invalidate: (
    scopes: string[],
    reason: string,
    metadata?: Record<string, any>,
  ) => void;
  isInitialized: boolean;
}

export function useGlobalInvalidation(
  options: UseGlobalInvalidationOptions,
): UseGlobalInvalidationReturn {
  const { user } = useAuth();
  const { scopes, onInvalidate, debounceMs = 100, enabled = true } = options;
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isInitializedRef = useRef(false);

  // Debounced invalidation handler
  const debouncedHandler = useCallback(
    (event: InvalidationEvent) => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        onInvalidate?.(event);
      }, debounceMs);
    },
    [onInvalidate, debounceMs],
  );

  // Initialize the global invalidation service
  useEffect(() => {
    if (!user || !enabled || isInitializedRef.current) return;

    const initService = async () => {
      try {
        await globalInvalidationService.initialize(user.id);
        isInitializedRef.current = true;
        console.log(
          "✅ Global invalidation service initialized for user:",
          user.id,
        );
      } catch (error) {
        console.error(
          "Failed to initialize global invalidation service:",
          error,
        );
      }
    };

    initService();

    // Cleanup on unmount
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [user, enabled]);

  // Subscribe to invalidation events
  useEffect(() => {
    if (!user || !enabled || !isInitializedRef.current || !scopes.length)
      return;

    console.log(`📡 Subscribing to invalidation scopes:`, scopes);

    const unsubscribe = globalInvalidationService.subscribe(
      scopes,
      debouncedHandler,
    );

    return () => {
      console.log(`🔇 Unsubscribing from invalidation scopes:`, scopes);
      unsubscribe();
    };
  }, [user, enabled, scopes, debouncedHandler]);

  // Manual invalidation function
  const invalidate = useCallback(
    (
      scopesToInvalidate: string[],
      reason: string,
      metadata?: Record<string, any>,
    ) => {
      globalInvalidationService.invalidate(
        scopesToInvalidate,
        reason,
        metadata,
      );
    },
    [],
  );

  return {
    invalidate,
    isInitialized: isInitializedRef.current,
  };
}

/**
 * Convenience hook for components that just need to refresh when their data changes
 */
export function useAutoRefresh(
  scopes: string[],
  refreshCallback: () => void,
  options?: {
    debounceMs?: number;
    enabled?: boolean;
  },
) {
  return useGlobalInvalidation({
    scopes,
    onInvalidate: (event) => {
      console.log(`🔄 Auto-refreshing due to:`, event.reason);
      refreshCallback();
    },
    debounceMs: options?.debounceMs,
    enabled: options?.enabled,
  });
}

/**
 * Hook for components that need to know when specific data types change
 */
export function useDataInvalidation(
  dataTypes: (
    | "candidates"
    | "jobs"
    | "events"
    | "notifications"
    | "messages"
  )[],
  onDataChange: (changedType: string, event: InvalidationEvent) => void,
  options?: {
    debounceMs?: number;
    enabled?: boolean;
  },
) {
  const scopes = dataTypes.flatMap((type) => {
    switch (type) {
      case "candidates":
        return [
          "candidates",
          "candidate-interviews",
          "candidate-communications",
        ];
      case "jobs":
        return ["jobs", "job-matches"];
      case "events":
        return ["calendar", "events", "timeline"];
      case "notifications":
        return ["notifications", "notification-center", "unread-count"];
      case "messages":
        return ["messages", "candidate-communications"];
      default:
        return [type];
    }
  });

  return useGlobalInvalidation({
    scopes,
    onInvalidate: (event) => {
      // Determine which data type changed based on scopes
      const changedType = dataTypes.find((type) => {
        const typeScopes = {
          candidates: [
            "candidates",
            "candidate-interviews",
            "candidate-communications",
          ],
          jobs: ["jobs", "job-matches"],
          events: ["calendar", "events", "timeline"],
          notifications: [
            "notifications",
            "notification-center",
            "unread-count",
          ],
          messages: ["messages", "candidate-communications"],
        }[type] || [type];

        return event.scope.some((scope) => typeScopes.includes(scope));
      });

      if (changedType) {
        onDataChange(changedType, event);
      }
    },
    debounceMs: options?.debounceMs,
    enabled: options?.enabled,
  });
}
