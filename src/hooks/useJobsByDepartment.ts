import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";

export interface DepartmentCount {
  name: string;
  count: number;
}

export const useJobsByDepartment = () => {
  const { user } = useAuth();

  // Subscribe to jobs; refetch the view on changes
  const { records: departments = [], isLoading } = useRealtimeCollection<DepartmentCount>(
    "jobs",
    async (): Promise<DepartmentCount[]> => {
      if (!user) return [];
      const { data, error } = await supabase
        .from("jobs_by_department")
        .select("name, count")
        .eq("user_id", user.id)
        .order("count", { ascending: false });

      if (error) {
        console.error("Error fetching jobs_by_department:", error);
        throw error;
      }

      // Map to expected shape
      return (data || []).map((row: any) => ({ name: row.name, count: row.count }));
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: departments, isLoading, error: null };
};


