import { useMemo } from "react";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface AnalyticsSalary {
  id: string;
  role: string;
  market_salary: number;
  recommended_salary: number;
  experience_level?: string;
  location?: string;
  industry?: string;
  created_at: string;
  updated_at: string;
}

const transformSalaryForChart = (data: any[]) => {
  if (!data || !Array.isArray(data)) return [];

  return data.map((item) => ({
    ...item,
    // Add any necessary transformations here
  }));
};

export const useAnalyticsSalary = () => {
  const { user } = useAuth();

  // Real-time salary analytics subscription
  const { records: salaryData = [], isLoading } = useRealtimeCollection(
    "analytics_salary",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      const { data, error } = await supabase
        .from("analytics_salary")
        .select("*")
        .eq("user_id", user.id);

      if (error) {
        console.error("Error fetching analytics salary:", error);
        throw error;
      }

      return data || [];
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  // Memoize the transformed data to prevent infinite re-renders
  const transformedData = useMemo(() => {
    if (!salaryData) return [];
    return transformSalaryForChart(salaryData);
  }, [salaryData]);

  return {
    data: transformedData,
    isLoading,
    error: null,
  };
};

// Helper function to create mock salary data if needed
function createMockSalary(): AnalyticsSalary[] {
  const now = new Date().toISOString();
  return [
    {
      id: "1",
      role: "Software Engineer",
      market_salary: 120000,
      recommended_salary: 132000,
      created_at: now,
      updated_at: now,
    },
    {
      id: "2",
      role: "Product Manager",
      market_salary: 130000,
      recommended_salary: 143000,
      created_at: now,
      updated_at: now,
    },
    {
      id: "3",
      role: "Data Scientist",
      market_salary: 115000,
      recommended_salary: 126500,
      created_at: now,
      updated_at: now,
    },
    {
      id: "4",
      role: "UX Designer",
      market_salary: 95000,
      recommended_salary: 104500,
      created_at: now,
      updated_at: now,
    },
  ];
}
