import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { WorkflowExecutionEngine } from "@/components/ai/workflow/WorkflowExecutionEngine";

interface ExecutionLog {
  nodeId: string;
  status: "success" | "error" | "pending" | "skipped";
  message: string;
  timestamp: Date;
  data?: any;
}

interface ExecutionContext {
  [key: string]: any;
}

export const useWorkflowExecution = (initialWorkflowId: string = "") => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [currentNodeIndex, setCurrentNodeIndex] = useState(0);
  const [executionPath, setExecutionPath] = useState<string[]>([]);
  const [executionLogs, setExecutionLogs] = useState<ExecutionLog[]>([]);
  const [progress, setProgress] = useState(0);
  const [executionContext, setExecutionContext] = useState<ExecutionContext>(
    {},
  );
  const [currentWorkflowId, setCurrentWorkflowId] =
    useState<string>(initialWorkflowId);

  const executeWorkflow = async (
    workflowIdToExecute: string = "",
    context: ExecutionContext = {},
  ) => {
    // Use provided workflowId or fall back to the current one
    const targetWorkflowId = workflowIdToExecute || currentWorkflowId;

    if (!targetWorkflowId || !user) {
      toast({
        title: "Error",
        description:
          "Cannot execute workflow. Missing workflow ID or user authentication.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsRunning(true);
      setCurrentWorkflowId(targetWorkflowId);
      setCurrentNodeIndex(0);
      setExecutionPath([]);
      setExecutionLogs([]);
      setProgress(0);
      setExecutionContext(context);

      // Fetch the workflow configuration
      const { data: workflow, error } = await supabase
        .from("workflow_configurations")
        .select("*")
        .eq("id", targetWorkflowId)
        .eq("created_by", user.id)
        .single();

      if (error) throw error;

      if (
        !workflow ||
        !workflow.config ||
        !workflow.config.nodes ||
        !workflow.config.edges
      ) {
        throw new Error("Invalid workflow configuration");
      }

      // Create a new workflow execution engine
      const engine = new WorkflowExecutionEngine(
        targetWorkflowId,
        user.id,
        workflow.config.nodes,
        workflow.config.edges,
        context,
      );

      // Execute the workflow
      const result = await engine.execute();

      try {
        // Update state with execution results
        setExecutionPath(result.executionPath || []);
        setExecutionLogs(result.logs || []);
        setProgress(100);

        if (result.success) {
          toast({
            title: "Workflow Completed",
            description: "The workflow execution has completed successfully.",
          });
        } else {
          toast({
            title: "Workflow Error",
            description: result.error
              ? result.error.message
              : "An error occurred during workflow execution.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error updating state with execution results:", error);
        toast({
          title: "Workflow Error",
          description: "An error occurred while processing workflow results.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error executing workflow:", error);

      toast({
        title: "Workflow Error",
        description:
          error instanceof Error
            ? error.message
            : "An error occurred during workflow execution.",
        variant: "destructive",
      });
    } finally {
      setIsRunning(false);
    }
  };

  return {
    isRunning,
    currentNodeIndex,
    executionPath,
    executionLogs,
    progress,
    executionContext,
    executeWorkflow,
    currentWorkflowId,
  };
};
