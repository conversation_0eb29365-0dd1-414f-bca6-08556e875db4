import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useMutation } from "@tanstack/react-query";
import {
  BudgetService,
  BudgetData,
  CreateBudgetData,
  UpdateBudgetData,
} from "@/services/BudgetService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

export const useBudget = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Real-time budget data subscription
  const { records: budgetData = [], isLoading } = useRealtimeCollection(
    "budget_data",
    async () => {
      if (!user?.id) throw new Error("User not authenticated");

      // Try to get existing data, or initialize with defaults
      const existingData = await BudgetService.getBudgetData(user.id);
      if (existingData.length > 0) {
        return existingData;
      }

      // Initialize with default budget data if none exists
      const totalBudget = 500000;
      const spentAmount = 320000;

      const defaultBudgetCategories: CreateBudgetData[] = [
        {
          user_id: user.id,
          total_budget: totalBudget,
          spent_amount: spentAmount,
          category: "Job Postings",
          amount: 120000,
          percentage: 37.5,
        },
        {
          user_id: user.id,
          total_budget: totalBudget,
          spent_amount: spentAmount,
          category: "Agency Fees",
          amount: 100000,
          percentage: 31.25,
        },
        {
          user_id: user.id,
          total_budget: totalBudget,
          spent_amount: spentAmount,
          category: "Tools & Software",
          amount: 60000,
          percentage: 18.75,
        },
        {
          user_id: user.id,
          total_budget: totalBudget,
          spent_amount: spentAmount,
          category: "Events",
          amount: 40000,
          percentage: 12.5,
        },
      ];

      // Create default budget data
      const createdData = await Promise.all(
        defaultBudgetCategories.map((budget) =>
          BudgetService.createBudgetData(budget),
        ),
      );

      return createdData;
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: budgetData, isLoading, error: null };
};

export const useBudgetSummary = () => {
  const { user } = useAuth();

  // Subscribe to job_expenses changes to trigger budget summary recalculation
  const { records: budgetData = [], isLoading } = useRealtimeCollection(
    "job_expenses", // Subscribe to job_expenses to detect when budget needs recalculation
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      const summary = await BudgetService.getBudgetSummary(user.id);
      return [summary]; // Wrap in array for useRealtimeCollection
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: budgetData[0] || null, isLoading, error: null };
};

export const useCreateBudget = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateBudgetData) =>
      BudgetService.createBudgetData(data),
    onSuccess: () => {
      toast({
        title: "Budget Created",
        description: "Budget category has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateBudget = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: UpdateBudgetData) =>
      BudgetService.updateBudgetData(data),
    onSuccess: () => {
      toast({
        title: "Budget Updated",
        description: "Budget data has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
