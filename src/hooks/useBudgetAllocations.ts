import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  BudgetAllocationService,
  BudgetAllocation,
  CreateBudgetAllocationData,
  UpdateBudgetAllocationData,
  BudgetSummaryData,
} from "@/services/BudgetAllocationService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

export const useBudgetAllocations = () => {
  const { user } = useAuth();

  // Real-time budget allocations subscription
  const { records: budgetAllocations = [], isLoading } = useRealtimeCollection(
    "budget_allocations",
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      
      // Try to get existing allocations
      const existingAllocations = await BudgetAllocationService.getBudgetAllocations(user.id);
      
      // If no allocations exist, create default ones
      if (existingAllocations.length === 0) {
        return await BudgetAllocationService.initializeDefaultBudgetAllocations(user.id);
      }
      
      return existingAllocations;
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: budgetAllocations, isLoading, error: null };
};

export const useEnhancedBudgetSummary = () => {
  const { user } = useAuth();

  // Subscribe to job_expenses changes since that's what affects budget calculations
  const { records: budgetSummaryData = [], isLoading } = useRealtimeCollection(
    "job_expenses", // Subscribe to job_expenses changes to trigger budget recalculation
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      return await BudgetAllocationService.getBudgetSummary(user.id);
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: budgetSummaryData, isLoading, error: null };
};

export const useBudgetUtilization = () => {
  const { user } = useAuth();

  // Subscribe to job_expenses changes since that's what affects budget utilization
  const { records: utilizationData = [], isLoading } = useRealtimeCollection(
    "job_expenses", // Subscribe to job_expenses changes to trigger utilization recalculation
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      const utilization = await BudgetAllocationService.getTotalBudgetUtilization(user.id);
      return [utilization]; // Wrap in array for useRealtimeCollection
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: utilizationData[0] || null, isLoading, error: null };
};

export const useCreateBudgetAllocation = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateBudgetAllocationData) =>
      BudgetAllocationService.createBudgetAllocation(data),
    onSuccess: () => {
      toast({
        title: "Budget Allocation Created",
        description: "Budget allocation has been created successfully.",
      });
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ["budget-allocations"] });
      queryClient.invalidateQueries({ queryKey: ["budget-summary"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateBudgetAllocation = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateBudgetAllocationData) =>
      BudgetAllocationService.updateBudgetAllocation(data),
    onSuccess: () => {
      toast({
        title: "Budget Allocation Updated",
        description: "Budget allocation has been updated successfully.",
      });
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ["budget-allocations"] });
      queryClient.invalidateQueries({ queryKey: ["budget-summary"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteBudgetAllocation = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => BudgetAllocationService.deleteBudgetAllocation(id),
    onSuccess: () => {
      toast({
        title: "Budget Allocation Deleted",
        description: "Budget allocation has been deleted successfully.",
      });
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ["budget-allocations"] });
      queryClient.invalidateQueries({ queryKey: ["budget-summary"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
