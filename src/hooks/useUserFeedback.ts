import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import {
  UserFeedbackService,
  UserFeedback,
  CreateUserFeedbackData,
  UpdateUserFeedbackData,
  FeedbackFilters,
  FeedbackStats,
} from "@/services/UserFeedbackService";
import { Json } from "@/integrations/supabase/types";
import {
  useRealtimeCollection,
  useRealtimeRecord,
} from "@/hooks/useRealtimeSubscription";
import { useMemo } from "react";

export const useUserFeedback = (filters?: FeedbackFilters) => {
  const { user } = useAuth();

  // Real-time user feedback subscription
  const { records: allFeedback = [], isLoading } = useRealtimeCollection(
    "user_feedback",
    async () => {
      if (!user) return [];

      try {
        return await UserFeedbackService.getUserFeedback(user.id, filters);
      } catch (error) {
        console.error("Error in useUserFeedback:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  // Apply filters to feedback (since real-time gives us all records)
  const filteredFeedback = useMemo(() => {
    if (!filters) return allFeedback;

    return allFeedback.filter((feedback) => {
      if (
        filters.feedback_type &&
        feedback.feedback_type !== filters.feedback_type
      )
        return false;
      if (
        filters.feature_name &&
        feedback.feature_name !== filters.feature_name
      )
        return false;
      if (
        filters.date_from &&
        new Date(feedback.created_at) < new Date(filters.date_from)
      )
        return false;
      if (
        filters.date_to &&
        new Date(feedback.created_at) > new Date(filters.date_to)
      )
        return false;
      if (filters.status && feedback.status !== filters.status) return false;
      if (
        filters.rating_min !== undefined &&
        (feedback.rating || 0) < filters.rating_min
      )
        return false;
      if (
        filters.rating_max !== undefined &&
        (feedback.rating || 0) > filters.rating_max
      )
        return false;
      return true;
    });
  }, [allFeedback, filters]);

  return { data: filteredFeedback, isLoading, error: null };
};

export const useAllFeedback = (filters?: FeedbackFilters, limit?: number) => {
  // Real-time all feedback subscription (admin/moderator view)
  const { records: allFeedback = [], isLoading } = useRealtimeCollection(
    "user_feedback",
    async () => {
      try {
        return await UserFeedbackService.getAllFeedback(filters, limit);
      } catch (error) {
        console.error("Error in useAllFeedback:", error);
        return [];
      }
    },
    "public",
  );

  // Apply filters to feedback (since real-time gives us all records)
  const filteredFeedback = useMemo(() => {
    if (!filters && !limit) return allFeedback;

    let filtered = allFeedback;

    if (filters) {
      filtered = allFeedback.filter((feedback) => {
        if (
          filters.feedback_type &&
          feedback.feedback_type !== filters.feedback_type
        )
          return false;
        if (
          filters.feature_name &&
          feedback.feature_name !== filters.feature_name
        )
          return false;
        if (
          filters.date_from &&
          new Date(feedback.created_at) < new Date(filters.date_from)
        )
          return false;
        if (
          filters.date_to &&
          new Date(feedback.created_at) > new Date(filters.date_to)
        )
          return false;
        if (filters.status && feedback.status !== filters.status) return false;
        if (
          filters.rating_min !== undefined &&
          (feedback.rating || 0) < filters.rating_min
        )
          return false;
        if (
          filters.rating_max !== undefined &&
          (feedback.rating || 0) > filters.rating_max
        )
          return false;
        return true;
      });
    }

    if (limit) {
      filtered = filtered.slice(0, limit);
    }

    return filtered;
  }, [allFeedback, filters, limit]);

  return { data: filteredFeedback, isLoading, error: null };
};

export const useFeedback = (feedbackId: string) => {
  // Real-time individual feedback subscription
  const { record: feedbackData, isLoading } = useRealtimeRecord(
    "user_feedback",
    feedbackId,
    "public",
  );

  return { data: feedbackData, isLoading, error: null };
};

export const useCreateFeedback = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (feedbackData: CreateUserFeedbackData) => {
      return await UserFeedbackService.createFeedback(feedbackData);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Feedback Submitted",
        description:
          "Your feedback has been successfully submitted. Thank you!",
      });
    },
    onError: (error) => {
      console.error("Error creating feedback:", error);
      toast({
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateFeedback = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      feedbackId,
      updateData,
    }: {
      feedbackId: string;
      updateData: UpdateUserFeedbackData;
    }) => {
      return await UserFeedbackService.updateFeedback(feedbackId, updateData);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Feedback Updated",
        description: "Feedback has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error("Error updating feedback:", error);
      toast({
        title: "Error",
        description: "Failed to update feedback. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateFeedbackStatus = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      feedbackId,
      status,
    }: {
      feedbackId: string;
      status:
        | "pending"
        | "reviewing"
        | "in_progress"
        | "resolved"
        | "dismissed";
    }) => {
      return await UserFeedbackService.updateFeedbackStatus(feedbackId, status);
    },
    onSuccess: (_, variables) => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Status Updated",
        description: `Feedback status updated to ${variables.status}.`,
      });
    },
    onError: (error) => {
      console.error("Error updating feedback status:", error);
      toast({
        title: "Error",
        description: "Failed to update feedback status. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteFeedback = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (feedbackId: string) => {
      return await UserFeedbackService.deleteFeedback(feedbackId);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Feedback Deleted",
        description: "Feedback has been successfully deleted.",
      });
    },
    onError: (error) => {
      console.error("Error deleting feedback:", error);
      toast({
        title: "Error",
        description: "Failed to delete feedback. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useFeedbackStats = (
  userId?: string,
  dateRange?: { start: string; end: string },
) => {
  // Real-time feedback stats subscription
  const { records: feedbackStatsData = [], isLoading } = useRealtimeCollection(
    "user_feedback",
    async () => {
      try {
        const stats = await UserFeedbackService.getFeedbackStats(
          userId,
          dateRange,
        );
        return stats ? [stats] : []; // Wrap in array for useRealtimeCollection
      } catch (error) {
        console.error("Error in useFeedbackStats:", error);
        return [];
      }
    },
    "public",
    userId ? `user_id=eq.${userId}` : undefined,
  );

  return { data: feedbackStatsData[0] || null, isLoading, error: null };
};

export const useFeedbackForFeature = (featureName: string, limit?: number) => {
  // Real-time feedback for feature subscription
  const { records: allFeatureFeedback = [], isLoading } = useRealtimeCollection(
    "user_feedback",
    async () => {
      if (!featureName) return [];

      try {
        return await UserFeedbackService.getFeedbackForFeature(
          featureName,
          limit,
        );
      } catch (error) {
        console.error("Error in useFeedbackForFeature:", error);
        return [];
      }
    },
    "public",
    `feature_name=eq.${featureName}`,
  );

  // Apply limit if specified
  const limitedFeedback = useMemo(() => {
    return limit ? allFeatureFeedback.slice(0, limit) : allFeatureFeedback;
  }, [allFeatureFeedback, limit]);

  return { data: limitedFeedback, isLoading, error: null };
};

export const useSubmitBugReport = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      userId,
      featureName,
      message,
      metadata,
    }: {
      userId: string;
      featureName: string;
      message: string;
      metadata?: Json;
    }) => {
      return await UserFeedbackService.submitBugReport(
        userId,
        featureName,
        message,
        metadata,
      );
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Bug Report Submitted",
        description:
          "Your bug report has been submitted. We'll investigate this issue.",
      });
    },
    onError: (error) => {
      console.error("Error submitting bug report:", error);
      toast({
        title: "Error",
        description: "Failed to submit bug report. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useSubmitFeatureRequest = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      userId,
      featureName,
      message,
      priority,
      metadata,
    }: {
      userId: string;
      featureName: string;
      message: string;
      priority?: number;
      metadata?: Json;
    }) => {
      return await UserFeedbackService.submitFeatureRequest(
        userId,
        featureName,
        message,
        priority,
        metadata,
      );
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Feature Request Submitted",
        description:
          "Your feature request has been submitted. Thank you for the suggestion!",
      });
    },
    onError: (error) => {
      console.error("Error submitting feature request:", error);
      toast({
        title: "Error",
        description: "Failed to submit feature request. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useSubmitGeneralFeedback = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      userId,
      featureName,
      message,
      rating,
      feedbackType,
    }: {
      userId: string;
      featureName: string;
      message: string;
      rating: number;
      feedbackType?: "improvement" | "praise" | "complaint";
    }) => {
      return await UserFeedbackService.submitGeneralFeedback(
        userId,
        featureName,
        message,
        rating,
        feedbackType,
      );
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Feedback Submitted",
        description:
          "Thank you for your feedback! It helps us improve the platform.",
      });
    },
    onError: (error) => {
      console.error("Error submitting general feedback:", error);
      toast({
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useTrendingTopics = (daysBack?: number, limit?: number) => {
  // Real-time trending topics subscription
  const { records: trendingTopicsData = [], isLoading } = useRealtimeCollection(
    "user_feedback",
    async () => {
      try {
        return await UserFeedbackService.getTrendingTopics(daysBack, limit);
      } catch (error) {
        console.error("Error in useTrendingTopics:", error);
        return [];
      }
    },
    "public",
  );

  return { data: trendingTopicsData, isLoading, error: null };
};

// Convenience hooks for common feedback scenarios
export const useQuickBugReport = () => {
  const { user } = useAuth();
  const submitBugReport = useSubmitBugReport();

  return (featureName: string, message: string, metadata?: Json) => {
    if (!user) {
      console.error("User not authenticated");
      return Promise.reject("User not authenticated");
    }

    return submitBugReport.mutateAsync({
      userId: user.id,
      featureName,
      message,
      metadata,
    });
  };
};

export const useQuickFeatureRequest = () => {
  const { user } = useAuth();
  const submitFeatureRequest = useSubmitFeatureRequest();

  return (featureName: string, message: string, priority?: number) => {
    if (!user) {
      console.error("User not authenticated");
      return Promise.reject("User not authenticated");
    }

    return submitFeatureRequest.mutateAsync({
      userId: user.id,
      featureName,
      message,
      priority,
    });
  };
};

export const useQuickFeedback = () => {
  const { user } = useAuth();
  const submitGeneralFeedback = useSubmitGeneralFeedback();

  return (
    featureName: string,
    message: string,
    rating: number,
    feedbackType: "improvement" | "praise" | "complaint" = "improvement",
  ) => {
    if (!user) {
      console.error("User not authenticated");
      return Promise.reject("User not authenticated");
    }

    return submitGeneralFeedback.mutateAsync({
      userId: user.id,
      featureName,
      message,
      rating,
      feedbackType,
    });
  };
};
