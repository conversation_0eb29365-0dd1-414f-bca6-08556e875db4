import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface CandidateJobData {
  id: string;
  job_id: string;
  stage: string;
  created_at: string;
  job_title?: string;
  job_department?: string;
}

export const useCandidateJobs = (candidateId: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["candidateJobs", candidateId],
    queryFn: async () => {
      if (!user || !candidateId) return [];

      const { data, error } = await supabase
        .from("pipeline_candidates_with_details")
        .select("id, job_id, stage, created_at, job_title, job_department")
        .eq("user_id", user.id)
        .eq("candidate_id", candidateId);

      if (error) throw error;
      return (data || []) as CandidateJobData[];
    },
    enabled: !!user && !!candidateId,
  });
};
