import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface JobApplicantCount {
  job_id: string;
  count: number;
}

export const useJobApplicantCounts = (jobIds?: string[]) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["job-applicant-counts", jobIds],
    queryFn: async () => {
      if (!user?.id) {
        return new Map<string, number>();
      }

      let query = supabase
        .from("pipeline_candidates_with_details")
        .select("job_id")
        .eq("user_id", user.id);

      // If specific job IDs are provided, filter by them
      if (jobIds && jobIds.length > 0) {
        query = query.in("job_id", jobIds);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching applicant counts:", error);
        throw error;
      }

      // Count applicants per job
      const countMap = new Map<string, number>();
      if (data) {
        data.forEach((row) => {
          const currentCount = countMap.get(row.job_id) || 0;
          countMap.set(row.job_id, currentCount + 1);
        });
      }

      return countMap;
    },
    enabled: !!user?.id,
    refetchInterval: 30000, // Refetch every 30 seconds for near real-time updates
  });
};

// Hook for a single job's applicant count
export const useJobApplicantCount = (jobId?: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["job-applicant-count", jobId],
    queryFn: async () => {
      if (!user?.id || !jobId) {
        return 0;
      }

      const { count, error } = await supabase
        .from("pipeline_candidates_with_details")
        .select("*", { count: "exact", head: true })
        .eq("user_id", user.id)
        .eq("job_id", jobId);

      if (error) {
        console.error("Error fetching applicant count:", error);
        throw error;
      }

      return count || 0;
    },
    enabled: !!user?.id && !!jobId,
    refetchInterval: 30000, // Refetch every 30 seconds for near real-time updates
  });
};
