import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export interface Requirement {
  id: string;
  name: string;
  category?: string;
}

export function useRequirements() {
  return useQuery({
    queryKey: ["requirements"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("requirements")
        .select("*")
        .order("name");

      if (error) throw error;

      return data.map((requirement) => ({
        id: requirement.id,
        name: requirement.name,
        category: requirement.category,
      })) as Requirement[];
    },
  });
}

export function useCreateRequirement() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ name, category }: { name: string; category?: string }) => {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("requirements")
        .insert({
          name,
          category,
          user_id: userData.user.id,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id,
        name: data.name,
        category: data.category,
      } as Requirement;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["requirements"] });
      toast({
        title: "Success",
        description: "Requirement created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useUpdateRequirement() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      id,
      name,
      category,
    }: {
      id: string;
      name: string;
      category?: string;
    }) => {
      const { error } = await supabase
        .from("requirements")
        .update({ name, category })
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["requirements"] });
      toast({
        title: "Success",
        description: "Requirement updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useDeleteRequirement() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("requirements")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["requirements"] });
      toast({
        title: "Success",
        description: "Requirement deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useJobRequirements(jobId: string) {
  return useQuery({
    queryKey: ["job-requirements", jobId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("job_requirements")
        .select("requirement_id, requirements!inner(id, name, category)")
        .eq("job_id", jobId);

      if (error) throw error;

      return data.map((item: any) => ({
        id: item.requirements.id,
        name: item.requirements.name,
        category: item.requirements.category,
      })) as Requirement[];
    },
    enabled: !!jobId,
  });
}

export function useUpdateJobRequirements() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      jobId,
      requirementIds,
    }: {
      jobId: string;
      requirementIds: string[];
    }) => {
      // Delete existing requirements
      const { error: deleteError } = await supabase
        .from("job_requirements")
        .delete()
        .eq("job_id", jobId);

      if (deleteError) throw deleteError;

      // Insert new requirements
      if (requirementIds.length > 0) {
        const { error: insertError } = await supabase
          .from("job_requirements")
          .insert(
            requirementIds.map((requirementId) => ({
              job_id: jobId,
              requirement_id: requirementId,
            })),
          );

        if (insertError) throw insertError;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["job-requirements", variables.jobId],
      });
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
      toast({
        title: "Success",
        description: "Requirements updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
} 