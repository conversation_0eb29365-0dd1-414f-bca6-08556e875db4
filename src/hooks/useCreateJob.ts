import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { Benefit } from "@/hooks/useBenefits";
import { Requirement } from "@/hooks/useRequirements";

interface CreateJobData {
  title: string;
  department: string;
  location: string;
  job_type: string;
  salary_range?: string;
  experience_required?: string;
  description: string;
  normalized_benefits?: Benefit[];
  normalized_requirements?: Requirement[];
  is_urgent: boolean;
}

export const useCreateJob = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (jobData: CreateJobData) => {
      if (!user) {
        throw new Error("User must be authenticated to create a job");
      }

      // Get the foreign key IDs for the normalized fields
      let departmentId: string | null = null;
      let jobTypeId: string | null = null;
      let locationId: string | null = null;

      // Get department_id
      if (jobData.department) {
        const { data: departmentData } = await supabase
          .from("departments")
          .select("id")
          .eq("name", jobData.department)
          .single();
        departmentId = departmentData?.id || null;
      }

      // Get job_type_id
      if (jobData.job_type) {
        const { data: jobTypeData } = await supabase
          .from("job_types")
          .select("id")
          .eq("name", jobData.job_type)
          .single();
        jobTypeId = jobTypeData?.id || null;
      }

      // Get location_id
      if (jobData.location) {
        const { data: locationData } = await supabase
          .from("locations")
          .select("id")
          .eq("name", jobData.location)
          .single();
        locationId = locationData?.id || null;
      }

      // Prepare the data for insertion
      const insertData = {
        title: jobData.title,
        salary_range: jobData.salary_range,
        experience_required: jobData.experience_required,
        description: jobData.description,
        is_urgent: jobData.is_urgent,
        user_id: user.id,
        is_active: true,
        applicant_count: 0,
        department_id: departmentId,
        job_type_id: jobTypeId,
        location_id: locationId,
      };

      const { data, error } = await supabase
        .from("jobs")
        .insert(insertData)
        .select()
        .single();

      if (error) throw error;

      // Create junction table entries for benefits
      if (jobData.normalized_benefits && jobData.normalized_benefits.length > 0) {
        const { error: benefitsError } = await supabase
          .from("job_benefits")
          .insert(
            jobData.normalized_benefits.map((benefit) => ({
              job_id: data.id,
              benefit_id: benefit.id,
            }))
          );

        if (benefitsError) throw benefitsError;
      }

      // Create junction table entries for requirements
      if (jobData.normalized_requirements && jobData.normalized_requirements.length > 0) {
        const { error: requirementsError } = await supabase
          .from("job_requirements")
          .insert(
            jobData.normalized_requirements.map((requirement) => ({
              job_id: data.id,
              requirement_id: requirement.id,
            }))
          );

        if (requirementsError) throw requirementsError;
      }
      
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Job Created",
        description: "Your job posting has been successfully created.",
      });
    },
    onError: (error) => {
      console.error("Error creating job:", error);
      toast({
        title: "Error",
        description: "Failed to create job posting. Please try again.",
        variant: "destructive",
      });
    },
  });
};
