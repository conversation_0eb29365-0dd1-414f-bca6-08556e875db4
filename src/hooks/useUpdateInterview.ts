import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateActivityEntry } from "./useCreateActivityEntry";
import { useToast } from "@/hooks/use-toast";

interface UpdateInterviewData {
  id: string;
  status?: "scheduled" | "completed" | "cancelled" | "rescheduled";
  notes?: string;
  feedback?: string;
  rating?: number;
}

export const useUpdateInterview = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const createActivityEntry = useCreateActivityEntry();

  return useMutation({
    mutationFn: async (updateData: UpdateInterviewData) => {
      if (!user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("candidate_interviews")
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", updateData.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Create activity entry for interview update
      try {
        await createActivityEntry.mutateAsync({
          candidate_id: data.candidate_id,
          activity_type: "interview",
          title: `Interview ${data.status}`,
          description: `Interview status updated to ${data.status}`,
          metadata: {
            interview_id: data.id,
            interview_status: data.status,
            rating: data.rating,
            status: "completed",
          },
        });
      } catch (activityError) {
        console.warn("Failed to create activity entry for interview update:", activityError);
      }

      toast({
        title: "Interview Updated",
        description: "Interview has been updated successfully.",
      });
    },
    onError: (error) => {
      console.error("Error updating interview:", error);
      toast({
        title: "Error",
        description: "Failed to update interview. Please try again.",
        variant: "destructive",
      });
    },
  });
};
