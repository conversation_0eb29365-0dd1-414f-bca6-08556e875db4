import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface AnalyticsMetric {
  id: string;
  user_id: string;
  metric_name: string;
  metric_value: number;
  change_percentage: number | null;
  period: string;
  created_at: string;
  updated_at: string;
}

interface MetricForUI {
  name: string;
  value: number;
  change: number;
  trend: "up" | "down" | "stable";
  period: string;
  suffix?: string;
}

const transformMetricsForUI = (metrics: AnalyticsMetric[]): MetricForUI[] => {
  if (!metrics || !Array.isArray(metrics)) return [];

  // Create a map of metric transformations
  const metricTransforms: Record<
    string,
    (value: number) => { value: number | string; suffix?: string }
  > = {
    total_applications: (value) => ({ value }),
    active_jobs: (value) => ({ value }),
    time_to_hire: (value) => ({ value, suffix: " days" }),
    success_rate: (value) => ({ value, suffix: "%" }),
    hiring_rate: (value) => ({ value, suffix: "%" }),
  };

  return metrics.map((metric) => {
    const transform =
      metricTransforms[metric.metric_name] ||
      ((value) => ({ value, suffix: undefined }));
    const transformed = transform(metric.metric_value);

    return {
      name: metric.metric_name,
      value:
        typeof transformed.value === "number"
          ? transformed.value
          : metric.metric_value,
      change: Math.abs(metric.change_percentage || 0),
      trend: (metric.change_percentage || 0) >= 0 ? "up" : "down",
      period: metric.period,
      suffix: transformed.suffix,
    };
  });
};

export const useAnalyticsMetrics = () => {
  const { user } = useAuth();

  // Real-time metrics analytics subscription
  const { records: metricsData = [], isLoading } = useRealtimeCollection(
    "analytics_metrics",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      const { data, error } = await supabase
        .from("analytics_metrics")
        .select("*")
        .eq("user_id", user.id)
        .order("updated_at", { ascending: false });

      if (error) {
        console.error("Error fetching analytics metrics:", error);
        throw error;
      }

      // Get the most recent metric for each metric_name (avoid duplicates)
      const uniqueMetrics: AnalyticsMetric[] = [];
      const seenMetrics = new Set<string>();

      for (const metric of data || []) {
        if (!seenMetrics.has(metric.metric_name)) {
          uniqueMetrics.push(metric);
          seenMetrics.add(metric.metric_name);
        }
      }

      // Ensure we have the 4 main metrics in the correct order
      const orderedMetricNames = [
        "total_applications",
        "active_jobs",
        "time_to_hire",
        "success_rate",
      ];
      const orderedMetrics = orderedMetricNames
        .map((name) => uniqueMetrics.find((m) => m.metric_name === name))
        .filter(Boolean) as AnalyticsMetric[];

      return transformMetricsForUI(orderedMetrics);
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return {
    data: metricsData,
    isLoading,
    error: null,
  };
};

export const useWorkflowUsageSummary = () => {
  // Real-time workflow usage summary subscription
  const { records: summary = [], isLoading } = useRealtimeCollection(
    "workflow_usage_summary",
    async () => {
      try {
        const { data, error } = await supabase
          .from("workflow_usage_summary")
          .select("*");
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error("Error in useWorkflowUsageSummary:", error);
        return [];
      }
    },
    "public",
  );
  return { data: summary, isLoading, error: null };
};
