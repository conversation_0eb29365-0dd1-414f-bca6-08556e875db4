import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface AnalyticsApplication {
  id: string;
  user_id: string;
  period: string;
  applications: number;
  interviews: number;
  created_at: string;
  updated_at: string;
}

const transformApplicationsForChart = (
  applications: AnalyticsApplication[],
) => {
  if (!applications || !Array.isArray(applications)) return [];

  return applications.map((app) => ({
    name: app.period, // Change period to name for chart compatibility
    period: app.period,
    applications: app.applications,
    qualified: Math.floor(app.applications * 0.6), // Mock qualified count as 60% of applications
    interviews: app.interviews,
    offers: Math.floor(app.applications * 0.15), // Mock offers as 15% of applications
    conversion_rate:
      app.applications > 0
        ? (
            (Math.floor(app.applications * 0.15) / app.applications) *
            100
          ).toFixed(1)
        : "0",
  }));
};

export const useAnalyticsApplications = () => {
  const { user } = useAuth();

  // Real-time applications analytics subscription
  const { records: applicationsData = [], isLoading } = useRealtimeCollection(
    // Subscribe to rollup table for realtime updates
    "analytics_applications_monthly",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      // Fetch last 12 months from rollup table
      const start = new Date();
      start.setMonth(start.getMonth() - 11);
      const startMonth = `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}-01`;

      const { data, error } = await supabase
        .from("analytics_applications_monthly")
        .select("month, applications, interviews")
        .eq("user_id", user.id)
        .gte("month", startMonth)
        .order("month");

      if (error) {
        console.error("Error fetching analytics applications:", error);
        throw error;
      }

      // Adapt rollup rows to chart format
      const monthNames = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];
      const points = (data || []).map((row: any) => {
        const d = new Date(row.month);
        return {
          name: monthNames[d.getMonth()],
          period: monthNames[d.getMonth()],
          applications: row.applications,
          qualified: Math.floor((row.applications || 0) * 0.6),
          interviews: row.interviews,
          offers: Math.floor((row.applications || 0) * 0.15),
          conversion_rate: row.applications > 0 ? ((Math.floor((row.applications||0)*0.15) / row.applications) * 100).toFixed(1) : "0",
        };
      });
      return points;
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return {
    data: applicationsData,
    isLoading,
    error: null,
  };
};
