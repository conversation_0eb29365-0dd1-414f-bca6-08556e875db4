import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { IntegrationsService } from "@/services/IntegrationsService";

/**
 * Hook for initializing default integrations
 */
export const useInitializeIntegrations = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      if (!user) throw new Error("User must be authenticated");
      return await IntegrationsService.initializeDefaultIntegrations(user.id);
    },
    onSuccess: () => {
      toast({
        title: "Integrations Initialized",
        description: "Default integrations have been set up for your account.",
      });
    },
    onError: (error) => {
      console.error("Error initializing integrations:", error);
      toast({
        title: "Initialization Failed",
        description: "Failed to initialize integrations. Please try again.",
        variant: "destructive",
      });
    },
  });
};

/**
 * Hook for connecting an integration
 */
export const useConnectIntegration = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      integrationName,
      settings,
    }: {
      integrationName: string;
      settings: Record<string, unknown>;
    }) => {
      if (!user) throw new Error("User must be authenticated");
      return await IntegrationsService.connectIntegration(
        user.id,
        integrationName,
        settings,
      );
    },
    onSuccess: (_, { integrationName }) => {
      toast({
        title: "Integration Connected",
        description: `Successfully connected to ${integrationName}.`,
      });
    },
    onError: (error, { integrationName }) => {
      console.error("Error connecting integration:", error);
      toast({
        title: "Connection Failed",
        description: `Failed to connect to ${integrationName}. Please try again.`,
        variant: "destructive",
      });
    },
  });
};

/**
 * Hook for disconnecting an integration
 */
export const useDisconnectIntegration = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (integrationName: string) => {
      if (!user) throw new Error("User must be authenticated");
      return await IntegrationsService.disconnectIntegration(
        user.id,
        integrationName,
      );
    },
    onSuccess: (_, integrationName) => {
      toast({
        title: "Integration Disconnected",
        description: `Successfully disconnected from ${integrationName}.`,
      });
    },
    onError: (error, integrationName) => {
      console.error("Error disconnecting integration:", error);
      toast({
        title: "Disconnection Failed",
        description: `Failed to disconnect from ${integrationName}. Please try again.`,
        variant: "destructive",
      });
    },
  });
};
