import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Tables } from "@/integrations/supabase/types";

export type Job = Tables<"jobs"> & {
  // Database normalized fields
  department_id?: string; // Foreign key to departments table
  department_name?: string; // Denormalized from departments table
  location_id?: string; // Foreign key to locations table
  location_name?: string; // Denormalized from locations table
  location_type?: string; // Denormalized from locations table
  location_city?: string; // Denormalized from locations table
  location_state?: string; // Denormalized from locations table
  location_is_remote?: boolean; // Denormalized from locations table
  job_type_id?: string; // Foreign key to job_types table
  job_type_name?: string; // Denormalized from job_types table
  // Backward compatibility
  department?: string; // Keep for backward compatibility
  location?: string; // Keep for backward compatibility
  job_type?: string; // Keep for backward compatibility
};

export const useJob = (jobId: string | undefined) => {
  return useQuery({
    queryKey: ['job', jobId],
    queryFn: async () => {
      if (!jobId) {
        return null;
      }

      const { data: job, error: fetchError } = await supabase
        .from("jobs_with_normalized_data")
        .select("*")
        .eq("id", jobId)
        .single();

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      return job;
    },
    enabled: !!jobId,
  });
};
