import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useMutation } from "@tanstack/react-query";
import {
  CandidateAnalyticsService,
  type CandidateMonthlyStatsData,
  type CreateCandidateMonthlyStatsData,
  type UpdateCandidateMonthlyStatsData,
  type CandidateSkillsStatsData,
  type CreateCandidateSkillsStatsData,
  type UpdateCandidateSkillsStatsData,
  type MonthlyChartData,
  type SkillsChartData,
} from "@/services/CandidateAnalyticsService";
import { useToast } from "@/hooks/use-toast";

// Monthly Stats Hooks
export const useCandidateMonthlyStats = (userId: string, year?: number) => {
  // Real-time candidate monthly stats subscription
  const { records: monthlyStatsData = [], isLoading } = useRealtimeCollection(
    "candidate_monthly_stats",
    async () => {
      if (!userId) throw new Error("User ID is required");
      return CandidateAnalyticsService.getCandidateMonthlyStats(userId, year);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: monthlyStatsData, isLoading, error: null };
};

export const useCandidateMonthlyChartData = (userId: string, year?: number) => {
  // Real-time candidate monthly chart data subscription
  const { records: chartData = [], isLoading } = useRealtimeCollection(
    "candidate_monthly_stats",
    async () => {
      if (!userId) throw new Error("User ID is required");
      const data = await CandidateAnalyticsService.getCandidateMonthlyStats(
        userId,
        year,
      );
      return CandidateAnalyticsService.transformMonthlyDataForChart(data);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: chartData, isLoading, error: null };
};

export const useCreateCandidateMonthlyStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (statsData: CreateCandidateMonthlyStatsData) =>
      CandidateAnalyticsService.createCandidateMonthlyStats(statsData),
    onSuccess: () => {
      toast({
        title: "Monthly Stats Created",
        description:
          "Candidate monthly statistics have been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateCandidateMonthlyStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (updateData: UpdateCandidateMonthlyStatsData) =>
      CandidateAnalyticsService.updateCandidateMonthlyStats(updateData),
    onSuccess: () => {
      toast({
        title: "Monthly Stats Updated",
        description:
          "Candidate monthly statistics have been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteCandidateMonthlyStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) =>
      CandidateAnalyticsService.deleteCandidateMonthlyStats(id),
    onSuccess: () => {
      toast({
        title: "Monthly Stats Deleted",
        description:
          "Candidate monthly statistics have been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Skills Stats Hooks
export const useCandidateSkillsStats = (userId: string) => {
  // Real-time candidate skills stats subscription
  const { records: skillsStatsData = [], isLoading } = useRealtimeCollection(
    "candidate_skills_stats",
    async () => {
      if (!userId) throw new Error("User ID is required");
      return CandidateAnalyticsService.getCandidateSkillsStats(userId);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: skillsStatsData, isLoading, error: null };
};

export const useCandidateSkillsChartData = (userId: string) => {
  // Real-time candidate skills chart data subscription
  const { records: chartData = [], isLoading } = useRealtimeCollection(
    "candidate_skills_stats",
    async () => {
      if (!userId) throw new Error("User ID is required");
      const data =
        await CandidateAnalyticsService.getCandidateSkillsStats(userId);
      return CandidateAnalyticsService.transformSkillsDataForChart(data);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: chartData, isLoading, error: null };
};

export const useCreateCandidateSkillsStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (skillData: CreateCandidateSkillsStatsData) =>
      CandidateAnalyticsService.createCandidateSkillsStats(skillData),
    onSuccess: () => {
      toast({
        title: "Skills Stats Created",
        description:
          "Candidate skills statistics have been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateCandidateSkillsStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (updateData: UpdateCandidateSkillsStatsData) =>
      CandidateAnalyticsService.updateCandidateSkillsStats(updateData),
    onSuccess: () => {
      toast({
        title: "Skills Stats Updated",
        description:
          "Candidate skills statistics have been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteCandidateSkillsStats = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) =>
      CandidateAnalyticsService.deleteCandidateSkillsStats(id),
    onSuccess: () => {
      toast({
        title: "Skills Stats Deleted",
        description:
          "Candidate skills statistics have been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Initialization hooks
export const useInitializeCandidateAnalytics = () => {
  const { toast } = useToast();

  const initializeMonthlyData = useMutation({
    mutationFn: (userId: string) =>
      CandidateAnalyticsService.initializeMonthlyData(userId),
    onSuccess: () => {
      toast({
        title: "Monthly Data Initialized",
        description: "Candidate monthly analytics data has been initialized.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const initializeSkillsData = useMutation({
    mutationFn: (userId: string) =>
      CandidateAnalyticsService.initializeSkillsData(userId),
    onSuccess: () => {
      toast({
        title: "Skills Data Initialized",
        description: "Candidate skills analytics data has been initialized.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const initializeAllData = async (userId: string) => {
    try {
      await Promise.all([
        initializeMonthlyData.mutateAsync(userId),
        initializeSkillsData.mutateAsync(userId),
      ]);
      return true;
    } catch (error) {
      console.error("Failed to initialize candidate analytics data:", error);
      throw error;
    }
  };

  return {
    initializeMonthlyData: initializeMonthlyData.mutateAsync,
    initializeSkillsData: initializeSkillsData.mutateAsync,
    initializeAllData,
    isInitializingMonthly: initializeMonthlyData.isPending,
    isInitializingSkills: initializeSkillsData.isPending,
    isInitializing:
      initializeMonthlyData.isPending || initializeSkillsData.isPending,
  };
};
