import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { CandidatesService, UpdateCandidateData } from "@/services";

export const useUpdateCandidate = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (vars: { id: string; data: UpdateCandidateData }) => {
      console.log("🔍 Starting candidate update process...");
      console.log("🔍 Candidate ID:", vars.id);
      console.log("🔍 Update data:", vars.data);

      // Validate candidate ID
      if (!vars.id) {
        console.error("❌ No candidate ID provided");
        throw new Error("Candidate ID is required");
      }

      const candidate = await CandidatesService.updateCandidate(
        vars.id,
        vars.data,
      );

      console.log("✅ Candidate successfully updated:", candidate);
      return candidate;
    },
    onSuccess: (data, vars) => {
      console.log("Mutation successful, invalidating queries");

      // Invalidate specific candidate query
      queryClient.invalidateQueries({ queryKey: ["candidate", vars.id] });
      // Invalidate candidates list
      queryClient.invalidateQueries({ queryKey: ["candidates"] });

      toast({
        title: "Success",
        description: "Candidate updated successfully",
      });
    },
    onError: (error) => {
      console.error("Error updating candidate:", error);
      toast({
        title: "Error",
        description: `Failed to update candidate: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
    },
  });
};
