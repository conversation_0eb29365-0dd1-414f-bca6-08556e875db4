import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { PipelineService } from "@/services/PipelineService";
import { useAuth } from "@/contexts/AuthContext";

export function usePipelineRealtimeSync(userId?: string) {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;
  const [candidates, setCandidates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubscribed, setIsSubscribed] = useState(false);

  useEffect(() => {
    if (!targetUserId) return;

    setIsLoading(true);

    // Initial fetch from the view
    const fetchCandidates = async () => {
      try {
        const data = await PipelineService.getPipelineCandidates(targetUserId);
        setCandidates(data);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching pipeline candidates:", error);
        setIsLoading(false);
      }
    };

    fetchCandidates();

    // Subscribe to the base table for real-time updates
    const channelName = `pipeline_candidates_${targetUserId}`;
    const subscription = supabase
      .channel(channelName)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "pipeline_candidates",
          filter: `user_id=eq.${targetUserId}`,
        },
        async (payload) => {
          console.log("Pipeline real-time event:", payload);

          // For any change, refetch from the view to get complete data
          // This ensures we always have candidate_name and other joined fields
          try {
            const data =
              await PipelineService.getPipelineCandidates(targetUserId);
            setCandidates(data);
          } catch (error) {
            console.error("Error refetching pipeline candidates:", error);
          }
        },
      )
      .subscribe((status) => {
        console.log("Pipeline subscription status:", status);
        setIsSubscribed(status === "SUBSCRIBED");
      });

    return () => {
      subscription.unsubscribe();
    };
  }, [targetUserId]);

  return { candidates, isLoading, isSubscribed };
}
