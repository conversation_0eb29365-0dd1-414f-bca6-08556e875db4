import { useMutation } from "@tanstack/react-query";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import {
  ReportGeneratorService,
  type ReportTemplateData,
  type CreateReportTemplateData,
  type UpdateReportTemplateData,
  type GeneratedReportData,
  type CreateGeneratedReportData,
  type UpdateGeneratedReportData,
} from "@/services/ReportGeneratorService";

// Report Templates Hooks
export const useReportTemplates = (userId: string) => {
  // Real-time report templates subscription
  const { records: reportTemplates = [], isLoading } = useRealtimeCollection(
    "report_templates",
    async () => {
      if (!userId) return [];

      try {
        return await ReportGeneratorService.getReportTemplates(userId);
      } catch (error) {
        console.error("Error in useReportTemplates:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: reportTemplates, isLoading, error: null };
};

export const useCreateReportTemplate = () => {
  return useMutation({
    mutationFn: (templateData: CreateReportTemplateData) =>
      ReportGeneratorService.createReportTemplate(templateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });
};

export const useUpdateReportTemplate = () => {
  return useMutation({
    mutationFn: (updateData: UpdateReportTemplateData) =>
      ReportGeneratorService.updateReportTemplate(updateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });
};

export const useDeleteReportTemplate = () => {
  return useMutation({
    mutationFn: (id: string) => ReportGeneratorService.deleteReportTemplate(id),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });
};

// Generated Reports Hooks
export const useGeneratedReports = (userId: string) => {
  // Real-time generated reports subscription
  const { records: generatedReports = [], isLoading } = useRealtimeCollection(
    "generated_reports",
    async () => {
      if (!userId) return [];

      try {
        return await ReportGeneratorService.getGeneratedReports(userId);
      } catch (error) {
        console.error("Error in useGeneratedReports:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: generatedReports, isLoading, error: null };
};

export const useCreateGeneratedReport = () => {
  return useMutation({
    mutationFn: (reportData: CreateGeneratedReportData) =>
      ReportGeneratorService.createGeneratedReport(reportData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });
};

export const useUpdateGeneratedReport = () => {
  return useMutation({
    mutationFn: (updateData: UpdateGeneratedReportData) =>
      ReportGeneratorService.updateGeneratedReport(updateData),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });
};

export const useDeleteGeneratedReport = () => {
  return useMutation({
    mutationFn: (id: string) =>
      ReportGeneratorService.deleteGeneratedReport(id),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });
};

// Initialization hook
export const useInitializeReportGenerator = () => {
  const initializeTemplates = useMutation({
    mutationFn: (userId: string) =>
      ReportGeneratorService.initializeReportTemplates(userId),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });

  const initializeReports = useMutation({
    mutationFn: (userId: string) =>
      ReportGeneratorService.initializeGeneratedReports(userId),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
  });

  const initializeAllData = async (userId: string) => {
    try {
      await Promise.all([
        initializeTemplates.mutateAsync(userId),
        initializeReports.mutateAsync(userId),
      ]);
      return true;
    } catch (error) {
      console.error("Failed to initialize report generator data:", error);
      throw error;
    }
  };

  return {
    initializeTemplates: initializeTemplates.mutateAsync,
    initializeReports: initializeReports.mutateAsync,
    initializeAllData,
    isInitializingTemplates: initializeTemplates.isPending,
    isInitializingReports: initializeReports.isPending,
    isInitializing:
      initializeTemplates.isPending || initializeReports.isPending,
  };
};
