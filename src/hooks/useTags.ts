import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Tag } from "@/components/TagPicker";
import { useToast } from "@/hooks/use-toast";

export function useTags() {
  return useQuery({
    queryKey: ["tags"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("tags")
        .select("*")
        .order("name");

      if (error) throw error;

      return data.map((tag) => ({
        id: tag.id.toString(),
        name: tag.name,
        color: tag.color || "#3b82f6",
      })) as Tag[];
    },
  });
}

export function useCreateTag() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ name, color }: { name: string; color: string }) => {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("tags")
        .insert({
          name,
          color,
          user_id: userData.user.id,
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: data.id.toString(),
        name: data.name,
        color: data.color || "#3b82f6",
      } as Tag;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      toast({
        title: "Success",
        description: "Tag created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useUpdateTag() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      id,
      name,
      color,
    }: {
      id: string;
      name: string;
      color: string;
    }) => {
      const { error } = await supabase
        .from("tags")
        .update({ name, color })
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      toast({
        title: "Success",
        description: "Tag updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useDeleteTag() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from("tags")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tags"] });
      toast({
        title: "Success",
        description: "Tag deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useCandidateTags(candidateId: string) {
  return useQuery({
    queryKey: ["candidate-tags", candidateId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("candidate_tags")
        .select("tag_id, tags!inner(id, name, color)")
        .eq("candidate_id", candidateId);

      if (error) throw error;

      // Be defensive about row typing coming from the join
      const rows = (data as any[]) || [];
      return rows.map((item: any) => ({
        id: String(item?.tags?.id ?? item?.tag_id ?? ""),
        name: String(item?.tags?.name ?? ""),
        color: String(item?.tags?.color ?? "#3b82f6"),
      })) as Tag[];
    },
    enabled: !!candidateId,
  });
}

export function useUpdateCandidateTags() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      candidateId,
      tagIds,
    }: {
      candidateId: string;
      tagIds: string[];
    }) => {
      // Delete existing tags
      const { error: deleteError } = await supabase
        .from("candidate_tags")
        .delete()
        .eq("candidate_id", candidateId);

      if (deleteError) throw deleteError;

      // Insert new tags
      if (tagIds.length > 0) {
        const { error: insertError } = await supabase
          .from("candidate_tags")
          .insert(
            tagIds.map((tagId) => ({
              candidate_id: candidateId,
              tag_id: tagId, // tag IDs are UUID strings
            })),
          );

        if (insertError) throw insertError;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["candidate-tags", variables.candidateId],
      });
      queryClient.invalidateQueries({ queryKey: ["candidates"] });
      queryClient.invalidateQueries({
        queryKey: ["candidate", variables.candidateId],
      });
      // Notify any real-time lists to refetch
      try {
        window.dispatchEvent(
          new CustomEvent("candidate-updated", {
            detail: { candidateId: variables.candidateId },
          }),
        );
      } catch {}
      toast({
        title: "Success",
        description: "Tags updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}
