import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateActivityEntry } from "./useCreateActivityEntry";
import { useToast } from "@/hooks/use-toast";

interface CreateNoteData {
  candidate_id: string;
  content: string;
  is_important?: boolean;
}

export const useCreateNote = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const createActivityEntry = useCreateActivityEntry();

  return useMutation({
    mutationFn: async (noteData: CreateNoteData) => {
      if (!user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("candidate_notes")
        .insert({
          ...noteData,
          user_id: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Real-time subscription will automatically update the UI

      // Create activity entry
      try {
        await createActivityEntry.mutateAsync({
          candidate_id: data.candidate_id,
          activity_type: "note",
          title: "Note Added",
          description: `Note added: ${data.content.substring(0, 50)}${data.content.length > 50 ? "..." : ""}`,
          metadata: {
            note_id: data.id,
            is_important: data.is_important || false,
            status: "completed",
          },
        });
      } catch (activityError) {
        console.warn("Failed to create activity entry for note:", activityError);
      }

      toast({
        title: "Note Added",
        description: "Your note has been saved successfully.",
      });
    },
    onError: (error) => {
      console.error("Error creating note:", error);
      toast({
        title: "Error",
        description: "Failed to save note. Please try again.",
        variant: "destructive",
      });
    },
  });
};
