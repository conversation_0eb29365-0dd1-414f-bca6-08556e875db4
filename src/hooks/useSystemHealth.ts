import { useMutation } from "@tanstack/react-query";
import {
  SystemHealthService,
  SystemHealthData,
  CreateSystemHealthData,
  UpdateSystemHealthData,
} from "@/services/SystemHealthService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export const useSystemHealth = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Real-time system health subscription
  const { records: systemHealthData = [], isLoading } = useRealtimeCollection(
    "system_health",
    async () => {
      if (!user?.id) return [];

      try {
        // Try to get existing data, or initialize with defaults
        const existingData = await SystemHealthService.getSystemHealth(user.id);
        if (existingData.length > 0) {
          return existingData;
        }

        // Initialize with default data if none exists
        return SystemHealthService.initializeDefaultSystemHealth(user.id);
      } catch (error) {
        console.error("Error fetching system health:", error);
        return [];
      }
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: systemHealthData, isLoading, error: null };
};

export const useCreateSystemHealth = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: Omit<CreateSystemHealthData, "user_id">) => {
      if (!user?.id) throw new Error("User not authenticated");
      return SystemHealthService.createSystemHealth({
        ...data,
        user_id: user.id,
      });
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "System Health Created",
        description: "New system health entry created successfully.",
      });
    },
    onError: (error) => {
      console.error("Error creating system health:", error);
      toast({
        title: "Error",
        description: "Failed to create system health entry.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateSystemHealth = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: UpdateSystemHealthData) => {
      return SystemHealthService.updateSystemHealth(data);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "System Health Updated",
        description: "System health data has been updated successfully.",
      });
    },
    onError: (error) => {
      console.error("Error updating system health:", error);
      toast({
        title: "Error",
        description: "Failed to update system health.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteSystemHealth = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => {
      return SystemHealthService.deleteSystemHealth(id);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "System Health Deleted",
        description: "System health entry deleted successfully.",
      });
    },
    onError: (error) => {
      console.error("Error deleting system health:", error);
      toast({
        title: "Error",
        description: "Failed to delete system health entry.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateSystemHealthMetrics = () => {
  const { user } = useAuth();

  return useMutation({
    mutationFn: () => {
      if (!user?.id) throw new Error("User not authenticated");
      return SystemHealthService.updateSystemHealthMetrics(user.id);
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
    },
    onError: (error) => {
      console.error("Error updating system health metrics:", error);
    },
  });
};
