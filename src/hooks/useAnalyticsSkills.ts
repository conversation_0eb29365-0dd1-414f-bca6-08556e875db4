import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface AnalyticsSkill {
  id: string;
  user_id: string;
  skill: string;
  current_level: number;
  required_level: number;
  gap: number;
  recommendation: string | null;
  success_impact: number | null;
  created_at: string;
  updated_at: string;
}

const transformSkillsForChart = (skills: AnalyticsSkill[]) => {
  if (!skills || !Array.isArray(skills)) return [];

  return skills.map((skill) => ({
    skill: skill.skill,
    current: skill.current_level, // For SkillGapAnalysis component compatibility
    required: skill.required_level, // For SkillGapAnalysis component compatibility
    current_level: skill.current_level,
    required_level: skill.required_level,
    gap: skill.gap,
    recommendation: skill.recommendation,
    successImpact: skill.success_impact || 0, // For SkillGapAnalysis component compatibility
    success_impact: skill.success_impact,
    demand: skill.current_level + Math.random() * 20, // Mock demand score
    supply: skill.required_level - Math.random() * 10, // Mock supply score
    salary: 75000 + skill.current_level * 1000, // Mock salary calculation
    candidates: Math.floor(Math.random() * 50) + 10, // Mock candidate count
    jobs: Math.floor(Math.random() * 20) + 5, // Mock job count
    competitiveness: skill.current_level / Math.max(skill.required_level, 1),
  }));
};

export const useAnalyticsSkills = () => {
  const { user } = useAuth();

  // Real-time skills analytics subscription
  const { records: skillsData = [], isLoading } = useRealtimeCollection(
    "analytics_skills",
    async () => {
      if (!user) {
        console.log("No authenticated user, returning empty array");
        return [];
      }

      const { data, error } = await supabase
        .from("analytics_skills")
        .select("*")
        .eq("user_id", user.id);

      if (error) {
        console.error("Error fetching analytics skills:", error);
        throw error;
      }

      return transformSkillsForChart(data || []);
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return {
    data: skillsData,
    isLoading,
    error: null,
  };
};
