import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  JobExpenseService,
  CreateJobExpenseData,
  UpdateJobExpenseData,
} from "@/services/JobExpenseService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

export const useJobExpenses = () => {
  const { user } = useAuth();

  // Real-time job expenses subscription
  const { records: jobExpenses = [], isLoading } = useRealtimeCollection(
    "job_expenses",
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      return await JobExpenseService.getJobExpenses(user.id);
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: jobExpenses, isLoading, error: null };
};

export const useJobExpensesByJob = (jobId: string) => {
  const { user } = useAuth();

  // Real-time job expenses subscription for specific job
  const { records: jobExpenses = [], isLoading } = useRealtimeCollection(
    "job_expenses",
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      return await JobExpenseService.getJobExpensesByJob(user.id, jobId);
    },
    "public",
    `user_id=eq.${user?.id}&job_id=eq.${jobId}`,
  );

  return { data: jobExpenses, isLoading, error: null };
};

export const useJobExpenseSummary = () => {
  const { user } = useAuth();

  // Real-time job expense summary subscription
  const { records: summaryData = [], isLoading } = useRealtimeCollection(
    "job_expenses",
    async () => {
      if (!user?.id) throw new Error("User not authenticated");
      return await JobExpenseService.getJobExpenseSummary(user.id);
    },
    "public",
    `user_id=eq.${user?.id}`,
  );

  return { data: summaryData, isLoading, error: null };
};

export const useCreateJobExpense = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (data: CreateJobExpenseData) =>
      JobExpenseService.createJobExpense(data),
    onSuccess: (_, variables) => {
      toast({
        title: "Expense Added",
        description: "Job expense has been recorded successfully.",
      });

      // Invalidate related queries (for any remaining React Query usage)
      queryClient.invalidateQueries({ queryKey: ["job-expenses"] });
      queryClient.invalidateQueries({ queryKey: ["budget-summary"] });

      // Force a refetch of budget-related data by triggering a custom event
      // This helps with real-time subscription timing issues
      window.dispatchEvent(new CustomEvent('budget-updated', {
        detail: {
          userId: user?.id,
          jobId: variables.job_id,
          expenseType: variables.expense_type,
          amount: variables.amount
        }
      }));
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useUpdateJobExpense = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (data: UpdateJobExpenseData) =>
      JobExpenseService.updateJobExpense(data),
    onSuccess: (_, variables) => {
      toast({
        title: "Expense Updated",
        description: "Job expense has been updated successfully.",
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ["job-expenses"] });
      queryClient.invalidateQueries({ queryKey: ["budget-summary"] });

      // Force a refetch of budget-related data
      window.dispatchEvent(new CustomEvent('budget-updated', {
        detail: {
          userId: user?.id,
          expenseId: variables.id
        }
      }));
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

export const useDeleteJobExpense = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (id: string) => JobExpenseService.deleteJobExpense(id),
    onSuccess: (_, expenseId) => {
      toast({
        title: "Expense Deleted",
        description: "Job expense has been deleted successfully.",
      });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ["job-expenses"] });
      queryClient.invalidateQueries({ queryKey: ["budget-summary"] });

      // Force a refetch of budget-related data
      window.dispatchEvent(new CustomEvent('budget-updated', {
        detail: {
          userId: user?.id,
          expenseId: expenseId
        }
      }));
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
