import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface UpdateMessageData {
  id: string;
  status?: "unread" | "read" | "archived";
  is_starred?: boolean;
  follow_up?: boolean;
  reminder?: boolean;
}

export const useUpdateMessage = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateMessageData) => {
      if (!id) {
        throw new Error("Message ID is required");
      }

      const { data, error } = await supabase
        .from("messages")
        .update(updateData)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Message Updated",
        description: "Message status has been updated successfully.",
      });
    },
    onError: (error) => {
      console.error("Error updating message:", error);
      toast({
        title: "Error",
        description: "Failed to update message. Please try again.",
        variant: "destructive",
      });
    },
  });
};
