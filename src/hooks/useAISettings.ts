import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface AISettings {
  aiMatching: boolean;
  matchingThreshold: number;
  aiScreening: boolean;
  aiSuggestions: boolean;
}

export const useAISettings = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [settings, setSettings] = useState<AISettings>({
    aiMatching: true,
    matchingThreshold: 75,
    aiScreening: false,
    aiSuggestions: true,
  });
  const [isLoading, setIsLoading] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    const loadSettings = async () => {
      if (!user) return;

      try {
        // Try to load from Supabase first
        const { data, error } = await supabase
          .from("user_settings")
          .select("settings")
          .eq("user_id", user.id)
          .eq("settings_type", "ai")
          .maybeSingle();

        if (error) {
          // Only log actual errors, not "no rows found" scenarios
          if (error.code !== "PGRST116") {
            console.error("Error loading AI settings from Supabase:", error);
          }
          // Fall back to localStorage for any error
        } else if (data?.settings) {
          // Successfully loaded from Supabase
          setSettings(data.settings);
          return;
        }

        // Fallback to localStorage if no Supabase data or error occurred
        const savedSettings = localStorage.getItem("aiSettings");
        if (savedSettings) {
          setSettings(JSON.parse(savedSettings));
        }
      } catch (error) {
        console.error("Error loading AI settings:", error);
        // Still try localStorage as final fallback
        const savedSettings = localStorage.getItem("aiSettings");
        if (savedSettings) {
          setSettings(JSON.parse(savedSettings));
        }
      }
    };

    loadSettings();
  }, [user]);

  const updateSetting = <K extends keyof AISettings>(
    key: K,
    value: AISettings[K],
  ) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);

    // Still update localStorage as a fallback
    try {
      localStorage.setItem("aiSettings", JSON.stringify(newSettings));
    } catch (error) {
      console.error("Error saving to localStorage:", error);
    }
  };

  const saveSettings = async () => {
    setIsLoading(true);
    try {
      if (!user) throw new Error("User not authenticated");

      // Upsert settings to Supabase
      const { error } = await supabase.from("user_settings").upsert(
        {
          user_id: user.id,
          settings_type: "ai",
          settings: settings,
          updated_at: new Date().toISOString(),
        },
        {
          onConflict: "user_id,settings_type",
        },
      );

      if (error) throw error;

      // Also update localStorage as fallback
      localStorage.setItem("aiSettings", JSON.stringify(settings));

      toast({
        title: "AI Settings Saved",
        description: "Your AI preferences have been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save AI settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    settings,
    updateSetting,
    saveSettings,
    isLoading,
  };
};
