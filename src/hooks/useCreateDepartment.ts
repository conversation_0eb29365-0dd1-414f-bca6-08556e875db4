import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Department } from "./useDepartments";

interface CreateDepartmentData {
  name: string;
}

export const useCreateDepartment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateDepartmentData): Promise<Department> => {
      const { data: newDepartment, error } = await supabase
        .from("departments")
        .insert([data])
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return newDepartment;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
    },
  });
}; 