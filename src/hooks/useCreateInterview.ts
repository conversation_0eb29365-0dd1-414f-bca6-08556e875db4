import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateActivityEntry } from "./useCreateActivityEntry";
import { useToast } from "@/hooks/use-toast";

interface CreateInterviewData {
  candidate_id: string;
  job_id?: string;
  interviewer_name: string;
  interview_type: string;
  scheduled_date: string;
  duration: number;
  location?: string;
  notes?: string;
}

export const useCreateInterview = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const createActivityEntry = useCreateActivityEntry();

  return useMutation({
    mutationFn: async (interviewData: CreateInterviewData) => {
      if (!user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("candidate_interviews")
        .insert({
          ...interviewData,
          user_id: user.id,
          status: "scheduled",
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Create activity entry for the interview
      try {
        await createActivityEntry.mutateAsync({
          candidate_id: data.candidate_id,
          activity_type: "interview",
          title: "Interview Scheduled",
          description: `${data.interview_type} interview scheduled with ${data.interviewer_name}`,
          metadata: {
            interview_id: data.id,
            scheduled_date: data.scheduled_date,
            status: "scheduled",
          },
        });
      } catch (activityError) {
        console.warn("Failed to create activity entry for interview:", activityError);
      }

      toast({
        title: "Interview Scheduled",
        description: "Interview has been scheduled successfully.",
      });
    },
    onError: (error) => {
      console.error("Error creating interview:", error);
      toast({
        title: "Error",
        description: "Failed to schedule interview. Please try again.",
        variant: "destructive",
      });
    },
  });
};
