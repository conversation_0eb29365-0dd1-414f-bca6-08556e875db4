import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { RealtimeChannel } from "@supabase/supabase-js";
import { REALTIME_SUBSCRIBE_STATES } from "@supabase/realtime-js/dist/module/RealtimeChannel";
import { CHANNEL_STATES } from "@supabase/realtime-js/dist/module/lib/constants";

/**
 * A hook to subscribe to Supabase Realtime changes for a specific table
 * @param tableName The name of the table to subscribe to
 * @param schema The schema of the table (defaults to 'public')
 * @param filters Optional filters to apply to the subscription
 * @returns An object containing the subscription status
 */
export function useRealtimeSubscription<T = any>(
  tableName: string,
  schema: string = "public",
  filters?: {
    event?: "INSERT" | "UPDATE" | "DELETE" | "*";
    filter?: string;
    filterValues?: any[];
  },
) {
  const { user } = useAuth();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [lastEvent, setLastEvent] = useState<{
    eventType: string;
    record: T | null;
    oldRecord?: T | null;
  } | null>(null);

  useEffect(() => {
    if (!user) return;

    // Create a unique channel name
    const channelName = `${schema}_${tableName}_${user.id}`;

    // Set up the subscription
    let subscription = supabase.channel(channelName);

    // Configure the subscription with filters
    const eventType = filters?.event || "*";
    const filter = filters?.filter || "";
    const filterValues = filters?.filterValues || [];

    // Build the subscription
    const subscriptionConfig = {
      event: eventType,
      schema: schema,
      table: tableName,
    } as any;

    if (filter) {
      subscriptionConfig.filter = filter;
    }

    subscription = subscription.on(
      "postgres_changes",
      subscriptionConfig,
      (payload) => {
        console.log(`Realtime event received for ${tableName}:`, payload);
        setLastEvent({
          eventType: payload.eventType,
          record: payload.new as T,
          oldRecord: payload.old as T,
        });
      },
    );

    // Subscribe to the channel
    subscription.subscribe((status) => {
      // Only log non-error statuses or actual subscription success
      if (status !== "CHANNEL_ERROR") {
        console.log(`Realtime subscription status for ${tableName}:`, status);
      }
      setIsSubscribed(status === REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);
      setChannel(subscription);
      
      // Handle channel errors gracefully
      if (status === "CHANNEL_ERROR") {
        // Retry subscription after a short delay
        setTimeout(() => {
          if (subscription.state !== CHANNEL_STATES.joined) {
            console.log(`Retrying subscription for ${tableName}`);
            subscription.subscribe();
          }
        }, 1000);
      }
    });

    // Cleanup function
    return () => {
      if (subscription) {
        console.log(`Unsubscribing from ${tableName}`);
        subscription.unsubscribe();
      }
    };
  }, [user, tableName, schema, filters]);

  return { isSubscribed, lastEvent, channel };
}

/**
 * A hook to subscribe to Supabase Realtime changes for a specific record
 * @param tableName The name of the table to subscribe to
 * @param recordId The ID of the record to subscribe to
 * @param schema The schema of the table (defaults to 'public')
 * @returns An object containing the subscription status and the record
 */
export function useRealtimeRecord<T = any>(
  tableName: string,
  recordId: string,
  schema: string = "public",
) {
  const { user } = useAuth();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [record, setRecord] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user || !recordId) return;

    setIsLoading(true);

    // First, fetch the current record
    const fetchRecord = async () => {
      const { data, error } = await supabase
        .from(tableName)
        .select("*")
        .eq("id", recordId)
        .single();

      if (error) {
        console.error(`Error fetching ${tableName} record:`, error);
        setIsLoading(false);
        return;
      }

      setRecord(data as T);
      setIsLoading(false);
    };

    fetchRecord();

    // Create a unique channel name
    const channelName = `${schema}_${tableName}_${recordId}`;

    // Set up the subscription
    const subscription = supabase
      .channel(channelName)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: schema,
          table: tableName,
          filter: `id=eq.${recordId}`,
        },
        (payload) => {
          console.log(
            `Realtime event received for ${tableName} record:`,
            payload,
          );
          if (payload.eventType === "DELETE") {
            setRecord(null);
          } else {
            setRecord(payload.new as T);
          }
        },
      )
      .subscribe((status) => {
        // Only log non-error statuses
        if (status !== "CHANNEL_ERROR") {
          console.log(
            `Realtime subscription status for ${tableName} record:`,
            status,
          );
        }
        setIsSubscribed(status === REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);
        
        // Handle channel errors gracefully
        if (status === "CHANNEL_ERROR") {
          setTimeout(() => {
            if (subscription.state !== CHANNEL_STATES.joined) {
              console.log(`Retrying subscription for ${tableName} record`);
              subscription.subscribe();
            }
          }, 1000);
        }
      });

    // Cleanup function
    return () => {
      subscription.unsubscribe();
    };
  }, [user, tableName, recordId, schema]);

  return { record, isLoading, isSubscribed };
}

/**
 * A hook to subscribe to Supabase Realtime changes for a collection of records
 * @param tableName The name of the table to subscribe to
 * @param queryFn A function that returns a Supabase query to fetch the initial data
 * @param schema The schema of the table (defaults to 'public')
 * @param filter Optional filter for the subscription
 * @returns An object containing the subscription status and the records
 */
export function useRealtimeCollection<T = any>(
  tableName: string,
  queryFn:
    | (() => Promise<{ data: T[] | null; error: any }>)
    | (() => Promise<T[]>),
  schema: string = "public",
  filter?: string,
) {
  const { user } = useAuth();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [records, setRecords] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) return;

    setIsLoading(true);

    // First, fetch the current records
    const fetchRecords = async () => {
      try {
        const result = await queryFn();

        // Handle both {data, error} format and direct array format
        if (Array.isArray(result)) {
          setRecords(result);
        } else if (result && "data" in result) {
          const { data, error } = result;
          if (error) {
            console.error(`Error fetching ${tableName} records:`, error);
            setIsLoading(false);
            return;
          }
          setRecords(data || []);
        } else {
          setRecords([]);
        }

        setIsLoading(false);
      } catch (error) {
        console.error(`Error in fetchRecords for ${tableName}:`, error);
        setIsLoading(false);
      }
    };

    fetchRecords();

    // Add custom event listener for manual refetch triggers
    const handleJobUpdated = (event: CustomEvent) => {
      if (tableName === 'jobs' && event.detail?.jobId) {
        console.log('Manual refetch triggered for jobs list');
        fetchRecords();
      }
    };

    const handleBudgetUpdated = (event: CustomEvent) => {
      if (tableName === 'job_expenses' && event.detail?.userId) {
        console.log('Manual refetch triggered for budget-related data');
        fetchRecords();
      }
    };

    window.addEventListener('job-updated', handleJobUpdated as EventListener);
    window.addEventListener('budget-updated', handleBudgetUpdated as EventListener);

    // Create a unique channel name
    const channelName = `${schema}_${tableName}_collection_${user.id}`;

    // Set up the subscription
    let subscription = supabase.channel(channelName);

    // Configure the subscription
    const subscriptionConfig = {
      event: "*",
      schema: schema,
      table: tableName,
    } as any;

    // Add filter if provided
    if (filter) {
      subscriptionConfig.filter = filter;
    }

    subscription = subscription.on(
      "postgres_changes",
      subscriptionConfig,
      async (payload) => {
        console.log(
          `Realtime event received for ${tableName} collection:`,
          payload,
        );

        // For INSERT events, refetch all data to ensure we have the complete record
        // This is important when the real-time payload doesn't include all fields
        if (payload.eventType === "INSERT") {
          try {
            const result = await queryFn();
            if (Array.isArray(result)) {
              setRecords(result);
            } else if (result && "data" in result && result.data) {
              setRecords(result.data);
            }
          } catch (error) {
            console.error(
              `Error refetching after INSERT for ${tableName}:`,
              error,
            );
          }
          return;
        }

        // Optimized record updates with minimal array operations
        setRecords((prev) => {
          const newRecord = payload.new as T;
          const oldRecord = payload.old as T;

          switch (payload.eventType) {
            case "UPDATE": {
              const targetId = (newRecord as any).id;
              // Use findIndex for better performance than map
              const index = prev.findIndex(
                (record) => (record as any).id === targetId,
              );
              if (index === -1) return prev;

              // Create new array with updated record
              const updated = [...prev];
              updated[index] = newRecord;
              return updated;
            }
            case "DELETE": {
              const targetId = (oldRecord as any).id;
              // Use filter with early return check
              const filtered = prev.filter(
                (record) => (record as any).id !== targetId,
              );
              return filtered.length === prev.length ? prev : filtered;
            }
            default:
              return prev;
          }
        });
      },
    );

    // Subscribe to the channel
    subscription.subscribe((status) => {
      // Only log non-error statuses
      if (status !== "CHANNEL_ERROR") {
        console.log(
          `Realtime subscription status for ${tableName} collection:`,
          status,
        );
      }
      setIsSubscribed(status === REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);
      
      // Handle channel errors gracefully
      if (status === "CHANNEL_ERROR") {
        setTimeout(() => {
          if (subscription.state !== CHANNEL_STATES.joined) {
            console.log(`Retrying subscription for ${tableName} collection`);
            subscription.subscribe();
          }
        }, 1000);
      }
    });

    // Cleanup function
    return () => {
      subscription.unsubscribe();
      window.removeEventListener('job-updated', handleJobUpdated as EventListener);
      window.removeEventListener('budget-updated', handleBudgetUpdated as EventListener);
    };
  }, [user, tableName, schema, filter]);

  return { records, isLoading, isSubscribed, setRecords };
}
