import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { PipelineTrendsService, type PipelineTrendsData } from "@/services/PipelineTrendsService";

export const usePipelineTrends = (userId: string, year?: number) => {
  // Real-time pipeline trends subscription
  const { records: trendsData = [], isLoading } = useRealtimeCollection(
    "pipeline_candidates",
    async () => {
      if (!userId) throw new Error("User ID is required");
      return PipelineTrendsService.getPipelineTrends(userId, year);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: trendsData, isLoading, error: null };
};

export const useCurrentPipelineDistribution = (userId: string) => {
  // Real-time current pipeline distribution subscription
  const { records: distributionData = [], isLoading } = useRealtimeCollection(
    "pipeline_candidates",
    async () => {
      if (!userId) throw new Error("User ID is required");
      return PipelineTrendsService.getCurrentPipelineDistribution(userId);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: distributionData, isLoading, error: null };
};

export const usePipelineConversionRates = (userId: string) => {
  // Real-time pipeline conversion rates subscription
  const { records: conversionRates = [], isLoading } = useRealtimeCollection(
    "pipeline_candidates",
    async () => {
      if (!userId) throw new Error("User ID is required");
      return PipelineTrendsService.getPipelineConversionRates(userId);
    },
    "public",
    `user_id=eq.${userId}`,
  );

  return { data: conversionRates, isLoading, error: null };
}; 