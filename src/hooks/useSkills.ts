import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export interface Skill {
  id: string;
  name: string;
  category: string;
  created_at: string;
}

export interface CandidateSkill {
  id: string;
  candidate_id: string;
  skill_id: string;
  proficiency_level: "beginner" | "intermediate" | "advanced" | "expert";
  years_experience: number;
  created_at: string;
  skill?: Skill;
}

// Fetch all available skills from the master table
export const useSkills = () => {
  return useQuery({
    queryKey: ["skills"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("skills")
        .select("*")
        .order("name");

      if (error) throw error;
      return data as Skill[];
    },
  });
};

// Fetch skills grouped by category
export const useSkillsByCategory = () => {
  return useQuery({
    queryKey: ["skills-by-category"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("skills")
        .select("*")
        .order("category, name");

      if (error) throw error;

      // Group skills by category
      const grouped = (data as Skill[]).reduce(
        (acc, skill) => {
          if (!acc[skill.category]) {
            acc[skill.category] = [];
          }
          acc[skill.category].push(skill);
          return acc;
        },
        {} as Record<string, Skill[]>,
      );

      return grouped;
    },
  });
};

// Fetch skills for a specific candidate
export const useCandidateSkills = (candidateId: string | undefined) => {
  return useQuery({
    queryKey: ["candidate-skills", candidateId],
    queryFn: async () => {
      if (!candidateId) return [];

      const { data, error } = await supabase
        .from("candidate_skills")
        .select(
          `
          *,
          skill:skills(*)
        `,
        )
        .eq("candidate_id", candidateId)
        .order("created_at");

      if (error) throw error;
      return data as CandidateSkill[];
    },
    enabled: !!candidateId,
  });
};

// Add a skill to the master table
export const useCreateSkill = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      name,
      category,
    }: {
      name: string;
      category: string;
    }) => {
      const { data, error } = await supabase
        .from("skills")
        .insert({ name, category })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["skills"] });
      queryClient.invalidateQueries({ queryKey: ["skills-by-category"] });
      toast({
        title: "Success",
        description: "Skill added successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Add skills to a candidate
export const useAddCandidateSkills = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      candidateId,
      skills,
    }: {
      candidateId: string;
      skills: Array<{
        skill_id: string;
        proficiency_level: "beginner" | "intermediate" | "advanced" | "expert";
        years_experience: number;
      }>;
    }) => {
      console.log("🔧 useAddCandidateSkills mutation called with:", {
        candidateId,
        skills,
        skillsCount: skills.length,
      });

      const insertData = skills.map((skill) => ({
        candidate_id: candidateId,
        skill_id: skill.skill_id,
        proficiency_level: skill.proficiency_level,
        years_experience: skill.years_experience,
      }));

      console.log("📤 Sending to Supabase:", insertData);

      const { data, error } = await supabase
        .from("candidate_skills")
        .insert(insertData)
        .select();

      if (error) {
        console.error("❌ Supabase error:", error);
        throw error;
      }

      console.log("✅ Supabase response:", data);
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["candidate-skills", variables.candidateId],
      });
      toast({
        title: "Success",
        description: "Skills added successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Remove a skill from a candidate
export const useRemoveCandidateSkill = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      candidateId,
    }: {
      id: string;
      candidateId: string;
    }) => {
      const { error } = await supabase
        .from("candidate_skills")
        .delete()
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["candidate-skills", variables.candidateId],
      });
      toast({
        title: "Success",
        description: "Skill removed successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};

// Update a candidate's skill
export const useUpdateCandidateSkill = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      candidateId,
      proficiency_level,
      years_experience,
    }: {
      id: string;
      candidateId: string;
      proficiency_level?: "beginner" | "intermediate" | "advanced" | "expert";
      years_experience?: number;
    }) => {
      const updates: any = {};
      if (proficiency_level) updates.proficiency_level = proficiency_level;
      if (years_experience !== undefined)
        updates.years_experience = years_experience;

      const { data, error } = await supabase
        .from("candidate_skills")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["candidate-skills", variables.candidateId],
      });
      toast({
        title: "Success",
        description: "Skill updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};
