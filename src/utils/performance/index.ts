/**
 * Performance Utilities Index
 * Centralized exports for all performance optimization utilities
 */

import React from "react";

// Core performance caching
export {
  PerformanceCache,
  performanceCaches,
  memoizeWithPerformance,
  withPerformanceMonitoring,
  createCacheWarmer,
  usePerformanceCache,
} from "./performanceCache";

// Optimized data transformations
export {
  OptimizedTransformationPipeline,
  OptimizedTransformations,
  BatchProcessor,
  PerformanceOptimizer,
} from "./optimizedTransformations";

// Re-export commonly used utilities
export {
  splitTrimFilter,
  filterMap,
  uniqueFilter,
  groupBy,
  arrayIntersection,
  ArrayPatterns,
} from "@/utils/arrayOptimizations";

export {
  safeJsonParse,
  transformKeysToCamelCase,
  transformKeysToSnakeCase,
  batchTransform,
  clearTransformationCaches,
} from "@/utils/dataTransformers";

// Import performanceCaches for use in patterns
import { performanceCaches } from "./performanceCache";

/**
 * Performance optimization patterns and best practices
 */
export const PerformancePatterns = {
  /**
   * Debounced search with caching
   */
  createDebouncedSearch: <T>(
    searchFn: (query: string) => Promise<T[]>,
    delay: number = 300,
  ) => {
    let timeoutId: NodeJS.Timeout;

    return (query: string): Promise<T[]> => {
      return new Promise((resolve) => {
        clearTimeout(timeoutId);

        timeoutId = setTimeout(async () => {
          const cacheKey = `search-${query}`;
          const cached = performanceCaches.searchResults.get(cacheKey);

          if (cached) {
            resolve(cached);
            return;
          }

          try {
            const results = await searchFn(query);
            performanceCaches.searchResults.set(cacheKey, results);
            resolve(results);
          } catch (error) {
            console.error("Search error:", error);
            resolve([]);
          }
        }, delay);
      });
    };
  },

  /**
   * Optimized pagination with prefetching
   */
  createPaginatedLoader: <T>(
    loadFn: (page: number, size: number) => Promise<T[]>,
    pageSize: number = 20,
  ) => {
    const cache = new Map<number, T[]>();

    return {
      load: async (page: number): Promise<T[]> => {
        if (cache.has(page)) {
          return cache.get(page)!;
        }

        const data = await loadFn(page, pageSize);
        cache.set(page, data);

        // Prefetch next page
        if (!cache.has(page + 1)) {
          loadFn(page + 1, pageSize)
            .then((nextData) => cache.set(page + 1, nextData))
            .catch(() => {}); // Ignore prefetch errors
        }

        return data;
      },
      clearCache: () => cache.clear(),
      getCacheSize: () => cache.size,
    };
  },

  /**
   * Virtual scrolling optimization
   */
  createVirtualizedRenderer: <T>(
    items: T[],
    itemHeight: number,
    containerHeight: number,
  ) => {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const bufferSize = Math.max(5, Math.floor(visibleCount * 0.5));

    return (scrollTop: number) => {
      const startIndex = Math.max(
        0,
        Math.floor(scrollTop / itemHeight) - bufferSize,
      );
      const endIndex = Math.min(
        items.length - 1,
        startIndex + visibleCount + bufferSize * 2,
      );

      return {
        startIndex,
        endIndex,
        visibleItems: items.slice(startIndex, endIndex + 1),
        totalHeight: items.length * itemHeight,
        offsetY: startIndex * itemHeight,
      };
    };
  },
};

/**
 * Performance monitoring hooks for React components
 */
export const PerformanceHooks = {
  /**
   * Hook to monitor component render performance
   */
  useRenderPerformance: (componentName: string) => {
    const startTime = performance.now();

    React.useEffect(() => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      if (renderTime > 16) {
        // Warn if render takes longer than one frame
        console.warn(
          `[Render Performance] ${componentName} took ${renderTime.toFixed(2)}ms`,
        );
      }
    });
  },

  /**
   * Hook for performance-aware state updates
   */
  useOptimizedState: <T>(initialValue: T, debounceMs: number = 100) => {
    const [value, setValue] = React.useState(initialValue);
    const [debouncedValue, setDebouncedValue] = React.useState(initialValue);

    React.useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedValue(value);
      }, debounceMs);

      return () => clearTimeout(timer);
    }, [value, debounceMs]);

    return [debouncedValue, setValue] as const;
  },
};

// Type definitions for better TypeScript support
export interface PerformanceMetrics {
  cacheHits: number;
  cacheMisses: number;
  totalRequests: number;
  averageResponseTime: number;
  hitRate: number;
  size: number;
}

export interface TransformationOptions {
  useCache?: boolean;
  cacheKey?: string;
  batchSize?: number;
  enableMonitoring?: boolean;
}
