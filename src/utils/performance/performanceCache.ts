/**
 * Performance Cache Utilities
 * Optimized caching and memoization for performance-critical operations
 */

import React from "react";

interface CacheEntry<T> {
  value: T;
  timestamp: number;
  hitCount: number;
}

interface PerformanceMetrics {
  cacheHits: number;
  cacheMisses: number;
  totalRequests: number;
  averageResponseTime: number;
}

/**
 * High-performance cache with TTL and LRU eviction
 */
class PerformanceCache<K, V> {
  private cache = new Map<K, CacheEntry<V>>();
  private readonly maxSize: number;
  private readonly ttl: number; // Time to live in milliseconds
  private metrics: PerformanceMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    totalRequests: 0,
    averageResponseTime: 0,
  };

  constructor(maxSize: number = 100, ttlMs: number = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.ttl = ttlMs;
  }

  get(key: K): V | undefined {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    const entry = this.cache.get(key);
    const responseTime = Date.now() - startTime;

    // Update average response time
    this.metrics.averageResponseTime =
      (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) +
        responseTime) /
      this.metrics.totalRequests;

    if (!entry) {
      this.metrics.cacheMisses++;
      return undefined;
    }

    // Check if entry is expired
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      this.metrics.cacheMisses++;
      return undefined;
    }

    // Update hit count and move to end (LRU)
    entry.hitCount++;
    this.cache.delete(key);
    this.cache.set(key, entry);
    this.metrics.cacheHits++;

    return entry.value;
  }

  set(key: K, value: V): void {
    // Remove oldest entry if cache is full
    if (this.cache.size >= this.maxSize) {
      // Find LRU entry (lowest hit count + oldest timestamp)
      let lruKey: K | undefined;
      let lruScore = Infinity;

      for (const [k, entry] of this.cache.entries()) {
        const score =
          entry.hitCount * 0.3 + (Date.now() - entry.timestamp) * 0.7;
        if (score < lruScore) {
          lruScore = score;
          lruKey = k;
        }
      }

      if (lruKey !== undefined) {
        this.cache.delete(lruKey);
      }
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      hitCount: 0,
    });
  }

  clear(): void {
    this.cache.clear();
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      totalRequests: 0,
      averageResponseTime: 0,
    };
  }

  getMetrics(): PerformanceMetrics & { size: number; hitRate: number } {
    const hitRate =
      this.metrics.totalRequests > 0
        ? (this.metrics.cacheHits / this.metrics.totalRequests) * 100
        : 0;

    return {
      ...this.metrics,
      size: this.cache.size,
      hitRate: Math.round(hitRate * 100) / 100,
    };
  }

  has(key: K): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Check if entry is expired
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }
}

/**
 * Optimized function memoization with performance tracking
 */
export function memoizeWithPerformance<Args extends unknown[], Return>(
  fn: (...args: Args) => Return,
  keyFn?: (...args: Args) => string,
  options: { maxSize?: number; ttl?: number } = {},
): (...args: Args) => Return {
  const cache = new PerformanceCache<string, Return>(
    options.maxSize || 50,
    options.ttl || 5 * 60 * 1000,
  );

  const defaultKeyFn = (...args: Args) => JSON.stringify(args);
  const getKey = keyFn || defaultKeyFn;

  const memoized = (...args: Args): Return => {
    const key = getKey(...args);
    const cached = cache.get(key);

    if (cached !== undefined) {
      return cached;
    }

    const result = fn(...args);
    cache.set(key, result);
    return result;
  };

  // Attach performance metrics
  (memoized as any).getMetrics = () => cache.getMetrics();
  (memoized as any).clearCache = () => cache.clear();

  return memoized;
}

/**
 * Performance monitoring for expensive computations
 */
export function withPerformanceMonitoring<T>(name: string, fn: () => T): T {
  const startTime = performance.now();
  const startMemory = (performance as any).memory?.usedJSHeapSize || 0;

  try {
    const result = fn();
    const endTime = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize || 0;

    const executionTime = endTime - startTime;
    const memoryDelta = endMemory - startMemory;

    // Log performance metrics (in development only)
    if (process.env.NODE_ENV === "development") {
      console.log(`[Performance] ${name}:`, {
        executionTime: `${executionTime.toFixed(2)}ms`,
        memoryDelta: `${(memoryDelta / 1024 / 1024).toFixed(2)}MB`,
        timestamp: new Date().toISOString(),
      });
    }

    // Warn about slow operations
    if (executionTime > 100) {
      console.warn(
        `[Performance Warning] ${name} took ${executionTime.toFixed(2)}ms`,
      );
    }

    return result;
  } catch (error) {
    const endTime = performance.now();
    console.error(
      `[Performance Error] ${name} failed after ${(endTime - startTime).toFixed(2)}ms:`,
      error,
    );
    throw error;
  }
}

/**
 * Global performance cache instances for common use cases
 */
export const performanceCaches = {
  // Search results cache
  searchResults: new PerformanceCache<string, any[]>(200, 2 * 60 * 1000), // 2 minutes

  // User data cache
  userData: new PerformanceCache<string, any>(100, 10 * 60 * 1000), // 10 minutes

  // Analytics cache
  analytics: new PerformanceCache<string, any>(50, 5 * 60 * 1000), // 5 minutes

  // System metrics cache
  systemMetrics: new PerformanceCache<string, any>(30, 1 * 60 * 1000), // 1 minute
};

/**
 * Debounced cache warming for predictive caching
 */
export function createCacheWarmer<T>(
  cache: PerformanceCache<string, T>,
  warmerFn: (key: string) => Promise<T>,
  debounceMs: number = 1000,
) {
  const warmingQueue = new Set<string>();
  let warmingTimeout: NodeJS.Timeout | null = null;

  return (key: string) => {
    warmingQueue.add(key);

    if (warmingTimeout) {
      clearTimeout(warmingTimeout);
    }

    warmingTimeout = setTimeout(async () => {
      const keys = Array.from(warmingQueue);
      warmingQueue.clear();

      // Warm cache for all queued keys
      await Promise.allSettled(
        keys.map(async (k) => {
          try {
            const value = await warmerFn(k);
            cache.set(k, value);
          } catch (error) {
            console.error(`Failed to warm cache for key ${k}:`, error);
          }
        }),
      );
    }, debounceMs);
  };
}

/**
 * React hook for performance-aware caching
 */
export function usePerformanceCache<T>(
  key: string,
  factory: () => T,
  deps: React.DependencyList = [],
): T {
  const cache = performanceCaches.userData;

  return React.useMemo(() => {
    const cached = cache.get(key);
    if (cached !== undefined) {
      return cached;
    }

    const value = withPerformanceMonitoring(
      `usePerformanceCache:${key}`,
      factory,
    );
    cache.set(key, value);
    return value;
  }, [key, ...deps]);
}

export { PerformanceCache };
