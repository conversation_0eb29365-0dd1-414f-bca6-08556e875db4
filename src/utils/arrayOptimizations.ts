/**
 * Optimized Array Operations Utilities
 * High-performance array processing functions to replace common inefficient patterns
 */

/**
 * Optimized split, trim, and filter operation
 * Replaces: array.split(',').map(s => s.trim()).filter(Boolean)
 * Performance: Single-pass operation vs 3-pass chain
 */
export function splitTrimFilter(value: string, separator = ","): string[] {
  if (!value || typeof value !== "string") return [];

  const result: string[] = [];
  const parts = value.split(separator);

  for (let i = 0; i < parts.length; i++) {
    const trimmed = parts[i].trim();
    if (trimmed) {
      result.push(trimmed);
    }
  }

  return result;
}

/**
 * Optimized filter and map combination
 * Single-pass operation with predicate and transformer
 */
export function filterMap<T, U>(
  array: T[],
  predicate: (item: T) => boolean,
  mapper: (item: T) => U,
): U[] {
  if (!Array.isArray(array)) return [];

  const result: U[] = [];

  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    if (predicate(item)) {
      result.push(mapper(item));
    }
  }

  return result;
}

/**
 * Optimized unique filtering
 * Better performance than [...new Set(array)] for primitives
 */
export function uniqueFilter<T>(array: T[]): T[] {
  if (!Array.isArray(array)) return [];
  if (array.length <= 1) return [...array];

  const seen = new Set<T>();
  const result: T[] = [];

  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    if (!seen.has(item)) {
      seen.add(item);
      result.push(item);
    }
  }

  return result;
}

/**
 * Optimized array grouping by key
 * Single-pass grouping operation
 */
export function groupBy<T, K extends string | number>(
  array: T[],
  keySelector: (item: T) => K,
): Record<K, T[]> {
  if (!Array.isArray(array)) return {} as Record<K, T[]>;

  const groups = {} as Record<K, T[]>;

  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    const key = keySelector(item);

    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
  }

  return groups;
}

/**
 * Optimized find with multiple conditions
 * Early termination with multiple predicates
 */
export function findWithConditions<T>(
  array: T[],
  conditions: Array<(item: T) => boolean>,
): T | undefined {
  if (!Array.isArray(array) || conditions.length === 0) return undefined;

  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    let allConditionsMet = true;

    for (let j = 0; j < conditions.length; j++) {
      if (!conditions[j](item)) {
        allConditionsMet = false;
        break;
      }
    }

    if (allConditionsMet) {
      return item;
    }
  }

  return undefined;
}

/**
 * Optimized array difference
 * Better than filter + includes for large arrays
 */
export function arrayDifference<T>(array1: T[], array2: T[]): T[] {
  if (!Array.isArray(array1)) return [];
  if (!Array.isArray(array2) || array2.length === 0) return [...array1];

  const set2 = new Set(array2);
  const result: T[] = [];

  for (let i = 0; i < array1.length; i++) {
    const item = array1[i];
    if (!set2.has(item)) {
      result.push(item);
    }
  }

  return result;
}

/**
 * Optimized array intersection
 * Single-pass operation using Set
 */
export function arrayIntersection<T>(array1: T[], array2: T[]): T[] {
  if (!Array.isArray(array1) || !Array.isArray(array2)) return [];
  if (array1.length === 0 || array2.length === 0) return [];

  // Use the smaller array for the Set to optimize memory
  const [smaller, larger] =
    array1.length <= array2.length ? [array1, array2] : [array2, array1];
  const smallerSet = new Set(smaller);
  const result: T[] = [];

  for (let i = 0; i < larger.length; i++) {
    const item = larger[i];
    if (smallerSet.has(item)) {
      result.push(item);
      smallerSet.delete(item); // Avoid duplicates
    }
  }

  return result;
}

/**
 * Optimized batch processing for large arrays
 * Prevents UI blocking with automatic batching
 */
export async function processBatched<T, U>(
  array: T[],
  processor: (item: T) => U | Promise<U>,
  batchSize = 100,
): Promise<U[]> {
  if (!Array.isArray(array)) return [];

  const results: U[] = [];

  for (let i = 0; i < array.length; i += batchSize) {
    const batch = array.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(processor));
    results.push(...batchResults);

    // Yield control to event loop between batches
    if (i + batchSize < array.length) {
      await new Promise((resolve) => setTimeout(resolve, 0));
    }
  }

  return results;
}

/**
 * Optimized sorting with caching for expensive comparisons
 */
export function sortWithCache<T, K>(
  array: T[],
  keySelector: (item: T) => K,
  comparer?: (a: K, b: K) => number,
): T[] {
  if (!Array.isArray(array)) return [];
  if (array.length <= 1) return [...array];

  // Create cache of sort keys to avoid recomputation
  const cached = array.map((item) => ({
    item,
    key: keySelector(item),
  }));

  // Sort using cached keys
  cached.sort((a, b) => {
    if (comparer) {
      return comparer(a.key, b.key);
    }

    // Default comparison
    if (a.key < b.key) return -1;
    if (a.key > b.key) return 1;
    return 0;
  });

  // Extract sorted items
  return cached.map((item) => item.item);
}

/**
 * Common patterns for optimized operations
 */
export const ArrayPatterns = {
  /**
   * Optimized email list parsing
   */
  parseEmailList: (emailString: string): string[] => {
    return splitTrimFilter(emailString, ",").filter(
      (email) => email.includes("@") && email.includes("."),
    );
  },

  /**
   * Optimized tag processing
   */
  processTags: (tagString: string): string[] => {
    return uniqueFilter(
      splitTrimFilter(tagString, ",").map((tag) => tag.toLowerCase()),
    );
  },

  /**
   * Optimized skill extraction
   */
  extractSkills: (
    skillString: string,
  ): Array<{ name: string; level?: string }> => {
    return splitTrimFilter(skillString, ",").map((skill) => {
      const parts = skill.split(":");
      return {
        name: parts[0].trim(),
        level: parts[1]?.trim(),
      };
    });
  },
};
