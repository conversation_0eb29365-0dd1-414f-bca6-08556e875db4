import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { parseResumeText } from "./gemini";

interface ParsedResume {
  name?: string;
  email?: string;
  phone?: string;
  skills: string[];
  experience: {
    title?: string;
    company?: string;
    duration?: string;
    description?: string;
  }[];
  education: {
    degree?: string;
    institution?: string;
    year?: string;
  }[];
  summary?: string;
}

/**
 * Parse a resume file and extract structured information
 */
export async function parseResume(file: File): Promise<ParsedResume | null> {
  try {
    // Read the file content
    const fileText = await file.text();

    // Use Gemini to parse the resume
    const parsedResume = await parseResumeText(fileText);

    return parsedResume;
  } catch (error) {
    console.error("Error parsing resume:", error);
    toast.error("Failed to parse resume. Please try again.");
    return null;
  }
}

/**
 * Convert parsed resume data to candidate format for database
 */
export function convertParsedResumeToCandidate(parsedResume: ParsedResume) {
  // Extract skills and convert to the format expected by the database
  const skills = parsedResume.skills.map((skill) => ({
    name: skill,
    level: "Intermediate",
    years: 2,
  }));

  // Extract experience
  const experience =
    parsedResume.experience.length > 0
      ? `${parsedResume.experience[0].duration}`
      : "";

  // Create candidate object
  return {
    name: parsedResume.name || "",
    email: parsedResume.email || "",
    role: parsedResume.experience[0]?.title || "",
    phone: parsedResume.phone || "",
    skills: skills,
    experience: experience,
    ai_summary: parsedResume.summary || "",
    tags: parsedResume.skills.slice(0, 3),
  };
}
