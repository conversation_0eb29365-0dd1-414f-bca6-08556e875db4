import { describe, it, expect } from "vitest";

describe("Skill Filter SQL Generation", () => {
  it("should generate correct SQL filter for single skill", () => {
    const skillNames = ["React"];
    const expectedFilter = `normalized_skills @> '[{"name": "React"}]'::jsonb`;

    const actualFilter = skillNames
      .map(
        (skillName) =>
          `normalized_skills @> '[{"name": "${skillName}"}]'::jsonb`,
      )
      .join(" OR ");

    expect(actualFilter).toBe(expectedFilter);
  });

  it("should generate correct SQL filter for multiple skills with OR", () => {
    const skillNames = ["React", "TypeScript", "Node.js"];
    const expectedFilter = `normalized_skills @> '[{"name": "React"}]'::jsonb OR normalized_skills @> '[{"name": "TypeScript"}]'::jsonb OR normalized_skills @> '[{"name": "Node.js"}]'::jsonb`;

    const actualFilter = skillNames
      .map(
        (skillName) =>
          `normalized_skills @> '[{"name": "${skillName}"}]'::jsonb`,
      )
      .join(" OR ");

    expect(actualFilter).toBe(expectedFilter);
  });

  it("should handle skill names with special characters", () => {
    const skillNames = ["C++", "C#", ".NET"];

    const actualFilter = skillNames
      .map(
        (skillName) =>
          `normalized_skills @> '[{"name": "${skillName}"}]'::jsonb`,
      )
      .join(" OR ");

    expect(actualFilter).toContain("C++");
    expect(actualFilter).toContain("C#");
    expect(actualFilter).toContain(".NET");
  });
});
