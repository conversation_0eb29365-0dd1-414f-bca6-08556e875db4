import { GoogleGenerativeAI } from "@google/generative-ai";

// Initialize the Gemini API with environment variable
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

if (!API_KEY) {
  throw new Error(
    "VITE_GEMINI_API_KEY environment variable is not set. Please add it to your .env file.",
  );
}

const genAI = new GoogleGenerativeAI(API_KEY);

// Get the generative model
const getModel = () => {
  return genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
};

/**
 * Generate text using Gemini API
 * @param prompt The prompt to send to Gemini
 * @param systemPrompt Optional system prompt to guide the model
 * @returns The generated text
 */
export async function generateText(
  prompt: string,
  systemPrompt?: string,
): Promise<string> {
  try {
    const model = getModel();

    const generationConfig = {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 1024,
    };

    const chat = model.startChat({
      generationConfig,
    });

    const result = await chat.sendMessage(prompt);
    const response = result.response;
    return response.text();
  } catch (error) {
    console.error("Error generating text with Gemini:", error);
    throw new Error(`Failed to generate text: ${error.message}`);
  }
}

/**
 * Generate interview questions based on job title and description
 * @param jobTitle The job title
 * @param jobDescription Optional job description
 * @returns Array of interview questions
 */
export async function generateInterviewQuestions(
  jobTitle: string,
  jobDescription?: string,
) {
  const prompt = `
Generate 5 interview questions for a ${jobTitle} position.
${jobDescription ? `Job Description: ${jobDescription}` : ""}

For each question, include:
1. The question text
2. The category (technical, behavioral, cultural, or experience)
3. The difficulty level (easy, medium, or hard)
4. A brief rationale for asking this question

Format the response as a JSON array of objects with the following structure:
[
  {
    "id": "unique-id-1",
    "question": "Question text here",
    "category": "technical|behavioral|cultural|experience",
    "difficulty": "easy|medium|hard",
    "rationale": "Rationale here"
  }
]
`;

  const systemPrompt =
    "You are an expert technical interviewer with deep knowledge of hiring best practices. Generate thoughtful, relevant interview questions that will help assess a candidate's skills, experience, and cultural fit.";

  try {
    const response = await generateText(prompt, systemPrompt);
    // Clean the response by removing markdown code block delimiters
    const cleanedResponse = response
      .replace(/```json\s*/, "")
      .replace(/```\s*$/, "")
      .trim();
    // Parse the JSON response
    const questions = JSON.parse(cleanedResponse);
    return questions;
  } catch (error) {
    console.error("Error generating interview questions:", error);
    throw new Error("Failed to generate interview questions");
  }
}

/**
 * Generate a job description based on title and department
 * @param title The job title
 * @param department The department
 * @returns Generated job description
 */
export async function generateJobDescription(
  title: string,
  department: string,
): Promise<string> {
  const prompt = `
Generate a comprehensive job description for a ${title} position in the ${department} department.

Include the following sections:
1. Brief overview of the role
2. Key Responsibilities (5-7 bullet points)
3. Requirements (5-7 bullet points)
4. Benefits (3-5 bullet points)

Make the description professional, engaging, and specific to the role.
`;

  const systemPrompt =
    "You are an expert technical recruiter with deep knowledge of job descriptions across various industries. Create compelling, accurate job descriptions that attract qualified candidates.";

  try {
    return await generateText(prompt, systemPrompt);
  } catch (error) {
    console.error("Error generating job description:", error);
    throw new Error("Failed to generate job description");
  }
}

/**
 * Parse a resume and extract structured information
 * @param resumeText The text content of the resume
 * @returns Structured resume data
 */
export async function parseResumeText(resumeText: string) {
  const prompt = `
Parse the following resume text and extract structured information in JSON format:

${resumeText}

Return a JSON object with the following structure:
{
  "name": "Candidate's full name",
  "email": "Email address",
  "phone": "Phone number",
  "skills": ["Skill 1", "Skill 2", ...],
  "experience": [
    {
      "title": "Job title",
      "company": "Company name",
      "duration": "Duration (e.g., 2020-Present)",
      "description": "Brief description of role"
    }
  ],
  "education": [
    {
      "degree": "Degree name",
      "institution": "Institution name",
      "year": "Graduation year"
    }
  ],
  "summary": "Brief professional summary"
}
`;

  const systemPrompt =
    "You are an expert resume parser. Extract relevant information from resumes accurately and organize it into a structured format.";

  try {
    const response = await generateText(prompt, systemPrompt);
    // Clean the response by removing markdown code block delimiters
    const cleanedResponse = response
      .replace(/```json\s*/, "")
      .replace(/```\s*$/, "")
      .trim();
    // Parse the JSON response
    return JSON.parse(cleanedResponse);
  } catch (error) {
    console.error("Error parsing resume:", error);
    throw new Error("Failed to parse resume");
  }
}

/**
 * Analyze interview feedback and provide a summary
 * @param feedback The interview feedback text
 * @returns Analysis with summary, strengths, weaknesses, and recommendation
 */
export async function analyzeInterviewFeedback(feedback: string) {
  const prompt = `
Analyze the following interview feedback and provide a structured summary:

FEEDBACK:
${feedback}

Please provide a JSON response with the following structure:
{
  "summary": "Brief overall summary of the feedback",
  "strengths": ["Strength 1", "Strength 2", ...],
  "weaknesses": ["Weakness 1", "Weakness 2", ...],
  "recommendation": "Hire/Reject/Consider further with specific reasoning",
  "sentiment": "positive/negative/neutral with a score from 0-100"
}
`;

  const systemPrompt =
    "You are an expert HR analyst who can objectively evaluate interview feedback. Provide balanced, fair assessments that highlight both strengths and areas for improvement.";

  try {
    const response = await generateText(prompt, systemPrompt);
    // Clean the response by removing markdown code block delimiters
    const cleanedResponse = response
      .replace(/```json\s*/, "")
      .replace(/```\s*$/, "")
      .trim();
    // Parse the JSON response
    return JSON.parse(cleanedResponse);
  } catch (error) {
    console.error("Error analyzing interview feedback:", error);
    throw new Error("Failed to analyze interview feedback");
  }
}

/**
 * Generate a personalized message based on candidate profile and context
 * @param candidateProfile The candidate's profile information
 * @param context Additional context like job details or communication history
 * @returns Personalized message text
 */
export async function generatePersonalizedMessage(
  candidateProfile: any,
  context: any,
) {
  const prompt = `
Generate a personalized message for a candidate based on their profile and the provided context.

CANDIDATE PROFILE:
${JSON.stringify(candidateProfile, null, 2)}

CONTEXT:
${JSON.stringify(context, null, 2)}

Create a professional, personalized message that references specific details from the candidate's profile
and addresses the context appropriately. The message should be warm, engaging, and tailored to this specific candidate.

Return ONLY the message text without any additional explanations or formatting.
`;

  const systemPrompt =
    "You are an expert recruiter with excellent communication skills. Create personalized, professional messages that engage candidates effectively.";

  try {
    return await generateText(prompt, systemPrompt);
  } catch (error) {
    console.error("Error generating personalized message:", error);
    throw new Error("Failed to generate personalized message");
  }
}

/**
 * Optimize a job description for clarity, inclusivity, and effectiveness
 * @param jobDescription The original job description
 * @param jobTitle The job title
 * @param department The department
 * @returns Optimized job description with suggestions
 */
export async function optimizeJobDescription(
  jobDescription: string,
  jobTitle: string,
  department: string,
) {
  const prompt = `
Analyze and optimize the following job description for clarity, inclusivity, and effectiveness:

JOB TITLE: ${jobTitle}
DEPARTMENT: ${department}

ORIGINAL DESCRIPTION:
${jobDescription}

Please provide a JSON response with the following structure:
{
  "optimizedDescription": "The improved job description text",
  "suggestions": [
    {
      "type": "clarity|inclusivity|effectiveness",
      "original": "The original problematic text",
      "improved": "The suggested improvement",
      "explanation": "Why this change improves the description"
    }
  ],
  "keywordSuggestions": ["keyword1", "keyword2", ...],
  "overallAssessment": "Brief assessment of the original description"
}
`;

  const systemPrompt =
    "You are an expert in talent acquisition and job description optimization. Provide clear, inclusive, and effective job descriptions that attract diverse, qualified candidates.";

  try {
    const response = await generateText(prompt, systemPrompt);
    // Clean the response by removing markdown code block delimiters
    const cleanedResponse = response
      .replace(/```json\s*/, "")
      .replace(/```\s*$/, "")
      .trim();
    // Parse the JSON response
    return JSON.parse(cleanedResponse);
  } catch (error) {
    console.error("Error optimizing job description:", error);
    throw new Error("Failed to optimize job description");
  }
}

/**
 * Screen a candidate against a job description
 * @param candidateProfile The candidate's profile or resume
 * @param jobDescription The job description
 * @returns Screening results with match score and analysis
 */
export async function screenCandidate(
  candidateProfile: any,
  jobDescription: string,
) {
  const candidateInfo =
    typeof candidateProfile === "string"
      ? candidateProfile
      : JSON.stringify(candidateProfile);

  const prompt = `
Screen this candidate against the job description and provide a detailed analysis:

CANDIDATE INFORMATION:
${candidateInfo}

JOB DESCRIPTION:
${jobDescription}

Provide a JSON response with:
1. An overall match score (0-100)
2. Key strengths that match the job requirements
3. Potential gaps or areas for improvement
4. A brief summary of the candidate's fit

Format:
{
  "score": 85,
  "strengths": ["Strength 1", "Strength 2", ...],
  "gaps": ["Gap 1", "Gap 2", ...],
  "summary": "Brief analysis of the candidate's fit"
}
`;

  const systemPrompt =
    "You are an expert AI recruiter. Analyze candidates objectively against job requirements, providing fair and accurate assessments of their qualifications.";

  try {
    const response = await generateText(prompt, systemPrompt);
    // Clean the response by removing markdown code block delimiters
    const cleanedResponse = response
      .replace(/```json\s*/, "")
      .replace(/```\s*$/, "")
      .trim();
    // Parse the JSON response
    return JSON.parse(cleanedResponse);
  } catch (error) {
    console.error("Error screening candidate:", error);
    throw new Error("Failed to screen candidate");
  }
}
