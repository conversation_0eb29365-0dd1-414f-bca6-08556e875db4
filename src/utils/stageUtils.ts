/**
 * Utility functions for consistent stage and rating display across the application
 */

/**
 * Get consistent color classes for stage badges
 * @param stage - The stage name
 * @returns Tailwind classes for background and text color
 */
export const getStageColorClasses = (stage: string): string => {
  const stageColors: Record<string, string> = {
    // Initial application stages
    "Applied": "bg-blue-100 text-blue-700",
    "New": "bg-blue-100 text-blue-700",
    
    // Screening stages
    "Phone Screen": "bg-yellow-100 text-yellow-700",
    "Screening": "bg-yellow-100 text-yellow-700",
    
    // Interview stages
    "Interview": "bg-purple-100 text-purple-700",
    "Technical Interview": "bg-purple-100 text-purple-700",
    "Behavioral Interview": "bg-purple-100 text-purple-700",
    
    // Final stages
    "Final Round": "bg-orange-100 text-orange-700",
    "Final Interview": "bg-orange-100 text-orange-700",
    
    // Decision stages
    "Offer": "bg-green-100 text-green-700",
    "Offer Extended": "bg-green-100 text-green-700",
    "Offer Accepted": "bg-green-100 text-green-700",
    
    // Completion stages
    "Hired": "bg-emerald-100 text-emerald-700",
    "Onboarding": "bg-emerald-100 text-emerald-700",
    
    // Rejection stages
    "Rejected": "bg-red-100 text-red-700",
    "Declined": "bg-red-100 text-red-700",
    "Withdrawn": "bg-red-100 text-red-700",
    
    // On hold stages
    "On Hold": "bg-gray-100 text-gray-700",
    "Paused": "bg-gray-100 text-gray-700",
  };
  
  return stageColors[stage] || "bg-gray-100 text-gray-700";
};

/**
 * Get progress bar colors for stages (more vibrant than badge colors)
 * @param stage - The stage name
 * @returns Tailwind class for progress bar color
 */
export const getStageProgressColor = (stage: string): string => {
  const progressColors: Record<string, string> = {
    // Initial application stages
    "Applied": "bg-blue-500",
    "New": "bg-blue-500",
    
    // Screening stages
    "Phone Screen": "bg-yellow-500",
    "Screening": "bg-yellow-500",
    
    // Interview stages
    "Interview": "bg-purple-500",
    "Technical Interview": "bg-purple-500",
    "Behavioral Interview": "bg-purple-500",
    
    // Final stages
    "Final Round": "bg-orange-500",
    "Final Interview": "bg-orange-500",
    
    // Decision stages
    "Offer": "bg-green-500",
    "Offer Extended": "bg-green-500",
    "Offer Accepted": "bg-green-500",
    
    // Completion stages
    "Hired": "bg-emerald-500",
    "Onboarding": "bg-emerald-500",
    
    // Rejection stages
    "Rejected": "bg-red-500",
    "Declined": "bg-red-500",
    "Withdrawn": "bg-red-500",
    
    // On hold stages
    "On Hold": "bg-gray-500",
    "Paused": "bg-gray-500",
  };
  
  return progressColors[stage] || "bg-gray-500";
};

/**
 * Legacy function for backward compatibility
 * @deprecated Use getStageColorClasses instead
 */
export const getStageColor = getStageColorClasses;

/**
 * Star rating configuration
 */
export const STAR_RATING_CONFIG = {
  maxStars: 5,
  filledClass: "fill-yellow-400 text-yellow-400",
  emptyClass: "text-gray-300",
  sizes: {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
  }
} as const;
