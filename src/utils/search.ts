import { supabase } from "@/integrations/supabase/client";
import { searchCandidates } from "./searchCandidates";
import { searchJobs } from "./searchJobs";
import { searchMessages } from "./searchMessages";
import { calculateRelevanceScore } from "./searchUtils";

export interface SearchFilters {
  department?: string;
  location?: {
    address: string;
    radius: number;
  };
  experience?: string;
  remote?: boolean;
  urgent?: boolean;
  remoteOnly?: boolean;
  visaSponsor?: boolean;
  skills?: {
    name: string;
    required: boolean;
    proficiency?: "beginner" | "intermediate" | "expert";
  }[];
  // Message-specific filters
  status?: "unread" | "read" | "archived";
  isStarred?: boolean;
  followUp?: boolean;
  reminder?: boolean;
  jobType?: string;
  salaryRange?: string;
}

export interface SearchResult {
  id: string;
  title: string;
  type: "candidate" | "job" | "message";
  description: string;
  location?: string;
  match?: number;
  avatar?: string;
  tags?: string[];
  status?: string;
  time?: string;
}

export interface HighlightedText {
  text: string;
  isHighlighted: boolean;
}

export const highlightSearchResults = (text: string, query: string): HighlightedText[] => {
  if (!query || !text) return [{ text, isHighlighted: false }];

  // Handle multiple words in query
  const words = query.trim().split(/\s+/);
  let result: HighlightedText[] = [{ text, isHighlighted: false }];

  words.forEach((word) => {
    const newResult: HighlightedText[] = [];
    
    result.forEach((segment) => {
      if (segment.isHighlighted) {
        // If already highlighted, keep as is
        newResult.push(segment);
      } else {
        // Split the text by the search word (case insensitive)
        const regex = new RegExp(`(${word})`, "gi");
        const parts = segment.text.split(regex);
        
        parts.forEach((part, index) => {
          if (part.toLowerCase() === word.toLowerCase()) {
            // This part matches the search term
            newResult.push({ text: part, isHighlighted: true });
          } else if (part.length > 0) {
            // This part doesn't match
            newResult.push({ text: part, isHighlighted: false });
          }
        });
      }
    });
    
    result = newResult;
  });

  return result;
};

export const searchAll = async (query: string, filters: SearchFilters = {}) => {
  const candidates = await searchCandidates(query, filters);
  const jobs = await searchJobs(query, filters);
  const messages = await searchMessages(query, {
    status: filters.status as any,
    isStarred: filters.isStarred,
    followUp: filters.followUp,
    reminder: filters.reminder,
  });

  return {
    candidates,
    jobs,
    messages,
    total: candidates.length + jobs.length + messages.length,
  };
};

// Re-export individual search functions
export { searchCandidates, searchJobs, searchMessages };
