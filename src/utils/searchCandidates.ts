import { supabase } from "@/integrations/supabase/client";
import { CandidateType } from "@/types/candidate";
import { calculateRelevanceScore } from "./searchUtils";

interface SearchFilters {
  location?: {
    address: string;
    radius: number;
  };
  skills?: {
    name: string;
    required: boolean;
    proficiency?: "beginner" | "intermediate" | "expert";
  }[];
  remoteOnly?: boolean;
  visaSponsor?: boolean;
}

export const searchCandidates = async (
  query: string,
  filters: SearchFilters = {},
): Promise<CandidateType[]> => {
  try {
    console.log("🔍 Searching candidates with query:", query);

    // Get current user
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      console.log("❌ No authenticated user found");
      return [];
    }

    console.log("👤 Current user ID:", user.id);

    let supabaseQuery = supabase
      .from("candidates_with_normalized_data")
      .select("*")
      .eq("user_id", user.id);

    // Apply search if query is provided
    if (query.trim()) {
      const searchTerm = `%${query.trim()}%`;

      // Search across multiple columns using OR conditions
      supabaseQuery = supabaseQuery.or(
        `name.ilike.${searchTerm},role.ilike.${searchTerm},email.ilike.${searchTerm},location.ilike.${searchTerm},industry.ilike.${searchTerm},ai_summary.ilike.${searchTerm}`,
      );
    }

    // Apply location filter
    if (filters.location?.address) {
      supabaseQuery = supabaseQuery.ilike(
        "location",
        `%${filters.location.address}%`,
      );
    }

    // Apply skills filter
    if (filters.skills && filters.skills.length > 0) {
      // Build a custom filter for JSONB array searching
      // We need to check if any of the skill names match in the normalized_skills array
      const skillNames = filters.skills.map((skill) => skill.name);

      // Create a raw SQL filter that checks if any skill name exists in the JSONB array
      // This uses the @> operator to check if the JSONB array contains any objects with matching names
      const skillFilter = skillNames
        .map(
          (skillName) =>
            `normalized_skills @> '[{"name": "${skillName}"}]'::jsonb`,
        )
        .join(" OR ");

      // Apply the custom filter
      supabaseQuery = supabaseQuery.or(skillFilter);
    }

    // Apply remote preference filter
    if (filters.remoteOnly) {
      supabaseQuery = supabaseQuery.in("remote_preference", [
        "Remote",
        "Hybrid",
      ]);
    }

    // Apply visa sponsorship filter
    if (filters.visaSponsor) {
      supabaseQuery = supabaseQuery.neq("visa_status", "US Citizen").neq("visa_status", "Green Card Holder");
    }

    const { data, error } = await supabaseQuery.limit(50);

    if (error) {
      console.error("❌ Search error:", error);
      throw error;
    }

    console.log(
      "📊 Raw search results:",
      data?.length || 0,
      "candidates found",
    );

    if (!data || data.length === 0) {
      console.log("⚠️ No candidates found in search");
      return [];
    }

    // Transform and sort the database data to match the CandidateType interface
    const results = (data || []).map((candidate) => ({
      id: candidate.id,
      name: candidate.name,
      role: candidate.role,
      email: candidate.email,
      phone: candidate.phone || "",
      location: candidate.location || "",
      avatar: candidate.avatar || "/placeholder.svg",
      recruiter: {
        id: candidate.recruiter_id || "",
        name: candidate.recruiter_name || "",
        avatar: candidate.recruiter_avatar || "/placeholder.svg",
      },
      tags: candidate.normalized_tags
        ? candidate.normalized_tags.map(tag => tag.name)
        : [],
      socialLinks: {
        github: candidate.github_url || "",
        linkedin: candidate.linkedin_url || "",
        twitter: candidate.twitter_url || "",
      },
      relationshipScore: candidate.relationship_score || 0,
      experience: candidate.experience || "",
      industry: candidate.industry || "",
      remotePreference: candidate.remote_preference || "",
      visaStatus: candidate.visa_status || "",
      skills: Array.isArray(candidate.normalized_skills)
        ? (candidate.normalized_skills as {
            name: string;
            level: string;
            years: number;
            category?: string;
          }[])
        : [],
      normalized_skills: Array.isArray(candidate.normalized_skills)
        ? (candidate.normalized_skills as {
            name: string;
            level: string;
            years: number;
            category?: string;
          }[])
        : [],
      aiSummary: candidate.ai_summary || "",
      matchedJobs: [],
      createdAt: candidate.created_at,
      updatedAt: candidate.updated_at,
    }));

    // Sort results by relevance to the search query
    if (query.trim()) {
      results.sort((a, b) => {
        const skillsA = (a.normalized_skills || a.skills || [])
          .map((s) => (typeof s === "string" ? s : s.name))
          .join(" ");
        const skillsB = (b.normalized_skills || b.skills || [])
          .map((s) => (typeof s === "string" ? s : s.name))
          .join(" ");
        const scoreA = calculateRelevanceScore(
          `${a.name} ${a.role} ${a.location} ${skillsA}`,
          query,
        );
        const scoreB = calculateRelevanceScore(
          `${b.name} ${b.role} ${b.location} ${skillsB}`,
          query,
        );
        return scoreB - scoreA;
      });
    } else {
      // If no query, sort by created_at
      results.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );
    }

    return results;
  } catch (error) {
    console.error("Error searching candidates:", error);
    return [];
  }
};
