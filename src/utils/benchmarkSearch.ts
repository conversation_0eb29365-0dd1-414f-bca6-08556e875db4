import { searchCandidatesOptimized } from "./optimizedSearch";
import { CandidateType } from "@/types/candidate";

// Generate mock candidates for benchmarking
function generateMockCandidates(count: number): CandidateType[] {
  const skills = [
    "JavaScript",
    "TypeScript",
    "React",
    "Vue",
    "Angular",
    "Node.js",
    "Python",
    "Django",
    "Java",
    "Spring",
    "C#",
    ".NET",
    "Ruby",
    "Rails",
    "PHP",
    "Go",
    "Rust",
    "Swift",
    "Kotlin",
    "Flutter",
  ];

  const locations = ["New York", "San Francisco", "London", "Berlin", "Tokyo"];
  const experiences = ["Junior", "Mid", "Senior", "Lead"];

  return Array(count)
    .fill(null)
    .map((_, index) => ({
      id: `candidate-${index}`,
      name: `Candidate ${index}`,
      email: `candidate${index}@example.com`,
      phone: `+1234567${index.toString().padStart(4, "0")}`,
      role: `${skills[index % skills.length]} Developer`,
      location: locations[index % locations.length],
      experience: experiences[index % experiences.length],
      tags: [
        "developer",
        skills[index % skills.length].toLowerCase(),
        experiences[index % experiences.length].toLowerCase(),
      ],
      skills: Array(3)
        .fill(null)
        .map((_, skillIndex) => ({
          name: skills[(index + skillIndex) % skills.length],
          level: skillIndex === 0 ? "expert" : "intermediate",
        })),
      relationshipScore: Math.floor(Math.random() * 100),
      status: "active",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }));
}

// Benchmark function
export function benchmarkSearch(
  candidateCount: number = 1000,
  iterations: number = 100,
) {
  console.log(`\n🏃 Running performance benchmark...`);
  console.log(`📊 Dataset size: ${candidateCount} candidates`);
  console.log(`🔄 Iterations: ${iterations}\n`);

  const candidates = generateMockCandidates(candidateCount);

  const testQueries = [
    "TypeScript React",
    "Python Django",
    "JavaScript Node",
    "Senior Developer",
    "New York React",
    "Flutter Mobile",
    "Rust Backend",
    "Lead Engineer Java",
  ];

  const results: { query: string; avgTime: number; resultCount: number }[] = [];

  testQueries.forEach((query) => {
    const times: number[] = [];
    let resultCount = 0;

    // Warm up
    for (let i = 0; i < 5; i++) {
      searchCandidatesOptimized(candidates, query, {});
    }

    // Actual benchmark
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      const searchResults = searchCandidatesOptimized(candidates, query, {});
      const end = performance.now();

      times.push(end - start);
      if (i === 0) resultCount = searchResults.length;
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    results.push({ query, avgTime, resultCount });
  });

  // Display results
  console.log("📈 Benchmark Results:\n");
  console.log(
    "Query".padEnd(25) + "Avg Time (ms)".padEnd(15) + "Results Found",
  );
  console.log("-".repeat(55));

  results.forEach(({ query, avgTime, resultCount }) => {
    console.log(query.padEnd(25) + avgTime.toFixed(3).padEnd(15) + resultCount);
  });

  const overallAvg =
    results.reduce((sum, r) => sum + r.avgTime, 0) / results.length;
  console.log("-".repeat(55));
  console.log("Overall Average:".padEnd(25) + overallAvg.toFixed(3) + " ms\n");

  // Performance assessment
  if (overallAvg < 5) {
    console.log("✅ Excellent performance! Average search time < 5ms");
  } else if (overallAvg < 10) {
    console.log("✅ Good performance! Average search time < 10ms");
  } else if (overallAvg < 20) {
    console.log("⚠️  Acceptable performance. Average search time < 20ms");
  } else {
    console.log(
      "❌ Performance needs optimization. Average search time > 20ms",
    );
  }

  return {
    results,
    overallAverage: overallAvg,
    candidateCount,
    iterations,
  };
}

// Compare with baseline (without skills search)
export function comparePerformance() {
  console.log("\n🔬 Performance Comparison Test\n");

  // Create a version without skills in search text
  function searchWithoutSkills(candidates: CandidateType[], query: string) {
    const pattern = query
      .toLowerCase()
      .split(/\s+/)
      .filter((term) => term.length > 0);
    return candidates.filter((candidate) => {
      const candidateText = [
        candidate.name,
        candidate.role,
        candidate.email,
        candidate.location,
        candidate.experience || "",
        candidate.industry || "",
        ...candidate.tags,
        // Skills NOT included here
      ]
        .join(" ")
        .toLowerCase();

      return pattern.every((term) => candidateText.indexOf(term) !== -1);
    });
  }

  const candidateCount = 1000;
  const iterations = 100;
  const candidates = generateMockCandidates(candidateCount);
  const testQuery = "TypeScript React Senior";

  // Benchmark without skills
  console.log("📊 Testing WITHOUT skills in search...");
  const timesWithout: number[] = [];
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    searchWithoutSkills(candidates, testQuery);
    const end = performance.now();
    timesWithout.push(end - start);
  }
  const avgWithout =
    timesWithout.reduce((a, b) => a + b, 0) / timesWithout.length;

  // Benchmark with skills
  console.log("📊 Testing WITH skills in search...");
  const timesWith: number[] = [];
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    searchCandidatesOptimized(candidates, testQuery, {});
    const end = performance.now();
    timesWith.push(end - start);
  }
  const avgWith = timesWith.reduce((a, b) => a + b, 0) / timesWith.length;

  // Calculate difference
  const difference = avgWith - avgWithout;
  const percentChange = (difference / avgWithout) * 100;

  console.log("\n📈 Results:");
  console.log(`Without skills: ${avgWithout.toFixed(3)}ms`);
  console.log(`With skills:    ${avgWith.toFixed(3)}ms`);
  console.log(
    `Difference:     ${difference.toFixed(3)}ms (${percentChange.toFixed(1)}%)\n`,
  );

  if (percentChange <= 5) {
    console.log("✅ Performance impact is minimal (≤ 5% slowdown)");
  } else if (percentChange <= 10) {
    console.log("⚠️  Moderate performance impact (5-10% slowdown)");
  } else {
    console.log("❌ Significant performance impact (> 10% slowdown)");
  }

  return {
    withoutSkills: avgWithout,
    withSkills: avgWith,
    difference,
    percentChange,
  };
}

// Run benchmarks if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  benchmarkSearch();
  console.log("\n" + "=".repeat(60) + "\n");
  comparePerformance();
}
