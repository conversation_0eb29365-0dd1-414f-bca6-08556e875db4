import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Users, Briefcase, Search, TrendingUp } from "lucide-react";
import { Link } from "react-router-dom";

export default function Index() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">HireLogix</h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Streamline your hiring process with our comprehensive talent
            management platform. Find, track, and hire the best candidates for
            your team.
          </p>
          <div className="space-x-4">
            <Button asChild size="lg">
              <Link to="/auth">Get Started</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link to="/auth">Sign In</Link>
            </Button>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <Card>
            <CardHeader>
              <Users className="h-8 w-8 text-blue-600 mb-2" />
              <CardTitle>Candidate Management</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Organize and track all your candidates in one centralized
                platform
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Briefcase className="h-8 w-8 text-green-600 mb-2" />
              <CardTitle>Job Tracking</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Manage your job openings and track applications seamlessly
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Search className="h-8 w-8 text-purple-600 mb-2" />
              <CardTitle>Smart Search</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Find the perfect candidates using our advanced search
                capabilities
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <TrendingUp className="h-8 w-8 text-orange-600 mb-2" />
              <CardTitle>Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Get insights into your hiring process with detailed analytics
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to transform your hiring process?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Join thousands of companies already using HireLogix
          </p>
          <Button asChild size="lg">
            <Link to="/auth">Start Your Free Trial</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
