import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Mail, Send, Users, Inbox } from "lucide-react";
import { MessageList } from "@/components/message/MessageList";
import { MessageComposer } from "@/components/message/MessageComposer";
import { EmailComposer } from "@/components/communication/EmailComposer";
import { EmailTemplateManager } from "@/components/communication/EmailTemplateManager";
import { ContactManagement } from "@/components/communication/ContactManagement";
import {
  useMessages,
  useUpdateMessage,
  type Message,
} from "@/hooks/useMessages";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { searchMessages } from "@/utils/searchMessages";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useDebounce } from "@/hooks/useDebounce";

const Communications = () => {
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchedMessages, setSearchedMessages] = useState<Message[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [activeTab, setActiveTab] = useState("inbox");
  const { user } = useAuth();
  const { data: templates = [] } = useMessageTemplates();
  const updateMessage = useUpdateMessage();
  
  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Fetch messages with realtime updates
  const fetchMessages = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from("messages")
      .select("*")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const { records: messages = [], isLoading } = useRealtimeCollection(
    "messages",
    fetchMessages,
  );

  // Perform search when query changes
  useEffect(() => {
    const performSearch = async () => {
      if (!debouncedSearchQuery.trim()) {
        setSearchedMessages(messages);
        return;
      }

      setIsSearching(true);
      try {
        const results = await searchMessages(debouncedSearchQuery);
        setSearchedMessages(results);
      } catch (error) {
        console.error("Search error:", error);
        setSearchedMessages([]);
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedSearchQuery, messages]);

  // Use searched messages or all messages
  const displayMessages = searchQuery.trim() ? searchedMessages : messages;

  const handleSelectMessage = (message: Message) => {
    setSelectedMessage(message);

    // Mark message as read if it's unread
    if (message.status === "unread") {
      updateMessage.mutate({
        id: message.id,
        status: "read",
      });
    }
  };

  const handleMessageUpdate = (updatedMessage: Message) => {
    setSelectedMessage(updatedMessage);
  };

  const unreadCount = messages.filter((m) => m.status === "unread").length;
  const templateCount = templates.length;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Communications</h1>
        <div className="text-center p-8">Loading messages...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div className="flex items-center gap-2 min-w-0">
          <MessageSquare className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold truncate">Communications</h1>
            <p className="text-sm sm:text-base text-muted-foreground truncate">
              Manage messages and communication templates
            </p>
          </div>
        </div>
      </div>
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 min-w-0">
            <MessageSquare className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="text-xs sm:text-sm text-muted-foreground truncate">
              {messages.length} total messages
            </span>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="flex-shrink-0">
                {unreadCount} unread
              </Badge>
            )}
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger
            value="inbox"
            className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4"
          >
            <Inbox className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="text-xs sm:text-sm">Inbox</span>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-1 text-xs">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger
            value="compose"
            className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4"
          >
            <Send className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="text-xs sm:text-sm">Compose</span>
          </TabsTrigger>
          <TabsTrigger
            value="templates"
            className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4"
          >
            <Mail className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="text-xs sm:text-sm hidden sm:inline">
              Templates
            </span>
            <span className="text-xs sm:hidden">Temp</span>
            {templateCount > 0 && (
              <Badge variant="secondary" className="ml-1 text-xs">
                {templateCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger
            value="contacts"
            className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4"
          >
            <Users className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="text-xs sm:text-sm hidden sm:inline">
              Contacts
            </span>
            <span className="text-xs sm:hidden">Cont</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="inbox" className="space-y-4 sm:space-y-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search messages..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Mobile: Show either list or conversation, not both */}
          <div className="lg:hidden">
            {!selectedMessage ? (
              <Card className="min-w-0">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">
                    Conversations ({displayMessages.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <MessageList
                    messages={displayMessages.map((msg) => ({
                      id: msg.id,
                      sender: {
                        name: msg.sender_name,
                        avatar: msg.sender_avatar || "/placeholder.svg",
                        role: msg.sender_role || "Unknown",
                      },
                      content: msg.content,
                      time: new Date(msg.created_at).toLocaleString(),
                      status: msg.status as "read" | "unread",
                      isStarred: msg.is_starred,
                      lastActivity: new Date(msg.updated_at).toLocaleDateString(),
                      followUp: msg.follow_up,
                      reminder: msg.reminder,
                    }))}
                    onSelectMessage={(mockMessage) => {
                      const realMessage = displayMessages.find(
                        (m) => m.id === mockMessage.id,
                      );
                      if (realMessage) handleSelectMessage(realMessage);
                    }}
                    selectedMessageId={selectedMessage?.id}
                  />
                </CardContent>
              </Card>
            ) : (
              <Card className="min-w-0">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setSelectedMessage(null)}
                      className="p-1 hover:bg-accent rounded"
                    >
                      ←
                    </button>
                    <CardTitle className="text-lg truncate">
                      {selectedMessage.sender_name}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="flex flex-col h-[calc(100vh-200px)] p-0">
                  <MessageComposer
                    selectedMessage={{
                      id: selectedMessage.id,
                      sender: {
                        name: selectedMessage.sender_name,
                        avatar: selectedMessage.sender_avatar || "/placeholder.svg",
                        role: selectedMessage.sender_role || "Unknown",
                      },
                      content: selectedMessage.content,
                      time: new Date(selectedMessage.created_at).toLocaleString(),
                      status: selectedMessage.status as "read" | "unread",
                      isStarred: selectedMessage.is_starred,
                      lastActivity: new Date(
                        selectedMessage.updated_at,
                      ).toLocaleDateString(),
                      followUp: selectedMessage.follow_up,
                      reminder: selectedMessage.reminder,
                    }}
                    onMessageUpdate={(mockMessage) => {
                      const realMessage = displayMessages.find(
                        (m) => m.id === mockMessage.id,
                      );
                      if (realMessage) handleMessageUpdate(realMessage);
                    }}
                  />
                </CardContent>
              </Card>
            )}
          </div>

          {/* Desktop: Show both side by side */}
          <div className="hidden lg:grid grid-cols-3 gap-6 h-[calc(100vh-200px)]">
            <Card className="col-span-1 min-w-0">
              <CardHeader>
                <CardTitle>Conversations ({displayMessages.length})</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <MessageList
                  messages={displayMessages.map((msg) => ({
                    id: msg.id,
                    sender: {
                      name: msg.sender_name,
                      avatar: msg.sender_avatar || "/placeholder.svg",
                      role: msg.sender_role || "Unknown",
                    },
                    content: msg.content,
                    time: new Date(msg.created_at).toLocaleString(),
                    status: msg.status as "read" | "unread",
                    isStarred: msg.is_starred,
                    lastActivity: new Date(msg.updated_at).toLocaleDateString(),
                    followUp: msg.follow_up,
                    reminder: msg.reminder,
                  }))}
                  onSelectMessage={(mockMessage) => {
                    const realMessage = displayMessages.find(
                      (m) => m.id === mockMessage.id,
                    );
                    if (realMessage) handleSelectMessage(realMessage);
                  }}
                  selectedMessageId={selectedMessage?.id}
                />
              </CardContent>
            </Card>

            <Card className="col-span-2 min-w-0">
              <CardHeader>
                <CardTitle>
                  {selectedMessage ? "Conversation" : "Select a conversation"}
                </CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col h-full p-0">
                <MessageComposer
                  selectedMessage={
                    selectedMessage
                      ? {
                          id: selectedMessage.id,
                          sender: {
                            name: selectedMessage.sender_name,
                            avatar:
                              selectedMessage.sender_avatar || "/placeholder.svg",
                            role: selectedMessage.sender_role || "Unknown",
                          },
                          content: selectedMessage.content,
                          time: new Date(
                            selectedMessage.created_at,
                          ).toLocaleString(),
                          status: selectedMessage.status as "read" | "unread",
                          isStarred: selectedMessage.is_starred,
                          lastActivity: new Date(
                            selectedMessage.updated_at,
                          ).toLocaleDateString(),
                          followUp: selectedMessage.follow_up,
                          reminder: selectedMessage.reminder,
                        }
                      : undefined
                  }
                  onMessageUpdate={(mockMessage) => {
                    const realMessage = displayMessages.find(
                      (m) => m.id === mockMessage.id,
                    );
                    if (realMessage) handleMessageUpdate(realMessage);
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compose" className="space-y-4 sm:space-y-6">
          <EmailComposer />
        </TabsContent>

        <TabsContent value="templates" className="space-y-4 sm:space-y-6">
          <EmailTemplateManager />
        </TabsContent>

        <TabsContent value="contacts" className="space-y-4 sm:space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                <Users className="h-4 w-4 sm:h-5 sm:w-5" />
                Contact Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ContactManagement />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Communications; 