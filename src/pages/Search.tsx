import { useEffect } from "react";
import { SearchInterface } from "@/components/search/SearchInterface";
import { initializeDatabase } from "@/integrations/supabase/client";
import { Search as SearchIcon } from "lucide-react";

const Search = () => {
  useEffect(() => {
    // Initialize database and assign orphaned candidates
    initializeDatabase();
  }, []);

  return (
    <div className="container mx-auto py-4 sm:py-6 px-4 sm:px-6">
      <div className="space-y-4 sm:space-y-6">
        <div className="flex items-center gap-2 min-w-0">
          <SearchIcon className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold truncate">Search</h1>
            <p className="text-sm sm:text-base text-muted-foreground truncate">
              Find candidates, jobs, and content
            </p>
          </div>
        </div>
        <div className="min-w-0">
          <SearchInterface />
        </div>
      </div>
    </div>
  );
};

export default Search;
