import { render, screen, waitFor } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import JobApplicants from "../JobApplicants";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";

// Mock the supabase client
vi.mock("@/integrations/supabase/client", () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock the useAuth hook
vi.mock("@/contexts/AuthContext", () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: vi.fn(),
}));

// Mock date-fns to have consistent dates in tests
vi.mock("date-fns", () => ({
  formatDistanceToNow: vi.fn(() => "2 days ago"),
}));

// Mock the stageUtils
vi.mock("@/utils/stageUtils", () => ({
  getStageColor: vi.fn((stage: string) => {
    const stageColors: Record<string, string> = {
      Applied: "bg-blue-100 text-blue-800",
      Screening: "bg-yellow-100 text-yellow-800",
      Interview: "bg-purple-100 text-purple-800",
      Offer: "bg-green-100 text-green-800",
      Rejected: "bg-red-100 text-red-800",
    };
    return stageColors[stage] || "bg-gray-100 text-gray-800";
  }),
}));

describe("JobApplicants Component", () => {
  const mockUseAuth = useAuth as any;
  let queryClient: QueryClient;

  // Mock user data
  const mockUser = {
    id: "user-123",
    email: "<EMAIL>",
  };

  // Mock applicants data
  const mockApplicants = [
    {
      id: "1",
      user_id: "user-123",
      job_id: "job-456",
      candidate_id: "candidate-1",
      candidate_name: "John Doe",
      role: "Senior Developer",
      stage: "Interview",
      rating: 4,
      last_activity: "2024-01-15T10:00:00Z",
      created_at: "2024-01-10T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z",
      candidate_email: "<EMAIL>",
      candidate_avatar: "https://example.com/avatar1.jpg",
    },
    {
      id: "2",
      user_id: "user-123",
      job_id: "job-456",
      candidate_id: "candidate-2",
      candidate_name: "Jane Smith",
      role: "Full Stack Developer",
      stage: "Screening",
      rating: 3,
      last_activity: "2024-01-14T10:00:00Z",
      created_at: "2024-01-09T10:00:00Z",
      updated_at: "2024-01-14T10:00:00Z",
      candidate_email: "<EMAIL>",
      candidate_avatar: null,
    },
    {
      id: "3",
      user_id: "user-123",
      job_id: "job-789", // Different job ID - should be filtered out
      candidate_id: "candidate-3",
      candidate_name: "Bob Wilson",
      role: "Backend Developer",
      stage: "Applied",
      rating: 5,
      last_activity: "2024-01-13T10:00:00Z",
      created_at: "2024-01-08T10:00:00Z",
      updated_at: "2024-01-13T10:00:00Z",
      candidate_email: "<EMAIL>",
      candidate_avatar: null,
    },
  ];

  // Helper function to render the component with providers
  const renderJobApplicants = (jobId = "job-456") => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <MemoryRouter initialEntries={[`/jobs/${jobId}/applicants`]}>
            <Routes>
              <Route
                path="/jobs/:jobId/applicants"
                element={<JobApplicants />}
              />
            </Routes>
          </MemoryRouter>
        </AuthProvider>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
  });

  describe("Loading State", () => {
    it("should show loading skeleton when data is being fetched", async () => {
      // Mock auth hook
      mockUseAuth.mockReturnValue({
        user: mockUser,
        loading: false,
      });

      // Mock supabase query to return a delayed promise
      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue(
                new Promise((resolve) => {
                  // Never resolve to keep loading state
                  setTimeout(() => resolve({ data: [], error: null }), 10000);
                })
              ),
            }),
          }),
        }),
      });
      (supabase.from as any).mockImplementation(mockFrom);

      renderJobApplicants();

      // Check for loading skeleton
      await waitFor(() => {
        const skeletons = screen.getAllByTestId("skeleton");
        expect(skeletons.length).toBeGreaterThan(0);
      });

      // The loading state should show multiple skeleton rows
      const skeletonRows = screen.getAllByRole("row");
      expect(skeletonRows.length).toBeGreaterThanOrEqual(5);
    });
  });

  describe("Authenticated State with Data", () => {
    it("should display applicants filtered by jobId after loading", async () => {
      // Mock auth hook
      mockUseAuth.mockReturnValue({
        user: mockUser,
        loading: false,
      });

      // Mock supabase query to return filtered applicants
      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue(
                Promise.resolve({
                  data: mockApplicants.filter((a) => a.job_id === "job-456"),
                  error: null,
                })
              ),
            }),
          }),
        }),
      });
      (supabase.from as any).mockImplementation(mockFrom);

      renderJobApplicants();

      // Wait for data to load - loading skeleton should disappear
      await waitFor(() => {
        expect(screen.queryByTestId("skeleton")).not.toBeInTheDocument();
      });

      // Now check for the content
      expect(screen.getByText("Job Applicants")).toBeInTheDocument();
      
      // Check that the correct number of applicants is shown
      expect(screen.getByText("2 applicants")).toBeInTheDocument();

      // Check for table headers
      expect(screen.getByText("Candidate")).toBeInTheDocument();
      expect(screen.getByText("Email")).toBeInTheDocument();
      expect(screen.getByText("Stage")).toBeInTheDocument();
      expect(screen.getByText("Rating")).toBeInTheDocument();
      expect(screen.getByText("Last Activity")).toBeInTheDocument();
      expect(screen.getByText("Actions")).toBeInTheDocument();

      // Check that only applicants for job-456 are displayed
      expect(screen.getByText("John Doe")).toBeInTheDocument();
      expect(screen.getByText("Jane Smith")).toBeInTheDocument();
      expect(screen.queryByText("Bob Wilson")).not.toBeInTheDocument();

      // Check candidate details
      expect(screen.getByText("Senior Developer")).toBeInTheDocument();
      expect(screen.getByText("Full Stack Developer")).toBeInTheDocument();

      // Check emails
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();

      // Check stages
      expect(screen.getByText("Interview")).toBeInTheDocument();
      expect(screen.getByText("Screening")).toBeInTheDocument();

      // Check that View Profile buttons are rendered
      const viewProfileButtons = screen.getAllByText("View Profile");
      expect(viewProfileButtons).toHaveLength(2);
    });

    it("should display correct rating stars", async () => {
      mockUseAuth.mockReturnValue({
        user: mockUser,
        loading: false,
      });

      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue(
                Promise.resolve({
                  data: [mockApplicants[0]], // John Doe with rating 4
                  error: null,
                })
              ),
            }),
          }),
        }),
      });
      (supabase.from as any).mockImplementation(mockFrom);

      renderJobApplicants();

      await waitFor(() => {
        expect(screen.getByText("John Doe")).toBeInTheDocument();
      });

      // Check for star icons - Lucide icons use SVG, not img role
      // Instead, check for the star container
      const starContainer = screen.getByRole("cell", { name: "" });
      const stars = starContainer.querySelectorAll("svg.lucide-star");
      expect(stars.length).toBe(5);
      
      // Check that 4 stars are filled (have the fill class)
      const filledStars = starContainer.querySelectorAll("svg.fill-yellow-400");
      expect(filledStars.length).toBe(4);
    });

    it("should show empty state when no applicants", async () => {
      mockUseAuth.mockReturnValue({
        user: mockUser,
        loading: false,
      });

      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue(
                Promise.resolve({
                  data: [],
                  error: null,
                })
              ),
            }),
          }),
        }),
      });
      (supabase.from as any).mockImplementation(mockFrom);

      renderJobApplicants();

      await waitFor(() => {
        expect(screen.getByText("No applicants yet")).toBeInTheDocument();
      });

      expect(
        screen.getByText(
          "No candidates have applied for this position yet. They will appear here once they apply."
        )
      ).toBeInTheDocument();

      expect(screen.getByText("0 applicants")).toBeInTheDocument();
    });
  });

  describe("Authentication States", () => {
    it("should show loading message when auth is loading", () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: true,
      });

      renderJobApplicants();

      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    it("should show sign in message when user is not authenticated", () => {
      mockUseAuth.mockReturnValue({
        user: null,
        loading: false,
      });

      renderJobApplicants();

      expect(
        screen.getByText("Please sign in to view job applicants")
      ).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("should show error state when query fails", async () => {
      mockUseAuth.mockReturnValue({
        user: mockUser,
        loading: false,
      });

      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue(
                Promise.reject(new Error("Failed to fetch applicants"))
              ),
            }),
          }),
        }),
      });
      (supabase.from as any).mockImplementation(mockFrom);

      renderJobApplicants();

      await waitFor(() => {
        expect(screen.getByText("Error loading applicants")).toBeInTheDocument();
      });

      expect(
        screen.getByText("Please try refreshing the page")
      ).toBeInTheDocument();
    });
  });

  describe("Query Key and Caching", () => {
    it("should use correct query key with jobId", async () => {
      mockUseAuth.mockReturnValue({
        user: mockUser,
        loading: false,
      });

      let capturedQueryKey: any;
      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn((field, value) => {
            if (field === "job_id") {
              capturedQueryKey = value;
            }
            return {
              eq: vi.fn().mockReturnValue({
                order: vi.fn().mockReturnValue(
                  Promise.resolve({
                    data: [],
                    error: null,
                  })
                ),
              }),
            };
          }),
        }),
      });
      (supabase.from as any).mockImplementation(mockFrom);

      renderJobApplicants("job-123");

      await waitFor(() => {
        expect(capturedQueryKey).toBe("job-123");
      });
    });
  });

  describe("Avatar Fallback", () => {
    it("should show initials when avatar image is not available", async () => {
      mockUseAuth.mockReturnValue({
        user: mockUser,
        loading: false,
      });

      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue(
                Promise.resolve({
                  data: [mockApplicants[1]], // Jane Smith without avatar
                  error: null,
                })
              ),
            }),
          }),
        }),
      });
      (supabase.from as any).mockImplementation(mockFrom);

      renderJobApplicants();

      await waitFor(() => {
        expect(screen.getByText("Jane Smith")).toBeInTheDocument();
      });

      // Check for avatar fallback with initials
      expect(screen.getByText("JS")).toBeInTheDocument();
    });
  });
});
