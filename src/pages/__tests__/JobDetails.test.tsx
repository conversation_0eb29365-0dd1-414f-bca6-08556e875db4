import { render, screen, waitFor } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import { vi, describe, it, expect, beforeEach } from "vitest";
import JobDetails from "../JobDetails";
import { useJob } from "@/hooks/useJob";
import { useToast } from "@/hooks/use-toast";

// Mock the hooks
vi.mock("@/hooks/useJob");
vi.mock("@/hooks/use-toast");

// Mock date-fns format to avoid timezone issues in tests
vi.mock("date-fns", () => ({
  format: vi.fn((date) => "January 1st, 2024"),
}));

const mockNavigate = vi.fn();
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe("JobDetails Component", () => {
  const mockToast = vi.fn();
  const mockUseJob = useJob as any;
  const mockUseToast = useToast as any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseToast.mockReturnValue({ toast: mockToast });
  });

  const renderJobDetails = (jobId = "123") => {
    return render(
      <MemoryRouter initialEntries={[`/jobs/${jobId}`]}>
        <Routes>
          <Route path="/jobs/:jobId" element={<JobDetails />} />
        </Routes>
      </MemoryRouter>,
    );
  };

  it("should show loading state", () => {
    mockUseJob.mockReturnValue({
      data: null,
      loading: true,
      error: null,
    });

    renderJobDetails();

    // Check for skeleton loaders
    expect(screen.getByTestId("skeleton")).toBeInTheDocument();
  });

  it("should show error state when there is an error", () => {
    mockUseJob.mockReturnValue({
      data: null,
      loading: false,
      error: "Failed to fetch job",
    });

    renderJobDetails();

    expect(screen.getByText("Error loading job details")).toBeInTheDocument();
  });

  it('should show "Job not found" when job is null', () => {
    mockUseJob.mockReturnValue({
      data: null,
      loading: false,
      error: null,
    });

    renderJobDetails();

    expect(screen.getByText("Job not found")).toBeInTheDocument();
  });

  it("should render job details correctly", async () => {
    const mockJob = {
      id: "123",
      title: "Senior Software Engineer",
      department: "Engineering",
      location: "Remote",
      job_type: "Full-time",
      is_active: true,
      is_urgent: true,
      applicant_count: 15,
      salary_range: "$120k - $180k",
      experience_required: "5+ years",
      description: "We are looking for a senior software engineer...",
      requirements: ["React", "TypeScript", "Node.js"],
      benefits: ["Health Insurance", "Remote Work", "401k"],
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      user_id: "user123",
      search_vector: null,
    };

    mockUseJob.mockReturnValue({
      data: mockJob,
      loading: false,
      error: null,
    });

    renderJobDetails();

    // Check job title
    expect(screen.getByText("Senior Software Engineer")).toBeInTheDocument();

    // Check status badge
    expect(screen.getByText("Active")).toBeInTheDocument();

    // Check urgent badge
    expect(screen.getByText("Urgent")).toBeInTheDocument();

    // Check basic information
    expect(screen.getByText("Remote")).toBeInTheDocument();
    expect(screen.getByText("Engineering")).toBeInTheDocument();
    expect(screen.getByText("Full-time")).toBeInTheDocument();
    expect(screen.getByText("15 candidates")).toBeInTheDocument();

    // Check salary
    expect(screen.getByText("$120k - $180k")).toBeInTheDocument();

    // Check experience
    expect(screen.getByText("5+ years")).toBeInTheDocument();

    // Check description
    expect(
      screen.getByText(/We are looking for a senior software engineer/),
    ).toBeInTheDocument();

    // Check requirements
    expect(screen.getByText("React")).toBeInTheDocument();
    expect(screen.getByText("TypeScript")).toBeInTheDocument();
    expect(screen.getByText("Node.js")).toBeInTheDocument();

    // Check benefits
    expect(screen.getByText("Health Insurance")).toBeInTheDocument();
    expect(screen.getByText("Remote Work")).toBeInTheDocument();
    expect(screen.getByText("401k")).toBeInTheDocument();

    // Check timestamps
    expect(screen.getByText(/Posted on January 1st, 2024/)).toBeInTheDocument();
  });

  it("should navigate back when back button is clicked", () => {
    const mockJob = {
      id: "123",
      title: "Software Engineer",
      department: "Engineering",
      location: "Remote",
      job_type: "Full-time",
      is_active: true,
      is_urgent: false,
      applicant_count: 10,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      user_id: "user123",
      search_vector: null,
    };

    mockUseJob.mockReturnValue({
      data: mockJob,
      loading: false,
      error: null,
    });

    renderJobDetails();

    const backButton = screen.getByRole("button", { name: /back/i });
    backButton.click();

    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it("should navigate to candidates page when View Applicants is clicked", () => {
    const mockJob = {
      id: "123",
      title: "Software Engineer",
      department: "Engineering",
      location: "Remote",
      job_type: "Full-time",
      is_active: true,
      is_urgent: false,
      applicant_count: 10,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      user_id: "user123",
      search_vector: null,
    };

    mockUseJob.mockReturnValue({
      data: mockJob,
      loading: false,
      error: null,
    });

    renderJobDetails();

    const viewApplicantsButton = screen.getByRole("button", {
      name: /view applicants/i,
    });
    viewApplicantsButton.click();

    expect(mockNavigate).toHaveBeenCalledWith("/candidates?jobId=123");
  });

  it("should show inactive status for inactive jobs", () => {
    const mockJob = {
      id: "123",
      title: "Software Engineer",
      department: "Engineering",
      location: "Remote",
      job_type: "Full-time",
      is_active: false,
      is_urgent: false,
      applicant_count: 10,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      user_id: "user123",
      search_vector: null,
    };

    mockUseJob.mockReturnValue({
      data: mockJob,
      loading: false,
      error: null,
    });

    renderJobDetails();

    expect(screen.getByText("Closed")).toBeInTheDocument();
  });
});
