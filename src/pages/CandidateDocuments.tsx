import { useParams, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { CandidateDocumentsManager } from "@/components/candidate/documents/CandidateDocumentsManager";
import { useCandidate } from "@/hooks/useCandidate";
import { LoadingState } from "@/components/candidate/documents/LoadingState";

const CandidateDocuments = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data: candidate, isLoading } = useCandidate(id || "");

  if (isLoading) {
    return <LoadingState />;
  }

  if (!candidate || !id) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Candidate not found</h1>
          <Button onClick={() => navigate("/candidates")}>
            Back to Candidates
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate(`/candidates/${id}`)}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to {candidate.name}
        </Button>
      </div>

      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Document Management</h1>
        <p className="text-muted-foreground">
          Manage documents for {candidate.name}
        </p>
      </div>

      <CandidateDocumentsManager candidateId={id} />
    </div>
  );
};

export default CandidateDocuments;
