import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useJob } from "@/hooks/useJob";
import { useJobApplicantCount } from "@/hooks/useJobApplicantCounts";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { JobFormDialog } from "@/components/job/JobFormDialog";
import {
  ArrowLeft,
  MapPin,
  Briefcase,
  DollarSign,
  Clock,
  Users,
  AlertCircle,
} from "lucide-react";
import { format } from "date-fns";

const JobDetails = () => {
  const { jobId } = useParams();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { data: job, isLoading: loading, error } = useJob(jobId || "");
  const { data: applicantCount = 0 } = useJobApplicantCount(jobId);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Skeleton className="h-6 w-48" data-testid="skeleton" />
            <Skeleton className="h-4 w-32" />
          </div>
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-48 w-full" />
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">
          Error loading job details
        </p>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Job not found</p>
      </div>
    );
  }

  const jobStatus = job.is_active ? "Active" : "Closed";

  const handleEditSuccess = () => {
    setIsEditDialogOpen(false);
    toast({
      title: "Job Updated",
      description: "The job has been updated successfully.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">{job.title}</h1>
        </div>
        <Badge variant={job.is_active ? "default" : "secondary"}>
          {jobStatus}
        </Badge>
      </div>

      {/* Main job details card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Job Details
            </CardTitle>
            {job.is_urgent && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                Urgent
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span className="font-medium">Location</span>
              </div>
              <p>{job.location_name}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Briefcase className="h-4 w-4" />
                <span className="font-medium">Department</span>
              </div>
              <p>{job.department_name}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span className="font-medium">Job Type</span>
              </div>
              <p>{job.job_type_name}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Users className="h-4 w-4" />
                <span className="font-medium">Applicants</span>
              </div>
              <p>{applicantCount} candidates</p>
            </div>
          </div>

          {/* Salary Information */}
          {job.salary_range && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-muted-foreground">
                <DollarSign className="h-4 w-4" />
                <span className="font-medium">Salary Range</span>
              </div>
              <p className="text-lg">{job.salary_range}</p>
            </div>
          )}

          {/* Experience Required */}
          {job.experience_required && (
            <div className="space-y-2">
              <h3 className="font-medium">Experience Required</h3>
              <p>{job.experience_required}</p>
            </div>
          )}

          {/* Job Description */}
          <div className="space-y-2">
            <h3 className="font-medium">Description</h3>
            <p className="text-muted-foreground whitespace-pre-wrap">
              {job.description || "No description provided."}
            </p>
          </div>

          {/* Requirements */}
          {job.normalized_requirements && job.normalized_requirements.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Requirements</h3>
              <ul className="list-disc list-inside space-y-1">
                {job.normalized_requirements.map((req, index) => (
                  <li key={index} className="text-muted-foreground">
                    {req.name}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Benefits */}
          {job.normalized_benefits && job.normalized_benefits.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Benefits</h3>
              <div className="flex flex-wrap gap-2">
                {job.normalized_benefits.map((benefit, index) => (
                  <Badge key={index} variant="secondary">
                    {benefit.name}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="pt-4 border-t text-sm text-muted-foreground">
            <p>Posted on {format(new Date(job.created_at), "PPP")}</p>
            {job.updated_at !== job.created_at && (
              <p>Last updated {format(new Date(job.updated_at), "PPP")}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Action buttons */}
      <div className="flex gap-3">
        <Button onClick={() => navigate(`/jobs/${job.id}/applicants`)}>
          View Applicants
        </Button>
        <Button
          variant="outline"
          onClick={() => setIsEditDialogOpen(true)}
        >
          Edit Job
        </Button>
      </div>

      {/* Edit Dialog */}
      <JobFormDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSuccess={handleEditSuccess}
        mode="edit"
        job={job}
      />
    </div>
  );
};

export default JobDetails;
