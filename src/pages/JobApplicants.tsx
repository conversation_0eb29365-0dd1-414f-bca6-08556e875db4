import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Star, Mail, Calendar, User, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { formatDistanceToNow } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getStageColor } from "@/utils/stageUtils";

// Define the type for pipeline candidate with details
interface PipelineCandidate {
  id: string;
  user_id: string;
  job_id: string;
  candidate_id: string;
  candidate_name: string;
  role: string;
  stage: string;
  rating: number;
  last_activity: string;
  created_at: string;
  updated_at: string;
  candidate_email?: string;
  candidate_avatar?: string;
}

// Custom hook to fetch job applicants
const useJobApplicants = (jobId: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["job-applicants", jobId],
    queryFn: async () => {
      if (!user?.id || !jobId) {
        return [];
      }

      const { data, error } = await supabase
        .from("pipeline_candidates_with_details")
        .select("*")
        .eq("job_id", jobId)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching job applicants:", error);
        throw error;
      }

      return data as PipelineCandidate[];
    },
    enabled: !!user?.id && !!jobId,
  });
};

const JobApplicants = () => {
  const { jobId } = useParams();
  const { user, loading: authLoading } = useAuth();
  const { data: applicants = [], isLoading, error } = useJobApplicants(jobId || "");

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">Loading...</p>
      </div>
    );
  }

  // Show authentication required message
  if (!user) {
    return (
      <div className="flex items-center justify-center h-96">
        <p className="text-lg text-muted-foreground">
          Please sign in to view job applicants
        </p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-lg text-destructive">Error loading applicants</p>
          <p className="text-sm text-muted-foreground mt-2">
            Please try refreshing the page
          </p>
        </div>
      </div>
    );
  }

  // Render stars for rating
  const renderStars = (rating: number) => {
    return (
      <div className="flex gap-0.5">
        {Array.from({ length: 5 }).map((_, index) => (
          <Star
            key={index}
            className={`w-4 h-4 ${
              index < rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };


  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/jobs">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Job Applicants</h1>
            <p className="text-muted-foreground">
              {applicants.length} applicant{applicants.length !== 1 ? "s" : ""}
            </p>
          </div>
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" data-testid="skeleton" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center gap-4" role="row">
                  <Skeleton className="h-12 w-12 rounded-full" data-testid="skeleton" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-48" data-testid="skeleton" />
                    <Skeleton className="h-3 w-32" data-testid="skeleton" />
                  </div>
                  <Skeleton className="h-6 w-20" data-testid="skeleton" />
                  <Skeleton className="h-4 w-24" data-testid="skeleton" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty state */}
      {!isLoading && applicants.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <User className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No applicants yet</h3>
            <p className="text-muted-foreground text-center max-w-sm">
              No candidates have applied for this position yet. They will appear here once they apply.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Applicants table */}
      {!isLoading && applicants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>All Applicants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Candidate</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Stage</TableHead>
                    <TableHead>Rating</TableHead>
                    <TableHead>Last Activity</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {applicants.map((applicant) => (
                    <TableRow key={applicant.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage
                              src={applicant.candidate_avatar}
                              alt={applicant.candidate_name}
                            />
                            <AvatarFallback>
                              {applicant.candidate_name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">
                              {applicant.candidate_name}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {applicant.role}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Mail className="h-3 w-3" />
                          {applicant.candidate_email || "No email"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="secondary"
                          className={getStageColor(applicant.stage)}
                        >
                          {applicant.stage}
                        </Badge>
                      </TableCell>
                      <TableCell>{renderStars(applicant.rating)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          {formatDistanceToNow(
                            new Date(applicant.last_activity),
                            {
                              addSuffix: true,
                            }
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Link to={`/candidates/${applicant.candidate_id}`}>
                          <Button variant="outline" size="sm">
                            View Profile
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default JobApplicants;
