import { AnalyticsDashboard } from "@/components/analytics/AnalyticsDashboard";
import { Button } from "@/components/ui/button";
import { RefreshCw, Loader2, BarChart3 } from "lucide-react";
import { useGenerateAnalyticsData } from "@/hooks/useAnalyticsDataGeneration";

const Analytics = () => {
  const generateAnalytics = useGenerateAnalyticsData();

  const handleRefreshAnalytics = async () => {
    try {
      await generateAnalytics.mutateAsync();
    } catch (error) {
      console.error("Failed to refresh analytics:", error);
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div className="flex items-center gap-2 min-w-0">
          <BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold truncate">Analytics</h1>
            <p className="text-sm sm:text-base text-muted-foreground truncate">
              Track performance metrics and insights
            </p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
          <Button
            variant="outline"
            onClick={handleRefreshAnalytics}
            disabled={generateAnalytics.isPending}
            className="w-full sm:w-auto"
          >
            {generateAnalytics.isPending ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            {generateAnalytics.isPending ? "Refreshing..." : "Refresh Analytics"}
          </Button>
        </div>
      </div>
      <AnalyticsDashboard />
    </div>
  );
};

export default Analytics;
