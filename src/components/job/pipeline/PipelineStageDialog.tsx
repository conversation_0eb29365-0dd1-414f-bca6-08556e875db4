import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock } from "lucide-react";

interface PipelineStageDialogProps {
  stage: {
    name: string;
    count: number;
    color: string;
  };
  onClose: () => void;
}

export function PipelineStageDialog({
  stage,
  onClose,
}: PipelineStageDialogProps) {
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {stage.name} Stage
            <Badge
              className="ml-2"
              style={{ backgroundColor: stage.color, color: "white" }}
            >
              {stage.count} Candidates
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Detailed view of candidates in the {stage.name.toLowerCase()} stage
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[400px] pr-4">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="text-4xl mb-4">👥</div>
            <p className="text-muted-foreground">
              No candidates in this stage yet. Candidates will appear here as
              they progress through your hiring pipeline.
            </p>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
