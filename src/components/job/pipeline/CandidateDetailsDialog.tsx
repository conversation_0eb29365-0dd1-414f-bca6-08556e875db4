import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Mail, Phone, MapPin, Calendar, Star } from "lucide-react";

import { useEffect, useState } from "react";
import { useCandidate } from "@/hooks/useCandidate";
import { getStageColor } from "@/utils/stageUtils";

interface CandidateDetailsDialogProps {
  candidate: {
    id: string;
    name: string;
    role: string;
    stage: string;
    rating: number;
    lastActivity: string;
  };
  onClose: () => void;
}

export function CandidateDetailsDialog({
  candidate,
  onClose,
}: CandidateDetailsDialogProps) {
  const navigate = useNavigate();

  // Fetch full candidate details using the hook
  const { data: fullCandidate, isLoading, error } = useCandidate(candidate.id);

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Candidate Profile</DialogTitle>
          <DialogDescription>
            {isLoading
              ? "Loading candidate details..."
              : `Detailed information about ${candidate.name}`}
          </DialogDescription>
        </DialogHeader>
        {!isLoading && fullCandidate && (
          <ScrollArea className="h-[500px] pr-4">
            <div className="space-y-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-16 w-16">
                    <AvatarImage
                      src={fullCandidate.avatar || "/placeholder.svg"}
                    />
                    <AvatarFallback>
                      {fullCandidate.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-lg font-semibold">
                      {fullCandidate.name}
                    </h3>
                    <p className="text-muted-foreground">
                      {fullCandidate.role}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className={getStageColor(candidate.stage)}>
                        {candidate.stage}
                      </Badge>
                      <div className="flex items-center">
                        {Array.from({ length: 5 }).map((_, index) => (
                          <Star
                            key={index}
                            className={`w-3 h-3 ${
                              index < candidate.rating
                                ? "fill-yellow-400 text-yellow-400"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                        <span className="ml-1 text-sm">{candidate.rating}/5</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-x-2">
                  <Button variant="outline" size="sm">
                    <Mail className="w-4 h-4 mr-2" />
                    Contact
                  </Button>
                  <Button size="sm">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      navigate(`/candidates/${candidate.id}`);
                      onClose();
                    }}
                  >
                    View Profile
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{fullCandidate.email}</p>
                  </div>
                  {fullCandidate.phone && (
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <p className="font-medium">{fullCandidate.phone}</p>
                    </div>
                  )}
                  {fullCandidate.location && (
                    <div>
                      <p className="text-sm text-muted-foreground">Location</p>
                      <p className="font-medium">{fullCandidate.location}</p>
                    </div>
                  )}
                  {fullCandidate.experience && (
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Experience
                      </p>
                      <p className="font-medium">{fullCandidate.experience}</p>
                    </div>
                  )}
                </div>

                {(fullCandidate.linkedin_url || fullCandidate.github_url) && (
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Social Links
                    </p>
                    <div className="flex gap-2">
                      {fullCandidate.linkedin_url && (
                        <Button variant="outline" size="sm" asChild>
                          <a
                            href={fullCandidate.linkedin_url}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            LinkedIn
                          </a>
                        </Button>
                      )}
                      {fullCandidate.github_url && (
                        <Button variant="outline" size="sm" asChild>
                          <a
                            href={fullCandidate.github_url}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            GitHub
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>
                )}

                {fullCandidate.skills && fullCandidate.skills.length > 0 && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">Skills</p>
                    <div className="flex flex-wrap gap-2">
                      {fullCandidate.skills.map((skill: any, index: number) => (
                        <Badge key={index} variant="secondary">
                          {typeof skill === "string" ? skill : skill.name}
                          {skill.level && (
                            <span className="ml-1 text-xs opacity-70">
                              ({skill.level})
                            </span>
                          )}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>
        )}
      </DialogContent>
    </Dialog>
  );
}
