import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Star, ChevronRight, GripVertical } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUpdatePipelineStage } from "@/hooks/useUpdatePipelineStage";
import { CandidateDetailsDialog } from "./CandidateDetailsDialog";
import { getStageColor } from "@/utils/stageUtils";
import { format } from "date-fns";

interface PipelineCandidate {
  id: string;
  candidate_id: string;
  candidate_name: string;
  role: string;
  stage: string;
  rating: number;
  last_activity: string;
}

interface PipelineStage {
  id: string;
  name: string;
  count: number;
  stage_order: number;
}

interface PipelineKanbanViewProps {
  stages: PipelineStage[];
  candidates: PipelineCandidate[];
  isLoading?: boolean;
  onOptimisticUpdate?: (candidates: PipelineCandidate[]) => void;
}

// Helper function to safely format dates
const formatDateSafely = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return "Invalid date";
    }
    return format(date, "MMMM d, yyyy");
  } catch (error) {
    console.warn("Error formatting date:", dateString, error);
    return "Invalid date";
  }
};

export function PipelineKanbanView({
  stages,
  candidates: initialCandidates,
  isLoading,
  onOptimisticUpdate,
}: PipelineKanbanViewProps) {
  const [selectedCandidate, setSelectedCandidate] = useState<any>(null);
  const [draggedCandidate, setDraggedCandidate] =
    useState<PipelineCandidate | null>(null);
  const [dragOverStage, setDragOverStage] = useState<string | null>(null);
  const [localCandidates, setLocalCandidates] =
    useState<PipelineCandidate[]>(initialCandidates);
  const updateStage = useUpdatePipelineStage();

  // Update local state when candidates prop changes, but preserve any optimistic updates
  useEffect(() => {
    // Only update if we're not in the middle of a drag operation
    if (!draggedCandidate) {
      setLocalCandidates(initialCandidates);
    }
  }, [initialCandidates, draggedCandidate]);

  // Group candidates by stage
  const candidatesByStage = localCandidates.reduce(
    (acc, candidate) => {
      if (!acc[candidate.stage]) {
        acc[candidate.stage] = [];
      }
      acc[candidate.stage].push(candidate);
      return acc;
    },
    {} as Record<string, PipelineCandidate[]>,
  );

  const handleDragStart = (
    e: React.DragEvent,
    candidate: PipelineCandidate,
  ) => {
    setDraggedCandidate(candidate);
    e.dataTransfer.effectAllowed = "move";
    // Use the card itself as the drag image for a better preview
    if (e.currentTarget) {
      try {
        e.dataTransfer.setDragImage(e.currentTarget as Element, 16, 16);
      } catch (_) {}
    }
  };

  const handleDragOver = (e: React.DragEvent, stageName: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOverStage(stageName);
  };

  const handleDragLeave = () => {
    setDragOverStage(null);
  };

  const handleDrop = (e: React.DragEvent, toStage: string) => {
    e.preventDefault();
    setDragOverStage(null);

    if (draggedCandidate && draggedCandidate.stage !== toStage) {
      console.log("Dragging candidate:", draggedCandidate);
      console.log("From stage:", draggedCandidate.stage, "To stage:", toStage);

      // Optimistically update local state and bubble up
      setLocalCandidates((prev) => {
        const next = prev.map((c) =>
          c.id === draggedCandidate.id ? { ...c, stage: toStage } : c,
        );
        onOptimisticUpdate?.(next);
        return next;
      });

      updateStage.mutate({
        candidateId: draggedCandidate.id,
        candidateName: draggedCandidate.candidate_name,
        fromStage: draggedCandidate.stage,
        toStage: toStage,
      });
    }
    setDraggedCandidate(null);
  };

  const handleStageChange = (
    candidateId: string,
    candidateName: string,
    fromStage: string,
    toStage: string,
  ) => {
    if (fromStage !== toStage) {
      console.log(
        "Stage change for:",
        candidateId,
        candidateName,
        "from",
        fromStage,
        "to",
        toStage,
      );

      // Optimistically update local state and bubble up
      setLocalCandidates((prev) => {
        const next = prev.map((c) =>
          c.id === candidateId ? { ...c, stage: toStage } : c,
        );
        onOptimisticUpdate?.(next);
        return next;
      });

      updateStage.mutate({
        candidateId: candidateId,
        candidateName: candidateName,
        fromStage,
        toStage,
      });
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        className={`w-3 h-3 ${
          index < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  if (isLoading) {
    return (
      <div className="w-full max-w-[calc(100vw-3.5rem-2rem)] sm:max-w-[calc(100vw-3.5rem-3rem)] lg:max-w-[calc(100vw-3.5rem-4rem)] overflow-x-auto">
        <div className="flex gap-4 pb-4 w-max">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="min-w-[300px] animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-24" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <div key={j} className="h-24 bg-gray-100 rounded" />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full max-w-[calc(100vw-3.5rem-2rem)] sm:max-w-[calc(100vw-3.5rem-3rem)] lg:max-w-[calc(100vw-3.5rem-4rem)] overflow-x-auto">
        <div className="flex gap-4 pb-4 w-max">
          {stages.map((stage) => (
            <Card
              key={stage.id}
              className={`min-w-[300px] ${
                dragOverStage === stage.name ? "ring-2 ring-primary" : ""
              }`}
              onDragOver={(e) => handleDragOver(e, stage.name)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, stage.name)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">
                    {stage.name}
                  </CardTitle>
                  <Badge
                    variant="secondary"
                    className={getStageColor(stage.name)}
                  >
                    {candidatesByStage[stage.name]?.length || 0}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px] pr-2">
                  <div className="space-y-3">
                    {candidatesByStage[stage.name]?.map((candidate) => (
                    <Card
                        key={candidate.id}
                      className={`cursor-move hover:shadow-md transition-all duration-150 select-none ${
                        draggedCandidate?.id === candidate.id
                          ? "opacity-60 scale-[0.98]"
                          : ""
                      }`}
                        draggable
                        onDragStart={(e) => handleDragStart(e, candidate)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <h4
                                className="font-medium text-sm cursor-pointer hover:text-primary"
                                onClick={() =>
                                  setSelectedCandidate({
                                    id: candidate.candidate_id,
                                    name: candidate.candidate_name,
                                    role: candidate.role,
                                    stage: candidate.stage,
                                    rating: candidate.rating,
                                    lastActivity: candidate.last_activity,
                                  })
                                }
                              >
                                {candidate.candidate_name}
                              </h4>
                              <p className="text-xs text-muted-foreground">
                                {candidate.role}
                              </p>
                            </div>
                            <GripVertical className="w-4 h-4 text-muted-foreground" />
                          </div>

                          <div className="flex items-center gap-1 mb-2">
                            {renderStars(candidate.rating)}
                          </div>

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-muted-foreground">
                              {formatDateSafely(candidate.last_activity)}
                            </span>

                            <Select
                              value={candidate.stage}
                              onValueChange={(value) =>
                                handleStageChange(
                                  candidate.id,
                                  candidate.candidate_name,
                                  candidate.stage,
                                  value,
                                )
                              }
                            >
                              <SelectTrigger className="w-[130px] h-7 text-xs">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {stages.map((s) => (
                                  <SelectItem key={s.id} value={s.name}>
                                    <div className="flex items-center gap-2 whitespace-nowrap">
                                      <div
                                        className={`w-2 h-2 rounded-full flex-shrink-0 ${getStageColor(s.name).split(' ')[0]}`}
                                      />
                                      <span className="text-xs">{s.name}</span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </CardContent>
                      </Card>
                    ))}

                    {(!candidatesByStage[stage.name] ||
                      candidatesByStage[stage.name].length === 0) && (
                      <div className="text-center py-8 text-muted-foreground text-sm">
                        No candidates in this stage
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {selectedCandidate && (
        <CandidateDetailsDialog
          candidate={selectedCandidate}
          onClose={() => setSelectedCandidate(null)}
        />
      )}
    </>
  );
}
