import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Check, ChevronDown, Plus } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useLocations } from "@/hooks/useLocations";
import { useCreateLocation } from "@/hooks/useCreateLocation";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface LocationPickerProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function LocationPicker({
  value,
  onValueChange,
  placeholder = "Select location...",
  className,
  disabled = false,
}: LocationPickerProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const commandListRef = useRef<HTMLDivElement>(null);

  const { data: locations = [], isLoading } = useLocations();
  const createLocation = useCreateLocation();
  const { toast } = useToast();

  const selectedLocation = locations.find((loc) => loc.name === value);

  const handleSelect = (locationName: string) => {
    onValueChange(locationName);
    setOpen(false);
  };

  const handleCreateLocation = async (locationName: string) => {
    try {
      const newLocation = await createLocation.mutateAsync({
        name: locationName.trim(),
      });
      onValueChange(newLocation.name);
      setSearchValue("");
      setOpen(false);
      toast({
        title: "Location Created",
        description: `"${newLocation.name}" has been created successfully.`,
      });
    } catch (error) {
      console.error("Error creating location:", error);
      toast({
        title: "Error",
        description: "Failed to create location. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredLocations = locations.filter((loc) =>
    loc.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (commandListRef.current) {
      commandListRef.current.scrollTop += e.deltaY;
    }
  };

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled || isLoading}
          >
            {selectedLocation ? selectedLocation.name : placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search locations..."
              value={searchValue}
              onValueChange={setSearchValue}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && searchValue && !filteredLocations.some(
                  (loc) => loc.name.toLowerCase() === searchValue.toLowerCase()
                )) {
                  e.preventDefault();
                  handleCreateLocation(searchValue);
                }
              }}
            />
            <CommandList 
              ref={commandListRef}
              className="max-h-[280px] overflow-y-auto overflow-x-hidden px-2 pb-2"
              onWheel={handleWheel}
            >
              <CommandEmpty>
                <div className="flex flex-col gap-2 p-2">
                  <p className="text-sm text-muted-foreground">No locations found.</p>
                  {searchValue && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2 h-auto p-0 text-sm"
                      onClick={() => handleCreateLocation(searchValue)}
                      disabled={createLocation.isPending}
                    >
                      <Plus className="mr-1 h-3 w-3" />
                      {createLocation.isPending ? "Creating..." : `Create "${searchValue}"`}
                    </Button>
                  )}
                </div>
              </CommandEmpty>
              <CommandGroup>
                {filteredLocations.map((location) => (
                  <CommandItem
                    key={location.id}
                    value={location.name}
                    onSelect={() => handleSelect(location.name)}
                  >
                    <span>{location.name}</span>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        value === location.name ? "opacity-100" : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))}
                {searchValue &&
                  !filteredLocations.some(
                    (loc) => loc.name.toLowerCase() === searchValue.toLowerCase(),
                  ) && (
                    <CommandItem
                      value={searchValue}
                      onSelect={() => handleCreateLocation(searchValue)}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create "{searchValue}"
                    </CommandItem>
                  )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
} 