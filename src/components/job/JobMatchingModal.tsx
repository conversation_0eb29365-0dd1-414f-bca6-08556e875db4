import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import {
  Briefcase,
  MapPin,
  Star,
  Mail,
  Sparkles,
  Loader2,
  TrendingUp,
} from "lucide-react";
import { useState } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";

interface JobMatch {
  id: string;
  name: string;
  match: number;
  role?: string;
  location?: string;
  experience?: string;
  skills?: string[];
  avatar?: string;
  email?: string;
}

interface JobMatchingModalProps {
  isOpen: boolean;
  onClose: () => void;
  matches: JobMatch[];
  jobTitle: string;
  jobDetails?: {
    description?: string;
    requirements?: string[];
    location?: string;
    department?: string;
  };
}

export function JobMatchingModal({
  isOpen,
  onClose,
  matches,
  jobTitle,
  jobDetails,
}: JobMatchingModalProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [aiInsights, setAiInsights] = useState<string>("");
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false);

  const generateAIInsights = async () => {
    if (isGeneratingInsights || !matches.length) return;

    setIsGeneratingInsights(true);
    try {
      const candidateData = matches
        .map(
          (candidate) =>
            `${candidate.name}: ${candidate.match}% match, Role: ${candidate.role || "Not specified"}, Location: ${candidate.location || "Not specified"}, Skills: ${candidate.skills?.join(", ") || "Not specified"}`,
        )
        .join("\n");

      const prompt = `
Analyze the following job matching data and provide strategic insights:

Job Details:
- Title: ${jobTitle}
- Description: ${jobDetails?.description || "Not provided"}
- Requirements: ${jobDetails?.requirements?.join(", ") || "Not specified"}
- Location: ${jobDetails?.location || "Not specified"}
- Department: ${jobDetails?.department || "Not specified"}

Candidate Matches:
${candidateData}

Provide analysis including:
1. Overall candidate pool assessment
2. Top recommendations and why
3. Potential concerns or gaps
4. Interview strategy suggestions
5. Hiring timeline recommendations

Keep the response actionable and focused on recruitment decision-making.
`;

      const systemPrompt =
        "You are an expert recruitment consultant specializing in candidate-job matching analysis. Provide strategic insights that help hiring managers make informed decisions.";

      const insights = await generateText(prompt, systemPrompt);
      setAiInsights(insights);

      toast({
        title: "AI Insights Generated",
        description: "Job matching analysis and recommendations are ready.",
      });
    } catch (error) {
      console.error("Error generating AI insights:", error);
      toast({
        title: "Insights Unavailable",
        description: "Unable to generate AI insights at the moment.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingInsights(false);
    }
  };

  const getMatchColor = (match: number) => {
    if (match >= 80) return "text-green-600";
    if (match >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getMatchBadgeColor = (match: number) => {
    if (match >= 80) return "bg-green-100 text-green-800";
    if (match >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const handleViewProfile = (candidateId: string) => {
    navigate(`/candidates/${candidateId}`);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>Matching Candidates</DialogTitle>
              <DialogDescription>
                Found {matches.length} potential candidates for {jobTitle}
              </DialogDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={generateAIInsights}
              disabled={isGeneratingInsights}
            >
              {isGeneratingInsights ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  AI Insights
                </>
              )}
            </Button>
          </div>
        </DialogHeader>
        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-4">
            {matches.map((candidate) => (
              <div
                key={candidate.id}
                className="flex items-start justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={candidate.avatar} alt={candidate.name} />
                    <AvatarFallback>
                      {candidate.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <div>
                      <h4 className="font-medium">{candidate.name}</h4>
                      {candidate.role && (
                        <p className="text-sm text-muted-foreground">
                          {candidate.role}
                        </p>
                      )}
                    </div>
                    <div className="flex gap-4 text-sm text-muted-foreground">
                      {candidate.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          {candidate.location}
                        </div>
                      )}
                      {candidate.experience && (
                        <div className="flex items-center gap-1">
                          <Briefcase className="w-4 h-4" />
                          {candidate.experience}
                        </div>
                      )}
                    </div>
                    {candidate.skills && (
                      <div className="flex flex-wrap gap-1">
                        {candidate.skills.map((skill) => (
                          <Badge
                            key={skill}
                            variant="secondary"
                            className="text-xs"
                          >
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={getMatchBadgeColor(candidate.match)}>
                      {candidate.match}% Match
                    </Badge>
                    {candidate.match >= 80 && (
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    )}
                  </div>
                  <div className="flex gap-2">
                    {candidate.email && (
                      <Button variant="outline" size="sm" className="h-8">
                        <Mail className="w-4 h-4 mr-1" />
                        Contact
                      </Button>
                    )}
                    <Button
                      size="sm"
                      className="h-8"
                      onClick={() => handleViewProfile(candidate.id)}
                    >
                      View Profile
                    </Button>
                  </div>
                </div>
              </div>
            ))}

            {aiInsights && (
              <div className="mt-6 pt-4 border-t">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Sparkles className="h-4 w-4 text-blue-600" />
                    <h3 className="font-medium text-blue-900">
                      AI Matching Insights
                    </h3>
                  </div>
                  <div className="text-sm text-gray-700 whitespace-pre-wrap">
                    {aiInsights}
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
