"use client";

import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, ChevronDown, Plus, X } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

export interface Requirement {
  id: string;
  name: string;
  category?: string;
}

interface RequirementsPickerProps {
  requirements: Requirement[];
  selectedRequirements: Requirement[];
  onRequirementsChange: (requirements: Requirement[]) => void;
  onCreateRequirement: (name: string) => Promise<Requirement>;
  placeholder?: string;
}

export function RequirementsPicker({
  requirements,
  selectedRequirements,
  onRequirementsChange,
  onCreateRequirement,
  placeholder = "Select requirements...",
}: RequirementsPickerProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const commandListRef = useRef<HTMLDivElement>(null);

  const filteredRequirements = requirements.filter((requirement) =>
    requirement.name.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const handleSelect = (requirement: Requirement) => {
    const isSelected = selectedRequirements.some((r) => r.id === requirement.id);
    if (isSelected) {
      onRequirementsChange(selectedRequirements.filter((r) => r.id !== requirement.id));
    } else {
      onRequirementsChange([...selectedRequirements, requirement]);
    }
  };

  const handleCreate = async () => {
    if (searchValue.trim()) {
      setIsCreating(true);
      try {
        const newRequirement = await onCreateRequirement(searchValue.trim());
        onRequirementsChange([...selectedRequirements, newRequirement]);
        setSearchValue("");
        setOpen(false);
      } catch (error) {
        console.error("Failed to create requirement:", error);
      } finally {
        setIsCreating(false);
      }
    }
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (commandListRef.current) {
      commandListRef.current.scrollTop += e.deltaY;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-1">
        {selectedRequirements.map((requirement) => (
          <Badge
            key={requirement.id}
            variant="secondary"
            className="flex items-center gap-1"
          >
            {requirement.name}
            <button
              type="button"
              onClick={() => onRequirementsChange(selectedRequirements.filter((r) => r.id !== requirement.id))}
              className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            <span className="text-muted-foreground">{placeholder}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search requirements..."
              value={searchValue}
              onValueChange={setSearchValue}
            />
            <CommandList 
              ref={commandListRef}
              className="max-h-[280px] overflow-y-auto overflow-x-hidden px-2 pb-2"
              onWheel={handleWheel}
            >
              <CommandEmpty>
                <div className="flex items-center justify-between p-2">
                  <span>No requirements found.</span>
                  {searchValue.trim() && (
                    <Button
                      size="sm"
                      onClick={handleCreate}
                      disabled={isCreating}
                    >
                      {isCreating ? (
                        "Creating..."
                      ) : (
                        <>
                          <Plus className="mr-1 h-3 w-3" />
                          Create "{searchValue}"
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CommandEmpty>
              <CommandGroup>
                {filteredRequirements.map((requirement) => (
                  <CommandItem
                    key={requirement.id}
                    value={requirement.name}
                    onSelect={() => handleSelect(requirement)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedRequirements.some((r) => r.id === requirement.id)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {requirement.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
} 