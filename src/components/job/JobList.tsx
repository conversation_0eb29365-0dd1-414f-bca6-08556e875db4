import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { JobMatchingModal } from "./JobMatchingModal";
import { JobFormDialog } from "./JobFormDialog";
import { useJobs, Job } from "@/hooks/useJobs";
import { useJobApplicantCounts } from "@/hooks/useJobApplicantCounts";
import { useUpdateJob } from "@/hooks/useUpdateJob";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import {
  Briefcase,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Share2,
  MoreVertical,
  Users,
  Loader2,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { JobFilters as JobFiltersType } from "@/types/jobFilters";

interface JobMatch {
  id: string;
  name: string;
  match: number;
  role?: string;
  location?: string;
  experience?: string;
  skills?: string[];
  avatar?: string;
  email?: string;
}

interface JobListProps {
  filters?: JobFiltersType;
}

export const JobList = ({ filters }: JobListProps) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [matchingCandidates, setMatchingCandidates] = useState<JobMatch[]>([]);
  const [isMatching, setIsMatching] = useState(false);
  const [selectedJob, setSelectedJob] = useState<{
    id: string;
    title: string;
  } | null>(null);
  const [editingJob, setEditingJob] = useState<Job | null>(null);

  const updateJobMutation = useUpdateJob();
  const { toast } = useToast();

  // Use unified jobs hook for real-time updates
  const { data: allJobs, isLoading: loading } = useJobs();
  
  // Get real applicant counts for all jobs
  const jobIds = useMemo(() => allJobs?.map(job => job.id) || [], [allJobs]);
  const { data: applicantCounts } = useJobApplicantCounts(jobIds);

  // Filter jobs based on the provided filters
  const jobs = useMemo(() => {
    if (!allJobs || !filters) return allJobs || [];

    return allJobs.filter((job) => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const searchableText = [
          job.title,
          job.department,
          job.location,
          job.description,
          ...(job.requirements || []),
        ]
          .join(" ")
          .toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Job type filter
      if (
        filters.jobType &&
        filters.jobType !== "all" &&
        job.job_type !== filters.jobType
      ) {
        return false;
      }

      // Experience level filter (match against experience_required field)
      if (
        filters.experienceLevel &&
        filters.experienceLevel !== "all" &&
        job.experience_required
      ) {
        const experienceMap: Record<string, string[]> = {
          entry: ["entry", "junior", "0-2", "1-2", "entry level"],
          mid: ["mid", "middle", "3-5", "2-5", "mid level"],
          senior: ["senior", "6-10", "5-10", "senior level"],
          lead: ["lead", "principal", "10+", "lead level", "leadership"],
        };

        const experienceText = job.experience_required.toLowerCase();
        const matchingTerms = experienceMap[filters.experienceLevel] || [];

        if (!matchingTerms.some((term) => experienceText.includes(term))) {
          return false;
        }
      }

      // Salary range filter
      if (filters.salaryRange && job.salary_range) {
        const [minFilter, maxFilter] = filters.salaryRange;
        // Extract numbers from salary range string (e.g., "$50,000 - $80,000")
        const salaryNumbers = job.salary_range.match(/\d+/g);
        if (salaryNumbers && salaryNumbers.length >= 2) {
          const jobMinSalary =
            parseInt(salaryNumbers[0]) *
            (salaryNumbers[0].length <= 3 ? 1000 : 1);
          const jobMaxSalary =
            parseInt(salaryNumbers[1]) *
            (salaryNumbers[1].length <= 3 ? 1000 : 1);

          // Check if job salary range overlaps with filter range
          if (jobMaxSalary < minFilter || jobMinSalary > maxFilter) {
            return false;
          }
        }
      }

      // Location filter (match against location field)
      if (filters.location && filters.location !== "all" && job.location) {
        const locationText = job.location.toLowerCase();
        const filterLocation = filters.location.toLowerCase();

        if (!locationText.includes(filterLocation)) {
          return false;
        }
      }

      // Urgent filter
      if (filters.urgentOnly && !job.is_urgent) {
        return false;
      }

      // Featured filter (assuming featured jobs have higher applicant count or are urgent)
      if (filters.featuredOnly && !job.is_urgent && job.applicant_count < 10) {
        return false;
      }

      return true;
    });
  }, [allJobs, filters]);

  const handleFindMatches = async (jobId: string, jobTitle: string) => {
    setIsMatching(true);
    setSelectedJob({ id: jobId, title: jobTitle });

    try {
      const { data: candidates, error } = await supabase
        .from("candidates_with_normalized_data")
        .select("*")
        .eq("user_id", user.id);

      if (candidates) {
        const matches = candidates.map((candidate) => ({
          id: candidate.id,
          name: candidate.name,
          match: Math.floor(Math.random() * 40) + 60, // Mock match percentage
          role: candidate.role,
          location: candidate.location_name || candidate.location,
          experience: candidate.experience,
          skills: candidate.normalized_skills?.map((s: any) => s.name) || [],
          avatar: candidate.avatar,
          email: candidate.email,
        }));

        setMatchingCandidates(matches);
      }
    } catch (error) {
      console.error("Error finding matches:", error);
    } finally {
      setIsMatching(false);
    }
  };

  const handleArchiveJob = (jobId: string) => {
    updateJobMutation.mutate({ id: jobId, is_active: false });
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      {jobs.map((job) => (
        <Card
          key={job.id}
          onClick={() => navigate(`/jobs/${job.id}`)}
          className="cursor-pointer hover:bg-muted/50 transition-colors"
          role="link"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              navigate(`/jobs/${job.id}`);
            }
          }}
          aria-label={`View details for ${job.title} position`}
        >
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold text-lg">{job.title}</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  {job.department_name || job.department}
                </p>
                <div className="mt-2 flex flex-wrap gap-x-4 gap-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Briefcase className="w-4 h-4" />
                    <span>{job.job_type_name || job.job_type}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <MapPin className="w-4 h-4" />
                    <span>{job.location_name || job.location}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <Clock className="w-4 h-4" />
                    <span>
                      Posted{" "}
                      {formatDistanceToNow(new Date(job.created_at), {
                        addSuffix: true,
                      })}
                    </span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <DollarSign className="w-4 h-4" />
                    <span>{job.salary_range}</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col items-end gap-2">
                <Badge variant={job.is_active ? "default" : "secondary"}>
                  {job.is_active ? "Open" : "Archived"}
                </Badge>
                <div className="flex items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Star className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Add to Favorites</TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Share2 className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Share Job</TooltipContent>
                  </Tooltip>
                  <div onClick={(e) => e.stopPropagation()}>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => setEditingJob(job)}>
                          Edit Job
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleArchiveJob(job.id)}
                        >
                          Archive Job
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="mt-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-sm text-muted-foreground">
                {applicantCounts?.get(job.id) || 0} applicants
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleFindMatches(job.id, job.title)}
                disabled={isMatching}
                className="w-full sm:w-auto"
              >
                {isMatching ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Users className="w-4 h-4 mr-2" />
                )}
                <span className="hidden sm:inline">Find Matches</span>
                <span className="sm:hidden">Matches</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}

      {selectedJob && (
        <JobMatchingModal
          isOpen={matchingCandidates.length > 0}
          onClose={() => {
            setMatchingCandidates([]);
            setSelectedJob(null);
          }}
          matches={matchingCandidates}
          jobTitle={selectedJob.title}
        />
      )}

      {editingJob && (
        <JobFormDialog
          job={editingJob}
          isOpen={!!editingJob}
          onClose={() => setEditingJob(null)}
          mode="edit"
        />
      )}
    </div>
  );
};
