import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2, Wand2, Plus, X } from "lucide-react";
import { generateJobDescription, generateText } from "@/utils/gemini";
import { useCreateJob } from "@/hooks/useCreateJob";
import { DepartmentPicker } from "./DepartmentPicker";
import { LocationPicker } from "./LocationPicker";
import { JobTypePicker } from "./JobTypePicker";

interface JobPostingFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export function JobPostingForm({ isOpen, onClose, onSuccess }: JobPostingFormProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [requirements, setRequirements] = useState<string[]>([""]);
  const [benefits, setBenefits] = useState<string[]>([""]);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    department: "",
    location: "",
    job_type: "Full-time",
    salary_range: "",
    experience_required: "",
    description: "",
    is_urgent: false,
  });

  const { toast } = useToast();
  const createJobMutation = useCreateJob();

  const handleGenerateDescription = async () => {
    if (!formData.title || !formData.department) {
      toast({
        title: "Missing Information",
        description: "Please fill in the job title and department first.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Use the Gemini-powered function to generate a job description
      const generatedDescription = await generateJobDescription(
        formData.title,
        formData.department,
      );
      setFormData((prev) => ({ ...prev, description: generatedDescription }));
      toast({
        title: "Description Generated",
        description: "AI has generated a job description based on your inputs.",
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate job description. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGenerateRequirements = async () => {
    if (!formData.title || !formData.department) {
      toast({
        title: "Missing Information",
        description: "Please fill in the job title and department first.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Create a prompt for Gemini
      const prompt = `
Generate a list of 5-7 specific requirements for a ${formData.title} position in the ${formData.department} department.

Each requirement should be:
1. Specific and measurable
2. Relevant to the role
3. Concise (one sentence each)

Format the response as a JSON array of strings, with each string being a single requirement.
Example: ["5+ years of experience with React", "Bachelor's degree in Computer Science or related field"]
`;

      const systemPrompt =
        "You are an expert technical recruiter with deep knowledge of job requirements across various industries. Create specific, relevant requirements that will attract qualified candidates.";

      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);

      // Parse the JSON response
      const generatedRequirements = JSON.parse(response);

      // Update the requirements
      setRequirements(generatedRequirements);

      toast({
        title: "Requirements Generated",
        description:
          "Job requirements have been generated based on the job title and department.",
      });
    } catch (error) {
      console.error("Error generating requirements:", error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate requirements. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGenerateBenefits = async () => {
    setIsGenerating(true);
    try {
      // Create a prompt for Gemini
      const prompt = `
Generate a list of 4-6 attractive benefits for a job posting for a ${formData.title} position.

Each benefit should be:
1. Appealing to potential candidates
2. Realistic for a modern company
3. Concise (one sentence each)

Format the response as a JSON array of strings, with each string being a single benefit.
Example: ["Comprehensive health insurance", "Flexible work hours"]
`;

      const systemPrompt =
        "You are an expert in HR and employee benefits. Create compelling, realistic benefits that will attract top talent.";

      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);

      // Parse the JSON response
      const generatedBenefits = JSON.parse(response);

      // Update the benefits
      setBenefits(generatedBenefits);

      toast({
        title: "Benefits Generated",
        description: "Job benefits have been generated.",
      });
    } catch (error) {
      console.error("Error generating benefits:", error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate benefits. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const addRequirement = () => {
    setRequirements([...requirements, ""]);
  };

  const removeRequirement = (index: number) => {
    setRequirements(requirements.filter((_, i) => i !== index));
  };

  const updateRequirement = (index: number, value: string) => {
    const updated = [...requirements];
    updated[index] = value;
    setRequirements(updated);
  };

  const addBenefit = () => {
    setBenefits([...benefits, ""]);
  };

  const removeBenefit = (index: number) => {
    setBenefits(benefits.filter((_, i) => i !== index));
  };

  const updateBenefit = (index: number, value: string) => {
    const updated = [...benefits];
    updated[index] = value;
    setBenefits(updated);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    const filteredRequirements = requirements.filter(
      (req) => req.trim() !== "",
    );
    const filteredBenefits = benefits.filter(
      (benefit) => benefit.trim() !== "",
    );

    try {
      await createJobMutation.mutateAsync({
        ...formData,
        requirements:
          filteredRequirements.length > 0 ? filteredRequirements : undefined,
        benefits: filteredBenefits.length > 0 ? filteredBenefits : undefined,
      });

      if (onSuccess) onSuccess();
      onClose();
    } catch (error) {
      console.error("Error creating job:", error);
      // Error handling is done by the useCreateJob hook
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Post a Job
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Input
              placeholder="Job Title"
              value={formData.title}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, title: e.target.value }))
              }
              required
            />
            <DepartmentPicker
              value={formData.department}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, department: value }))
              }
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <LocationPicker
              value={formData.location}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, location: value }))
              }
            />
            <JobTypePicker
              value={formData.job_type}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, job_type: value }))
              }
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Input
              placeholder="Salary Range (e.g., $120k - $160k)"
              value={formData.salary_range}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  salary_range: e.target.value,
                }))
              }
            />
            <Input
              placeholder="Experience Required (e.g., 5+ years)"
              value={formData.experience_required}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  experience_required: e.target.value,
                }))
              }
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Job Description</label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleGenerateDescription}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Wand2 className="w-4 h-4 mr-2" />
                )}
                Generate with AI
              </Button>
            </div>
            <Textarea
              placeholder="Job Description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              className="min-h-[120px]"
              required
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Requirements</label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleGenerateRequirements}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                  ) : (
                    <Wand2 className="w-4 h-4 mr-1" />
                  )}
                  Generate
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addRequirement}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add
                </Button>
              </div>
            </div>
            {requirements.map((req, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  placeholder="Requirement"
                  value={req}
                  onChange={(e) => updateRequirement(index, e.target.value)}
                />
                {requirements.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeRequirement(index)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Benefits</label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleGenerateBenefits}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                  ) : (
                    <Wand2 className="w-4 h-4 mr-1" />
                  )}
                  Generate
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addBenefit}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add
                </Button>
              </div>
            </div>
            {benefits.map((benefit, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  placeholder="Benefit"
                  value={benefit}
                  onChange={(e) => updateBenefit(index, e.target.value)}
                />
                {benefits.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeBenefit(index)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="urgent"
              checked={formData.is_urgent}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  is_urgent: e.target.checked,
                }))
              }
              className="rounded"
            />
            <label htmlFor="urgent" className="text-sm font-medium">
              Mark as urgent
            </label>
          </div>
          <Button
            type="submit"
            className="w-full"
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : null}
            Post Job
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
