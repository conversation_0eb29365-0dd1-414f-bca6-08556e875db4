"use client";

import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, ChevronDown, Plus, X } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

export interface Benefit {
  id: string;
  name: string;
  category?: string;
}

interface BenefitsPickerProps {
  benefits: Benefit[];
  selectedBenefits: Benefit[];
  onBenefitsChange: (benefits: Benefit[]) => void;
  onCreateBenefit: (name: string) => Promise<Benefit>;
  placeholder?: string;
}

export function BenefitsPicker({
  benefits,
  selectedBenefits,
  onBenefitsChange,
  onCreateBenefit,
  placeholder = "Select benefits...",
}: BenefitsPickerProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const commandListRef = useRef<HTMLDivElement>(null);

  const filteredBenefits = benefits.filter((benefit) =>
    benefit.name.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const handleSelect = (benefit: Benefit) => {
    const isSelected = selectedBenefits.some((b) => b.id === benefit.id);
    if (isSelected) {
      onBenefitsChange(selectedBenefits.filter((b) => b.id !== benefit.id));
    } else {
      onBenefitsChange([...selectedBenefits, benefit]);
    }
  };

  const handleCreate = async () => {
    if (searchValue.trim()) {
      setIsCreating(true);
      try {
        const newBenefit = await onCreateBenefit(searchValue.trim());
        onBenefitsChange([...selectedBenefits, newBenefit]);
        setSearchValue("");
        setOpen(false);
      } catch (error) {
        console.error("Failed to create benefit:", error);
      } finally {
        setIsCreating(false);
      }
    }
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (commandListRef.current) {
      commandListRef.current.scrollTop += e.deltaY;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-1">
        {selectedBenefits.map((benefit) => (
          <Badge
            key={benefit.id}
            variant="secondary"
            className="flex items-center gap-1"
          >
            {benefit.name}
            <button
              type="button"
              onClick={() => onBenefitsChange(selectedBenefits.filter((b) => b.id !== benefit.id))}
              className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            <span className="text-muted-foreground">{placeholder}</span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search benefits..."
              value={searchValue}
              onValueChange={setSearchValue}
            />
            <CommandList 
              ref={commandListRef}
              className="max-h-[280px] overflow-y-auto overflow-x-hidden px-2 pb-2"
              onWheel={handleWheel}
            >
              <CommandEmpty>
                <div className="flex items-center justify-between p-2">
                  <span>No benefits found.</span>
                  {searchValue.trim() && (
                    <Button
                      size="sm"
                      onClick={handleCreate}
                      disabled={isCreating}
                    >
                      {isCreating ? (
                        "Creating..."
                      ) : (
                        <>
                          <Plus className="mr-1 h-3 w-3" />
                          Create "{searchValue}"
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </CommandEmpty>
              <CommandGroup>
                {filteredBenefits.map((benefit) => (
                  <CommandItem
                    key={benefit.id}
                    value={benefit.name}
                    onSelect={() => handleSelect(benefit)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedBenefits.some((b) => b.id === benefit.id)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {benefit.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
} 