import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DollarSign,
  TrendingUp,
  Users,
  RefreshCw,
  Loader2,
} from "lucide-react";
import { DepartmentChart } from "./charts/DepartmentChart";
import { useJobsByDepartment } from "@/hooks/useJobsByDepartment";
import { ApplicationsChart } from "./charts/ApplicationsChart";
import { BudgetTracker } from "@/components/analytics/BudgetTracker";
import { ComplianceTracker } from "@/components/analytics/ComplianceTracker";
import { HiringPipeline } from "@/components/analytics/HiringPipeline";
import { RetentionRisk } from "@/components/analytics/RetentionRisk";
import {
  useJobAnalytics,
  useGenerateAnalyticsData,
} from "@/hooks/useAnalyticsDataGeneration";
import { JobAnalyticsData } from "@/services/AnalyticsDataService";

// Removed mock data; replaced with live hook below

const CHART_TYPES = {
  BAR: "bar",
  LINE: "line",
  PIE: "pie",
} as const;

export function JobAnalytics() {
  const [applicationsChartType, setApplicationsChartType] = React.useState<
    "bar" | "line"
  >(CHART_TYPES.BAR);
  const [departmentChartType, setDepartmentChartType] = React.useState<
    "bar" | "pie"
  >(CHART_TYPES.PIE);
  const [analyticsData, setAnalyticsData] =
    React.useState<JobAnalyticsData | null>(null);

  const jobAnalytics = useJobAnalytics();
  const generateAnalytics = useGenerateAnalyticsData();
  const { data: departmentData = [], isLoading: deptLoading } = useJobsByDepartment();

  // Load analytics data on component mount
  React.useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      const data = await jobAnalytics.mutateAsync();
      setAnalyticsData(data);
    } catch (error) {
      console.error("Failed to load analytics data:", error);
    }
  };

  const handleRefreshAnalytics = async () => {
    try {
      await generateAnalytics.mutateAsync();
      await loadAnalyticsData();
    } catch (error) {
      console.error("Failed to refresh analytics:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Job Analytics</h2>
        <Button
          onClick={handleRefreshAnalytics}
          disabled={generateAnalytics.isPending || jobAnalytics.isPending}
          variant="outline"
        >
          {generateAnalytics.isPending || jobAnalytics.isPending ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          Refresh Analytics
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Job Views
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData
                ? analyticsData.totalJobViews.toLocaleString()
                : "0"}
            </div>
            <p className="text-xs text-muted-foreground">
              +18% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Average Time to Fill
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData
                ? `${analyticsData.averageTimeToFill} days`
                : "0 days"}
            </div>
            <p className="text-xs text-muted-foreground">
              -3 days from average
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cost per Hire</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData
                ? `$${analyticsData.costPerHire.toLocaleString()}`
                : "$0"}
            </div>
            <p className="text-xs text-muted-foreground">
              -8% from last quarter
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Applications Over Time</CardTitle>
            <Select
              value={applicationsChartType}
              onValueChange={(value) =>
                setApplicationsChartType(value as "bar" | "line")
              }
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Chart Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={CHART_TYPES.BAR}>Bar Chart</SelectItem>
                <SelectItem value={CHART_TYPES.LINE}>Line Chart</SelectItem>
              </SelectContent>
            </Select>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ApplicationsChart type={applicationsChartType} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Jobs by Department</CardTitle>
            <Select
              value={departmentChartType}
              onValueChange={(value) =>
                setDepartmentChartType(value as "bar" | "pie")
              }
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Chart Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={CHART_TYPES.BAR}>Bar Chart</SelectItem>
                <SelectItem value={CHART_TYPES.PIE}>Pie Chart</SelectItem>
              </SelectContent>
            </Select>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <DepartmentChart
                data={departmentData}
                chartType={departmentChartType}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <BudgetTracker />
        <ComplianceTracker />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <HiringPipeline />
        <RetentionRisk />
      </div>
    </div>
  );
}
