import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Job } from "@/hooks/useJobs";
import { useUpdateJob } from "@/hooks/useUpdateJob";
import { Plus, X } from "lucide-react";
import { DepartmentPicker } from "./DepartmentPicker";
import { LocationPicker } from "./LocationPicker";
import { JobTypePicker } from "./JobTypePicker";

interface EditJobDialogProps {
  job: Job;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (updatedJob: Job) => void; // Make optional since we'll handle updates internally
}

export function EditJobDialog({
  job,
  isOpen,
  onClose,
  onSave,
}: EditJobDialogProps) {
  const [formData, setFormData] = useState<Job>(job);
  const [requirements, setRequirements] = useState<string[]>(
    job.requirements || [""],
  );
  const [benefits, setBenefits] = useState<string[]>(job.benefits || [""]);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const updateJob = useUpdateJob();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSaving(true);

    const filteredRequirements = requirements.filter(
      (req) => req.trim() !== "",
    );
    const filteredBenefits = benefits.filter(
      (benefit) => benefit.trim() !== "",
    );

    try {
      await updateJob.mutateAsync({
        id: job.id,
        title: formData.title,
        department: formData.department,
        location: formData.location,
        job_type: formData.job_type,
        salary_range: formData.salary_range,
        experience_required: formData.experience_required,
        description: formData.description,
        is_urgent: formData.is_urgent,
        requirements: filteredRequirements.length > 0 ? filteredRequirements : null,
        benefits: filteredBenefits.length > 0 ? filteredBenefits : null,
      });

      // Call onSave if provided (for backward compatibility)
      if (onSave) {
        onSave(formData);
      }
      
      onClose();
    } catch (error) {
      console.error("Error updating job:", error);
      // Error handling is done by the useUpdateJob hook
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (field: keyof Job, value: string | boolean) => {
    setFormData((prev) => {
      const updated = { ...prev, [field]: value };
      
      // Handle normalized fields - when user updates department_name, also update department
      if (field === 'department_name') {
        updated.department = value as string;
      }
      if (field === 'location_name') {
        updated.location = value as string;
      }
      if (field === 'job_type_name') {
        updated.job_type = value as string;
      }
      
      return updated;
    });
  };

  const addRequirement = () => {
    setRequirements([...requirements, ""]);
  };

  const removeRequirement = (index: number) => {
    setRequirements(requirements.filter((_, i) => i !== index));
  };

  const updateRequirement = (index: number, value: string) => {
    const updated = [...requirements];
    updated[index] = value;
    setRequirements(updated);
  };

  const addBenefit = () => {
    setBenefits([...benefits, ""]);
  };

  const removeBenefit = (index: number) => {
    setBenefits(benefits.filter((_, i) => i !== index));
  };

  const updateBenefit = (index: number, value: string) => {
    const updated = [...benefits];
    updated[index] = value;
    setBenefits(updated);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Job Posting</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Job Title</label>
              <Input
                value={formData.title}
                onChange={(e) => handleChange("title", e.target.value)}
                placeholder="Job Title"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Department</label>
              <DepartmentPicker
                value={formData.department_name || formData.department}
                onValueChange={(department) => handleChange("department_name", department)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              <LocationPicker
                value={formData.location_name || formData.location}
                onValueChange={(location) => handleChange("location_name", location)}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Job Type</label>
              <JobTypePicker
                value={formData.job_type_name || formData.job_type}
                onValueChange={(jobType) => handleChange("job_type_name", jobType)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Salary Range</label>
              <Input
                value={formData.salary_range || ""}
                onChange={(e) => handleChange("salary_range", e.target.value)}
                placeholder="e.g., $120k - $160k"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Experience Required</label>
              <Input
                value={formData.experience_required || ""}
                onChange={(e) =>
                  handleChange("experience_required", e.target.value)
                }
                placeholder="e.g., 5+ years"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Job Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleChange("description", e.target.value)}
              placeholder="Job Description"
              className="min-h-[120px]"
              required
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Requirements</label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addRequirement}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </div>
            {requirements.map((req, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  placeholder="Requirement"
                  value={req}
                  onChange={(e) => updateRequirement(index, e.target.value)}
                />
                {requirements.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeRequirement(index)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Benefits</label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addBenefit}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add
              </Button>
            </div>
            {benefits.map((benefit, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  placeholder="Benefit"
                  value={benefit}
                  onChange={(e) => updateBenefit(index, e.target.value)}
                />
                {benefits.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeBenefit(index)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="urgent"
              checked={formData.is_urgent}
              onChange={(e) => handleChange("is_urgent", e.target.checked)}
              className="rounded"
            />
            <label htmlFor="urgent" className="text-sm font-medium">
              Mark as urgent
            </label>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
