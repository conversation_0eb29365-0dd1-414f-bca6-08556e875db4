import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Check, ChevronDown, Plus } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useDepartments } from "@/hooks/useDepartments";
import { useCreateDepartment } from "@/hooks/useCreateDepartment";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface DepartmentPickerProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function DepartmentPicker({
  value,
  onValueChange,
  placeholder = "Select department...",
  className,
  disabled = false,
}: DepartmentPickerProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const commandListRef = useRef<HTMLDivElement>(null);

  const { data: departments = [], isLoading } = useDepartments();
  const createDepartment = useCreateDepartment();
  const { toast } = useToast();

  const selectedDepartment = departments.find((dept) => dept.name === value);

  const handleSelect = (departmentName: string) => {
    onValueChange(departmentName);
    setOpen(false);
  };

  const handleCreateDepartment = async (departmentName: string) => {
    try {
      const newDepartment = await createDepartment.mutateAsync({
        name: departmentName.trim(),
      });
      onValueChange(newDepartment.name);
      setSearchValue("");
      setOpen(false);
      toast({
        title: "Department Created",
        description: `"${newDepartment.name}" has been created successfully.`,
      });
    } catch (error) {
      console.error("Error creating department:", error);
      toast({
        title: "Error",
        description: "Failed to create department. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredDepartments = departments.filter((dept) =>
    dept.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (commandListRef.current) {
      commandListRef.current.scrollTop += e.deltaY;
    }
  };

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled || isLoading}
          >
            {selectedDepartment ? selectedDepartment.name : placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search departments..."
              value={searchValue}
              onValueChange={setSearchValue}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && searchValue && !filteredDepartments.some(
                  (dept) => dept.name.toLowerCase() === searchValue.toLowerCase()
                )) {
                  e.preventDefault();
                  handleCreateDepartment(searchValue);
                }
              }}
            />
            <CommandList 
              ref={commandListRef}
              className="max-h-[280px] overflow-y-auto overflow-x-hidden px-2 pb-2"
              onWheel={handleWheel}
            >
              <CommandEmpty>
                <div className="flex flex-col gap-2 p-2">
                  <p className="text-sm text-muted-foreground">No departments found.</p>
                  {searchValue && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2 h-auto p-0 text-sm"
                      onClick={() => handleCreateDepartment(searchValue)}
                      disabled={createDepartment.isPending}
                    >
                      <Plus className="mr-1 h-3 w-3" />
                      {createDepartment.isPending ? "Creating..." : `Create "${searchValue}"`}
                    </Button>
                  )}
                </div>
              </CommandEmpty>
              <CommandGroup>
                {filteredDepartments.map((department) => (
                  <CommandItem
                    key={department.id}
                    value={department.name}
                    onSelect={() => handleSelect(department.name)}
                  >
                    <span>{department.name}</span>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        value === department.name ? "opacity-100" : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))}
                {searchValue &&
                  !filteredDepartments.some(
                    (dept) => dept.name.toLowerCase() === searchValue.toLowerCase(),
                  ) && (
                    <CommandItem
                      value={searchValue}
                      onSelect={() => handleCreateDepartment(searchValue)}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create "{searchValue}"
                    </CommandItem>
                  )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
} 