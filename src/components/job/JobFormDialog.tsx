import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2, Wand2 } from "lucide-react";
import { generateJobDescription, generateText } from "@/utils/gemini";
import { useCreateJob } from "@/hooks/useCreateJob";
import { useUpdateJob } from "@/hooks/useUpdateJob";
import { DepartmentPicker } from "./DepartmentPicker";
import { LocationPicker } from "./LocationPicker";
import { JobTypePicker } from "./JobTypePicker";
import { BenefitsPicker } from "./BenefitsPicker";
import { RequirementsPicker } from "./RequirementsPicker";
import { useBenefits, useCreateBenefit } from "@/hooks/useBenefits";
import { useRequirements, useCreateRequirement } from "@/hooks/useRequirements";
import { Job } from "@/hooks/useJobs";
import { Benefit } from "@/hooks/useBenefits";
import { Requirement } from "@/hooks/useRequirements";

interface JobFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  mode: "create" | "edit";
  job?: Job; // Required for edit mode
}

export function JobFormDialog({ 
  isOpen, 
  onClose, 
  onSuccess, 
  mode, 
  job 
}: JobFormDialogProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedBenefits, setSelectedBenefits] = useState<Benefit[]>(
    mode === "edit" ? (job?.normalized_benefits || []) : []
  );
  const [selectedRequirements, setSelectedRequirements] = useState<Requirement[]>(
    mode === "edit" ? (job?.normalized_requirements || []) : []
  );
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    title: mode === "edit" ? job?.title || "" : "",
    department: mode === "edit" ? job?.department || "" : "",
    location: mode === "edit" ? job?.location || "" : "",
    job_type: mode === "edit" ? job?.job_type || "Full-time" : "Full-time",
    salary_range: mode === "edit" ? job?.salary_range || "" : "",
    experience_required: mode === "edit" ? job?.experience_required || "" : "",
    description: mode === "edit" ? job?.description || "" : "",
    is_urgent: mode === "edit" ? job?.is_urgent || false : false,
  });

  const { toast } = useToast();
  const createJobMutation = useCreateJob();
  const updateJobMutation = useUpdateJob();
  
  // Hooks for benefits and requirements
  const { data: benefits = [] } = useBenefits();
  const { data: requirements = [] } = useRequirements();
  const createBenefitMutation = useCreateBenefit();
  const createRequirementMutation = useCreateRequirement();

  const handleGenerateDescription = async () => {
    if (!formData.title || !formData.department) {
      toast({
        title: "Missing Information",
        description: "Please fill in the job title and department first.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      const generatedDescription = await generateJobDescription(
        formData.title,
        formData.department,
      );
      setFormData((prev) => ({ ...prev, description: generatedDescription }));
      toast({
        title: "Description Generated",
        description: "AI has generated a job description based on your inputs.",
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate job description. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGenerateRequirements = async () => {
    if (!formData.title || !formData.department) {
      toast({
        title: "Missing Information",
        description: "Please fill in the job title and department first.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      const prompt = `
Generate a list of 5-7 specific requirements for a ${formData.title} position in the ${formData.department} department.

Each requirement should be:
1. Specific and measurable
2. Relevant to the role
3. Concise (one sentence each)

Format the response as a JSON array of strings, with each string being a single requirement.
Example: ["5+ years of experience with React", "Bachelor's degree in Computer Science or related field"]
`;

      const systemPrompt =
        "You are an expert technical recruiter with deep knowledge of job requirements across various industries. Create specific, relevant requirements that will attract qualified candidates.";

      const response = await generateText(prompt, systemPrompt);
      const generatedRequirements = JSON.parse(response);
      
      // Add generated requirements as new requirements in the normalized table
      const newRequirements: Requirement[] = [];
      for (const reqName of generatedRequirements) {
        try {
          const newReq = await createRequirementMutation.mutateAsync({ name: reqName });
          newRequirements.push(newReq);
        } catch (error) {
          // Requirement might already exist, that's okay
          console.log(`Requirement "${reqName}" might already exist`);
        }
      }
      
      setSelectedRequirements([...selectedRequirements, ...newRequirements]);
      toast({
        title: "Requirements Generated",
        description: "AI has generated job requirements based on your inputs.",
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate requirements. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGenerateBenefits = async () => {
    if (!formData.title || !formData.department) {
      toast({
        title: "Missing Information",
        description: "Please fill in the job title and department first.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      const prompt = `
Generate a list of 4-6 attractive benefits for a ${formData.title} position in the ${formData.department} department.

Each benefit should be:
1. Specific and valuable
2. Relevant to the role and industry
3. Concise (one sentence each)

Format the response as a JSON array of strings, with each string being a single benefit.
Example: ["Competitive salary with annual bonuses", "Flexible remote work options", "Comprehensive health insurance"]
`;

      const systemPrompt =
        "You are an expert HR professional with deep knowledge of employee benefits and perks. Create attractive, relevant benefits that will attract top talent.";

      const response = await generateText(prompt, systemPrompt);
      const generatedBenefits = JSON.parse(response);
      
      // Add generated benefits as new benefits in the normalized table
      const newBenefits: Benefit[] = [];
      for (const benefitName of generatedBenefits) {
        try {
          const newBenefit = await createBenefitMutation.mutateAsync({ name: benefitName });
          newBenefits.push(newBenefit);
        } catch (error) {
          // Benefit might already exist, that's okay
          console.log(`Benefit "${benefitName}" might already exist`);
        }
      }
      
      setSelectedBenefits([...selectedBenefits, ...newBenefits]);
      toast({
        title: "Benefits Generated",
        description: "AI has generated job benefits based on your inputs.",
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate benefits. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCreateBenefit = async (name: string): Promise<Benefit> => {
    return await createBenefitMutation.mutateAsync({ name });
  };

  const handleCreateRequirement = async (name: string): Promise<Requirement> => {
    return await createRequirementMutation.mutateAsync({ name });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    // Use only normalized data
    const benefitIds = selectedBenefits.map(b => b.id);
    const requirementIds = selectedRequirements.map(r => r.id);

    try {
      if (mode === "create") {
        await createJobMutation.mutateAsync({
          ...formData,
          normalized_benefits: selectedBenefits,
          normalized_requirements: selectedRequirements,
        });
      } else {
        if (!job) throw new Error("Job is required for edit mode");
        await updateJobMutation.mutateAsync({
          id: job.id,
          title: formData.title,
          department: formData.department,
          location: formData.location,
          job_type: formData.job_type,
          salary_range: formData.salary_range,
          experience_required: formData.experience_required,
          description: formData.description,
          is_urgent: formData.is_urgent,
          normalized_benefits: selectedBenefits,
          normalized_requirements: selectedRequirements,
        });
      }

      if (onSuccess) onSuccess();
      onClose();
    } catch (error) {
      console.error(`Error ${mode === "create" ? "creating" : "updating"} job:`, error);
      // Error handling is done by the respective hooks
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (field: keyof typeof formData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {mode === "create" ? "Post a Job" : "Edit Job Posting"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Input
              placeholder="Job Title"
              value={formData.title}
              onChange={(e) => handleChange("title", e.target.value)}
              required
            />
            <DepartmentPicker
              value={formData.department}
              onValueChange={(value) => handleChange("department", value)}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <LocationPicker
              value={formData.location}
              onValueChange={(value) => handleChange("location", value)}
            />
            <JobTypePicker
              value={formData.job_type}
              onValueChange={(value) => handleChange("job_type", value)}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <Input
              placeholder="Salary Range (e.g., $120k - $160k)"
              value={formData.salary_range}
              onChange={(e) => handleChange("salary_range", e.target.value)}
            />
            <Input
              placeholder="Experience Required (e.g., 5+ years)"
              value={formData.experience_required}
              onChange={(e) => handleChange("experience_required", e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Job Description</label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleGenerateDescription}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                ) : (
                  <Wand2 className="w-4 h-4 mr-1" />
                )}
                Generate with AI
              </Button>
            </div>
            <Textarea
              placeholder="Enter job description..."
              value={formData.description}
              onChange={(e) => handleChange("description", e.target.value)}
              rows={4}
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Requirements</label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleGenerateRequirements}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                ) : (
                  <Wand2 className="w-4 h-4 mr-1" />
                )}
                Generate with AI
              </Button>
            </div>
            <RequirementsPicker
              requirements={requirements}
              selectedRequirements={selectedRequirements}
              onRequirementsChange={setSelectedRequirements}
              onCreateRequirement={handleCreateRequirement}
              placeholder="Select requirements..."
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Benefits</label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleGenerateBenefits}
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                ) : (
                  <Wand2 className="w-4 h-4 mr-1" />
                )}
                Generate with AI
              </Button>
            </div>
            <BenefitsPicker
              benefits={benefits}
              selectedBenefits={selectedBenefits}
              onBenefitsChange={setSelectedBenefits}
              onCreateBenefit={handleCreateBenefit}
              placeholder="Select benefits..."
            />
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="urgent"
              checked={formData.is_urgent}
              onChange={(e) => handleChange("is_urgent", e.target.checked)}
              className="rounded"
            />
            <label htmlFor="urgent" className="text-sm font-medium">
              Mark as urgent
            </label>
          </div>
          <Button
            type="submit"
            className="w-full"
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : null}
            {mode === "create" ? "Post Job" : "Save Changes"}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
} 