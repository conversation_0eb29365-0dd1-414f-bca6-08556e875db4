import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Check, ChevronDown, Plus } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useJobTypes } from "@/hooks/useJobTypes";
import { useCreateJobType } from "@/hooks/useCreateJobType";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface JobTypePickerProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function JobTypePicker({
  value,
  onValueChange,
  placeholder = "Select job type...",
  className,
  disabled = false,
}: JobTypePickerProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const commandListRef = useRef<HTMLDivElement>(null);

  const { data: jobTypes = [], isLoading } = useJobTypes();
  const createJobType = useCreateJobType();
  const { toast } = useToast();

  const selectedJobType = jobTypes.find((type) => type.name === value);

  const handleSelect = (jobTypeName: string) => {
    onValueChange(jobTypeName);
    setOpen(false);
  };

  const handleCreateJobType = async (jobTypeName: string) => {
    try {
      const newJobType = await createJobType.mutateAsync({
        name: jobTypeName.trim(),
      });
      onValueChange(newJobType.name);
      setSearchValue("");
      setOpen(false);
      toast({
        title: "Job Type Created",
        description: `"${newJobType.name}" has been created successfully.`,
      });
    } catch (error) {
      console.error("Error creating job type:", error);
      toast({
        title: "Error",
        description: "Failed to create job type. Please try again.",
        variant: "destructive",
      });
    }
  };

  const filteredJobTypes = jobTypes.filter((type) =>
    type.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (commandListRef.current) {
      commandListRef.current.scrollTop += e.deltaY;
    }
  };

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled || isLoading}
          >
            {selectedJobType ? selectedJobType.name : placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search job types..."
              value={searchValue}
              onValueChange={setSearchValue}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && searchValue && !filteredJobTypes.some(
                  (type) => type.name.toLowerCase() === searchValue.toLowerCase()
                )) {
                  e.preventDefault();
                  handleCreateJobType(searchValue);
                }
              }}
            />
            <CommandList 
              ref={commandListRef}
              className="max-h-[280px] overflow-y-auto overflow-x-hidden px-2 pb-2"
              onWheel={handleWheel}
            >
              <CommandEmpty>
                <div className="flex flex-col gap-2 p-2">
                  <p className="text-sm text-muted-foreground">No job types found.</p>
                  {searchValue && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2 h-auto p-0 text-sm"
                      onClick={() => handleCreateJobType(searchValue)}
                      disabled={createJobType.isPending}
                    >
                      <Plus className="mr-1 h-3 w-3" />
                      {createJobType.isPending ? "Creating..." : `Create "${searchValue}"`}
                    </Button>
                  )}
                </div>
              </CommandEmpty>
              <CommandGroup>
                {filteredJobTypes.map((jobType) => (
                  <CommandItem
                    key={jobType.id}
                    value={jobType.name}
                    onSelect={() => handleSelect(jobType.name)}
                  >
                    <span>{jobType.name}</span>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        value === jobType.name ? "opacity-100" : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))}
                {searchValue &&
                  !filteredJobTypes.some(
                    (type) => type.name.toLowerCase() === searchValue.toLowerCase(),
                  ) && (
                    <CommandItem
                      value={searchValue}
                      onSelect={() => handleCreateJobType(searchValue)}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create "{searchValue}"
                    </CommandItem>
                  )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
} 