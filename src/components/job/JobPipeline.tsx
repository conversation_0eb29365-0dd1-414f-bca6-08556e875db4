import { use<PERSON>em<PERSON>, useState, useEffect } from "react";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { PipelineStageDialog } from "./pipeline/PipelineStageDialog";
import { CandidateDetailsDialog } from "./pipeline/CandidateDetailsDialog";
import { PipelineKanbanView } from "./pipeline/PipelineKanbanView";
import { usePipelineStages, usePipelineCandidates, useInitializePipelineData } from "@/hooks/usePipeline";
import { useAuth } from "@/contexts/AuthContext";
import { LayoutG<PERSON>, <PERSON>, ChevronUp, ChevronDown } from "lucide-react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { format } from "date-fns";
import { useProfile } from "@/hooks/useProfiles";
import { supabase } from "@/integrations/supabase/client";

// Types
interface RawCandidate {
  id: string;
  candidate_id: string;
  candidate_name: string;
  role: string;
  stage: string;
  rating: number;
  last_activity: string;
}

export interface NormalizedCandidate {
  id: string; // This will be the candidate_id, not the pipeline entry id
  name: string;
  role: string;
  stage: string;
  rating: number;
  lastActivity: string;
}

type SortColumn = 'name' | 'role' | 'stage' | 'rating' | 'last_activity';
type SortDirection = 'asc' | 'desc';

// Helper function to safely format dates
const formatDateSafely = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return "Invalid date";
    }
    return format(date, "MMMM d, yyyy");
  } catch (error) {
    console.warn("Error formatting date:", dateString, error);
    return "Invalid date";
  }
};

function normalizeCandidate(candidate: RawCandidate): NormalizedCandidate {
  return {
    id: candidate.candidate_id,
    name: candidate.candidate_name,
    role: candidate.role,
    stage: candidate.stage,
    rating: candidate.rating,
    lastActivity: candidate.last_activity,
  };
}

export function JobPipeline() {
  const { user } = useAuth();
  const userId = user?.id;
  const { data: profile } = useProfile();
  const companyId = profile?.company_id || null;

  const {
    data: pipelineStages = [],
    isLoading: stagesLoading,
    error: stagesError,
  } = usePipelineStages(userId || "");

  // Scope toggle: 'my' (user + company) vs 'company' (company only)
  const [scope, setScope] = useState<"my" | "company">("my");

  // Return to previously working realtime candidates hook (user scoped)
  const {
    data: candidates = [],
    isLoading: candidatesLoading,
  } = usePipelineCandidates(userId || "");

  const { initializeDefaultStages, isLoading: initLoading } =
    useInitializePipelineData();

  const [selectedStage, setSelectedStage] = useState<any>(null);
  const [selectedCandidate, setSelectedCandidate] =
    useState<NormalizedCandidate | null>(null);
  const [viewMode, setViewMode] = useState<"table" | "kanban">("kanban");
  
  // Sorting state
  const [sortColumn, setSortColumn] = useState<SortColumn>('last_activity');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Initialize data if empty
  useEffect(() => {
    if (
      userId &&
      pipelineStages.length === 0 &&
      !stagesLoading &&
      !stagesError
    ) {
      initializeDefaultStages(userId);
    }
  }, [
    userId,
    pipelineStages.length,
    stagesLoading,
    stagesError,
    initializeDefaultStages,
  ]);

  // Sort function
  const sortCandidates = (candidates: RawCandidate[]) => {
    return [...candidates].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortColumn) {
        case 'name':
          aValue = a.candidate_name.toLowerCase();
          bValue = b.candidate_name.toLowerCase();
          break;
        case 'role':
          aValue = a.role.toLowerCase();
          bValue = b.role.toLowerCase();
          break;
        case 'stage':
          aValue = a.stage.toLowerCase();
          bValue = b.stage.toLowerCase();
          break;
        case 'rating':
          aValue = a.rating;
          bValue = b.rating;
          break;
        case 'last_activity':
          aValue = new Date(a.last_activity).getTime();
          bValue = new Date(b.last_activity).getTime();
          break;
        default:
          return 0;
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
    });
  };

  // Handle sort column click
  const handleSort = (column: SortColumn) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Get sort indicator
  const getSortIndicator = (column: SortColumn) => {
    if (sortColumn !== column) {
      return null;
    }
    return sortDirection === 'asc' ? (
      <ChevronUp className="h-4 w-4" />
    ) : (
      <ChevronDown className="h-4 w-4" />
    );
  };

  // Sort candidates
  const [optimisticCandidates, setOptimisticCandidates] = useState<RawCandidate[] | null>(null);
  const effectiveCandidates = optimisticCandidates || candidates;
  const sortedCandidates = sortCandidates(effectiveCandidates);

  // Compute live counts by stage from current candidates, to drive the cards
  const stageCounts = useMemo(() => {
    const counts: Record<string, number> = {};
    for (const c of effectiveCandidates) {
      counts[c.stage] = (counts[c.stage] || 0) + 1;
    }
    return counts;
  }, [effectiveCandidates]);

  if (stagesLoading || candidatesLoading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-5">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-12 mb-2" />
                <Skeleton className="h-2 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Recent Candidates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (stagesError) {
    return (
      <div className="space-y-4">
        <Alert>
          <AlertDescription>
            Error loading pipeline data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const maxCount = Math.max(
    ...pipelineStages.map((stage) => stageCounts[stage.name] || 0),
    1,
  );

  return (
    <div className="space-y-6">
      {/* View & Scope Toggles */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Pipeline</h2>
        <div className="flex items-center gap-2">
          <ToggleGroup
            type="single"
            value={scope}
            onValueChange={(value) => value && setScope(value as "my" | "company")}
          >
            <ToggleGroupItem value="my" aria-label="My pipeline">My</ToggleGroupItem>
            <ToggleGroupItem value="company" aria-label="Company pipeline" disabled={!companyId}>Company</ToggleGroupItem>
          </ToggleGroup>
          <ToggleGroup
            type="single"
            value={viewMode}
            onValueChange={(value) => value && setViewMode(value as "table" | "kanban")}
          >
            <ToggleGroupItem value="kanban" aria-label="Kanban view">
              <LayoutGrid className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="table" aria-label="Table view">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>

      {/* Stage Overview Cards - always visible */}
      <div className="grid gap-4 md:grid-cols-5">
        {pipelineStages.map((stage) => (
          <Card
            key={stage.id}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => setSelectedStage(stage)}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                {stage.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stageCounts[stage.name] || 0}</div>
              <div className="mt-2 h-2 rounded-full bg-gray-200">
                <div
                  className={`h-2 rounded-full ${stage.color}`}
                  style={{ width: `${((stageCounts[stage.name] || 0) / maxCount) * 100}%` }}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* View Content */}
      {viewMode === "kanban" ? (
        <div className="max-w-full overflow-hidden">
          <PipelineKanbanView
            stages={pipelineStages}
            candidates={effectiveCandidates}
            isLoading={candidatesLoading}
            onOptimisticUpdate={(next) => setOptimisticCandidates(next as unknown as RawCandidate[])}
          />
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>All Pipeline Candidates</CardTitle>
          </CardHeader>
          <CardContent>
            {sortedCandidates.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead 
                      className="cursor-pointer hover:bg-accent/50"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        Name
                        {getSortIndicator('name')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-accent/50"
                      onClick={() => handleSort('role')}
                    >
                      <div className="flex items-center gap-2">
                        Role
                        {getSortIndicator('role')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-accent/50"
                      onClick={() => handleSort('stage')}
                    >
                      <div className="flex items-center gap-2">
                        Stage
                        {getSortIndicator('stage')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-accent/50"
                      onClick={() => handleSort('rating')}
                    >
                      <div className="flex items-center gap-2">
                        Rating
                        {getSortIndicator('rating')}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer hover:bg-accent/50"
                      onClick={() => handleSort('last_activity')}
                    >
                      <div className="flex items-center gap-2">
                        Last Activity
                        {getSortIndicator('last_activity')}
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedCandidates.map((candidate) => (
                    <TableRow
                      key={candidate.id}
                      className="cursor-pointer hover:bg-accent/50"
                      onClick={() =>
                        setSelectedCandidate(normalizeCandidate(candidate))
                      }
                    >
                      <TableCell className="font-medium">
                        {candidate.candidate_name}
                      </TableCell>
                      <TableCell>{candidate.role}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">{candidate.stage}</Badge>
                      </TableCell>
                      <TableCell>{candidate.rating}</TableCell>
                      <TableCell>
                        {formatDateSafely(candidate.last_activity)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">
                  No candidates in pipeline yet
                </p>
                <Button variant="outline" size="sm">
                  Add Candidate
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {selectedStage && (
        <PipelineStageDialog
          stage={selectedStage}
          onClose={() => setSelectedStage(null)}
        />
      )}

      {selectedCandidate && (
        <CandidateDetailsDialog
          candidate={selectedCandidate}
          onClose={() => setSelectedCandidate(null)}
        />
      )}
    </div>
  );
}
