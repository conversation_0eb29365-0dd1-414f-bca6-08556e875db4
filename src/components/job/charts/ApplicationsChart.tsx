import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, Cell, LineChart, Line } from "recharts";
import { useAnalyticsApplications } from "@/hooks/useAnalyticsApplications";

interface ApplicationsChartProps {
  type: "bar" | "line";
}

export const ApplicationsChart = ({ type }: ApplicationsChartProps) => {
  const { data = [], isLoading } = useAnalyticsApplications();

  if (isLoading) {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={[]}></BarChart>
      </ResponsiveContainer>
    );
  }

  if (type === "bar") {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 10 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            style={{
              fontSize: "12px",
              fontFamily: "inherit",
            }}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            style={{
              fontSize: "12px",
              fontFamily: "inherit",
            }}
          />
          <Tooltip
            cursor={{ fill: "rgba(236, 237, 254, 0.4)" }}
            contentStyle={{
              backgroundColor: "rgba(255, 255, 255, 0.9)",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
              boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
            }}
          />
          <Bar dataKey="applications" radius={[4, 4, 0, 0]}>
            {data.map((_: any, index: number) => (
              <Cell
                key={`cell-${index}`}
                fill={`url(#applicationGradient-${index})`}
              />
            ))}
          </Bar>
          <defs>
            {data.map((_: any, index: number) => (
              <linearGradient
                key={`applicationGradient-${index}`}
                id={`applicationGradient-${index}`}
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.8} />
                <stop offset="100%" stopColor="#3b82f6" stopOpacity={0.3} />
              </linearGradient>
            ))}
          </defs>
        </BarChart>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={data}
        margin={{ top: 10, right: 30, left: 0, bottom: 10 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="name"
          axisLine={false}
          tickLine={false}
          style={{
            fontSize: "12px",
            fontFamily: "inherit",
          }}
        />
        <YAxis
          axisLine={false}
          tickLine={false}
          style={{
            fontSize: "12px",
            fontFamily: "inherit",
          }}
        />
        <Tooltip
          cursor={{ fill: "rgba(236, 237, 254, 0.4)" }}
          contentStyle={{
            backgroundColor: "rgba(255, 255, 255, 0.9)",
            borderRadius: "8px",
            border: "1px solid #e2e8f0",
            boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
          }}
        />
        <Line
          type="monotone"
          dataKey="applications"
          stroke="url(#applicationGradient)"
          strokeWidth={2}
        />
        <defs>
          <linearGradient id="applicationGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.8} />
            <stop offset="100%" stopColor="#3b82f6" stopOpacity={0.3} />
          </linearGradient>
        </defs>
      </LineChart>
    </ResponsiveContainer>
  );
};
