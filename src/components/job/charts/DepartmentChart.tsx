import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
  PieChart,
  Pie,
} from "recharts";

interface DepartmentChartProps {
  data: Array<{ name: string; count: number; dept_color?: string | null }>;
  chartType: "bar" | "pie";
}

export const DepartmentChart = ({ data, chartType }: DepartmentChartProps) => {
  if (chartType === "bar") {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          layout="vertical"
          margin={{ top: 10, right: 30, left: 80, bottom: 10 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            type="number"
            axisLine={false}
            tickLine={false}
            style={{
              fontSize: "12px",
              fontFamily: "inherit",
            }}
          />
          <YAxis
            type="category"
            dataKey="name"
            axisLine={false}
            tickLine={false}
            style={{
              fontSize: "12px",
              fontFamily: "inherit",
            }}
          />
          <Tooltip
            cursor={{ fill: "rgba(236, 237, 254, 0.4)" }}
            contentStyle={{
              backgroundColor: "rgba(255, 255, 255, 0.9)",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
              boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
            }}
          />
          <Bar dataKey="count" radius={[0, 4, 4, 0]}>
            {data.map((d, index) => (
              <Cell
                key={`cell-${index}`}
                fill={d.dept_color || `url(#departmentGradient-${index})`}
              />
            ))}
          </Bar>
          <defs>
            {data.map((_, index) => (
              <linearGradient
                key={`departmentGradient-${index}`}
                id={`departmentGradient-${index}`}
                x1="0"
                y1="0"
                x2="1"
                y2="0"
              >
                <stop offset="0%" stopColor="#9b87f5" stopOpacity={0.8} />
                <stop offset="100%" stopColor="#7E69AB" stopOpacity={0.3} />
              </linearGradient>
            ))}
          </defs>
        </BarChart>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={data}
          dataKey="count"
          nameKey="name"
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={80}
          paddingAngle={5}
          label
        >
          {data.map((d, index) => (
            <Cell
              key={`cell-${index}`}
              fill={d.dept_color || `url(#colorGradient-${index})`}
            />
          ))}
        </Pie>
        <Tooltip />
        <defs>
          {(() => {
            // Extended palette so we always have a gradient for every slice
            const palette: [string, string][] = [
              ["#60A5FA", "#3B82F6"], // blue
              ["#34D399", "#10B981"], // green
              ["#A78BFA", "#8B5CF6"], // purple
              ["#F472B6", "#EC4899"], // pink
              ["#FBBF24", "#D97706"], // amber
              ["#F59E0B", "#B45309"], // orange
              ["#22D3EE", "#06B6D4"], // cyan
              ["#EF4444", "#DC2626"], // red
              ["#9333EA", "#7E22CE"], // violet
              ["#84CC16", "#65A30D"], // lime
            ];
            return data.map((_, index) => {
              const colors = palette[index % palette.length];
              return (
                <linearGradient
                  key={`colorGradient-${index}`}
                  id={`colorGradient-${index}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="0%" stopColor={colors[0]} />
                  <stop offset="100%" stopColor={colors[1]} />
                </linearGradient>
              );
            });
          })()}
        </defs>
      </PieChart>
    </ResponsiveContainer>
  );
};
