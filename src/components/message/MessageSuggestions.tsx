import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MessageCircle, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { generateText } from "@/utils/gemini";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";

interface Template {
  id: string;
  title: string;
  content: string;
  category?: string;
}

interface MessageSuggestionsProps {
  onSelectTemplate: (content: string) => void;
  templates?: Template[];
}

export const MessageSuggestions = ({
  onSelectTemplate,
  templates = [],
}: MessageSuggestionsProps) => {
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const { data: messageTemplates = [], isLoading } = useMessageTemplates();

  // Convert message templates to the format expected by the component
  const convertedTemplates: Template[] = messageTemplates.map((template) => ({
    id: template.id,
    title: template.name,
    content: template.content,
    category: template.template_category,
  }));

  // Use passed templates if available, otherwise fall back to default templates
  const templatesToShow = templates.length > 0 ? templates : convertedTemplates;

  const generateAIResponse = async (context?: string) => {
    setIsGenerating(true);
    toast({
      title: "AI Assistant",
      description: "Generating personalized response...",
    });

    try {
      // Create a prompt for Gemini
      const prompt = `
Generate a professional, personalized email response for a recruitment conversation.
${context ? `Context: ${context}` : ""}

The response should be:
1. Professional and courteous
2. Clear and concise
3. Engaging and personalized
4. Appropriate for a recruitment context

Generate only the email body text, without any explanations or formatting.
`;

      const systemPrompt =
        "You are an expert recruiter crafting professional communication with candidates. Your responses are clear, engaging, and tailored to recruitment contexts.";

      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);

      // Pass the generated response to the parent component
      onSelectTemplate(response);
    } catch (error) {
      console.error("Error generating AI response:", error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate AI response. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="mt-4">
        <CardContent className="p-4">
          <div className="text-center py-4">Loading templates...</div>
        </CardContent>
      </Card>
    );
  }
  return (
    <Card className="mt-4">
      <CardContent className="p-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Message Templates</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => generateAIResponse()}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Generate AI Response
                </>
              )}
            </Button>
          </div>
          <div className="grid gap-2">
            {templatesToShow.map((template) => (
              <Button
                key={template.id}
                variant="ghost"
                className="justify-start h-auto py-2"
                onClick={() => onSelectTemplate(template.content)}
              >
                <span className="text-left">
                  <div className="font-medium">{template.title}</div>
                  <div className="text-sm text-muted-foreground truncate">
                    {template.content.substring(0, 60)}...
                  </div>
                </span>
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
