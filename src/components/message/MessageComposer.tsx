import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Send,
  Paperclip,
  Image as ImageIcon,
  Link,
  Smile,
  FileText,
  Star,
  Reply,
  Clock,
  UserRound,
  MoreVertical,
  Flag,
  Archive,
  Trash2,
  Mail,
} from "lucide-react";
import { useState } from "react";
import { MessageSuggestions } from "./MessageSuggestions";
import { useToast } from "@/components/ui/use-toast";
import { useCreateMessage } from "@/hooks/useCreateMessage";
import { useUpdateMessage } from "@/hooks/useUpdateMessage";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Message {
  id: string;
  sender: {
    name: string;
    avatar?: string;
    role: string;
  };
  content: string;
  time: string;
  status: "unread" | "read" | "archived";
  isStarred: boolean;
  lastActivity: string;
  followUp?: boolean;
  reminder?: boolean;
}

interface MessageComposerProps {
  selectedMessage?: Message;
  onMessageUpdate: (message: Message) => void;
}

export const MessageComposer = ({
  selectedMessage,
  onMessageUpdate,
}: MessageComposerProps) => {
  const [message, setMessage] = useState("");
  const { toast } = useToast();
  const createMessage = useCreateMessage();
  const updateMessageMutation = useUpdateMessage();

  const handleSend = async () => {
    if (!message.trim()) return;

    try {
      // Create a new message
      await createMessage.mutateAsync({
        sender_name: "You",
        sender_email: "<EMAIL>",
        sender_role: "Recruiter",
        content: message,
        status: "unread",
      });

      toast({
        title: "Message sent",
        description: "Your message has been sent successfully.",
      });
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    }

    setMessage("");
  };

  const handleTemplateSelect = (content: string) => {
    setMessage(content);
  };

  const handleAttachment = () => {
    toast({
      title: "Attachment feature",
      description: "File attachment functionality will be implemented soon.",
    });
  };

  const handleAction = (action: string) => {
    if (!selectedMessage) return;

    const updatedMessage = { ...selectedMessage };

    switch (action) {
      case "Follow-up":
        updatedMessage.followUp = !updatedMessage.followUp;
        break;
      case "Reminder":
        updatedMessage.reminder = !updatedMessage.reminder;
        break;
      case "Mark as Unread":
        updatedMessage.status = "unread";
        break;
      case "Star":
        updatedMessage.isStarred = !updatedMessage.isStarred;
        break;
    }

    onMessageUpdate(updatedMessage);

    // Update the message in the database
    if (selectedMessage) {
      updateMessageMutation.mutate({
        id: selectedMessage.id,
        status: updatedMessage.status,
        is_starred: updatedMessage.isStarred,
        follow_up: updatedMessage.followUp,
        reminder: updatedMessage.reminder,
      });
    }
    toast({
      title: `${action} updated`,
      description: `Message has been ${action.toLowerCase()}.`,
    });
  };

  return (
    <div className="flex-1 p-3 sm:p-4 flex flex-col min-w-0">
      {selectedMessage ? (
        <ScrollArea className="flex-1 mb-3 sm:mb-4">
          <div className="space-y-3 sm:space-y-4">
            <div className="flex items-center justify-between pb-3 sm:pb-4 border-b">
              <div className="flex items-center gap-3 sm:gap-4 min-w-0 flex-1">
                <Avatar className="h-10 w-10 sm:h-12 sm:w-12 flex-shrink-0">
                  <AvatarImage
                    src={selectedMessage.sender.avatar}
                    alt={selectedMessage.sender.name}
                  />
                  <AvatarFallback>
                    <UserRound className="h-5 w-5 sm:h-6 sm:w-6" />
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0 flex-1">
                  <h2 className="text-base sm:text-lg font-semibold truncate">
                    {selectedMessage.sender.name}
                  </h2>
                  <p className="text-xs sm:text-sm text-muted-foreground truncate">
                    {selectedMessage.sender.role}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                <div className="text-xs sm:text-sm text-muted-foreground mr-2 sm:mr-4 hidden sm:block">
                  {selectedMessage.time}
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-8 w-8 sm:h-10 sm:w-10 ${selectedMessage.isStarred ? "text-warning" : ""}`}
                  onClick={() => handleAction("Star")}
                >
                  <Star
                    className={`h-3 w-3 sm:h-4 sm:w-4 ${selectedMessage.isStarred ? "fill-warning" : ""}`}
                  />
                </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-8 w-8 sm:h-10 sm:w-10 ${selectedMessage.followUp ? "text-primary" : ""}`}
                  onClick={() => handleAction("Follow-up")}
                >
                  <Reply
                    className={`h-3 w-3 sm:h-4 sm:w-4 ${selectedMessage.followUp ? "text-primary" : ""}`}
                  />
                </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-8 w-8 sm:h-10 sm:w-10 ${selectedMessage.reminder ? "text-primary" : ""}`}
                  onClick={() => handleAction("Reminder")}
                >
                  <Clock
                    className={`h-3 w-3 sm:h-4 sm:w-4 ${selectedMessage.reminder ? "text-primary" : ""}`}
                  />
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 sm:h-10 sm:w-10"
                    >
                      <MoreVertical className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => handleAction("Mark as Unread")}
                    >
                      <Mail className="mr-2 h-4 w-4" />
                      Mark as Unread
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleAction("View Profile")}
                    >
                      <UserRound className="mr-2 h-4 w-4" />
                      View Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleAction("Flag")}>
                      <Flag className="mr-2 h-4 w-4" />
                      Flag Message
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleAction("Archive")}>
                      <Archive className="mr-2 h-4 w-4" />
                      Archive
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => handleAction("Delete")}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <div className="prose prose-sm max-w-none">
              {selectedMessage.content}
            </div>
          </div>
        </ScrollArea>
      ) : (
        <div className="flex-1 flex items-center justify-center text-muted-foreground">
          Select a message to view the conversation
        </div>
      )}
      <div className="mt-3 sm:mt-4 rounded-lg border bg-background p-3 sm:p-4">
        <Textarea
          placeholder="Type your message..."
          className="min-h-[80px] sm:min-h-[100px] mb-3 sm:mb-4 resize-none text-sm"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
        />
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 sm:h-10 sm:w-10"
              onClick={handleAttachment}
            >
              <Paperclip className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 sm:h-10 sm:w-10"
              onClick={handleAttachment}
            >
              <ImageIcon className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 sm:h-10 sm:w-10 hidden sm:inline-flex"
              onClick={handleAttachment}
            >
              <Link className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 sm:h-10 sm:w-10 hidden sm:inline-flex"
              onClick={handleAttachment}
            >
              <Smile className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8 sm:h-10 sm:w-10"
                  title="Message Templates"
                >
                  <FileText className="h-3 w-3 sm:h-4 sm:w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Message Templates</DialogTitle>
                </DialogHeader>
                <MessageSuggestions onSelectTemplate={handleTemplateSelect} />
              </DialogContent>
            </Dialog>
          </div>
          <Button onClick={handleSend} className="px-3 sm:px-6 text-sm">
            <Send className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Send</span>
            <span className="sm:hidden">Send</span>
          </Button>
        </div>
      </div>
    </div>
  );
};
