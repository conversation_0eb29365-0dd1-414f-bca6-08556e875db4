import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Search,
  Filter,
  Star,
  Clock,
  CheckCheck,
  UserRound,
  Bell,
} from "lucide-react";
import { useState, useEffect, useMemo } from "react";
import { cn } from "@/lib/utils";
import { useUpdateMessage } from "@/hooks/useUpdateMessage";
import { searchMessages } from "@/utils/searchMessages";
import { useDebounce } from "@/hooks/useDebounce";

interface Message {
  id: string;
  sender: {
    name: string;
    avatar?: string;
    role: string;
  };
  content: string;
  time: string;
  status: "unread" | "read" | "archived";
  isStarred: boolean;
  lastActivity: string;
  followUp?: boolean;
  reminder?: boolean;
}

interface MessageListProps {
  messages: Message[];
  onSelectMessage: (message: Message) => void;
  selectedMessageId?: string;
}

export const MessageList = ({
  messages,
  onSelectMessage,
  selectedMessageId,
}: MessageListProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState<
    "all" | "unread" | "starred" | "followup"
  >("all");
  const [searchResults, setSearchResults] = useState<Message[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const updateMessage = useUpdateMessage();

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Perform search when query or filter changes
  useEffect(() => {
    const performSearch = async () => {
      if (!debouncedSearchQuery.trim() && filter === "all") {
        setSearchResults(messages);
        return;
      }

      setIsSearching(true);
      try {
        const filters: any = {};
        if (filter === "unread") filters.status = "unread";
        if (filter === "starred") filters.isStarred = true;
        if (filter === "followup") filters.followUp = true;

        const results = await searchMessages(debouncedSearchQuery, filters);
        
        // Map the results to match the component's Message interface
        const mappedResults = results.map(result => ({
          id: result.id,
          sender: {
            name: result.sender_name,
            avatar: result.sender_avatar,
            role: result.sender_role || "Unknown",
          },
          content: result.content,
          time: new Date(result.created_at).toLocaleString(),
          status: result.status as "unread" | "read" | "archived",
          isStarred: result.is_starred,
          lastActivity: new Date(result.updated_at).toLocaleDateString(),
          followUp: result.follow_up,
          reminder: result.reminder,
        }));
        
        setSearchResults(mappedResults);
      } catch (error) {
        console.error("Search error:", error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedSearchQuery, filter, messages]);

  const filteredMessages = searchResults;

  // Handle message selection and mark as read if unread
  const handleMessageSelect = (message: Message) => {
    onSelectMessage(message);

    // If message is unread, mark it as read
    if (message.status === "unread") {
      updateMessage.mutate({
        id: message.id,
        status: "read",
      });
    }
  };
  return (
    <div className="h-full flex flex-col min-w-0">
      <div className="p-3 sm:p-4 border-b space-y-3 sm:space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search messages..."
            className="pl-9 text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-1 sm:gap-2 flex-wrap">
          <Button
            variant={filter === "all" ? "default" : "outline"}
            size="sm"
            className="text-xs sm:text-sm px-2 sm:px-3"
            onClick={() => setFilter("all")}
          >
            All
          </Button>
          <Button
            variant={filter === "unread" ? "default" : "outline"}
            size="sm"
            className="text-xs sm:text-sm px-2 sm:px-3"
            onClick={() => setFilter("unread")}
          >
            Unread
          </Button>
          <Button
            variant={filter === "starred" ? "default" : "outline"}
            size="sm"
            className="text-xs sm:text-sm px-2 sm:px-3"
            onClick={() => setFilter("starred")}
          >
            Starred
          </Button>
          <Button
            variant={filter === "followup" ? "default" : "outline"}
            size="sm"
            className="text-xs sm:text-sm px-2 sm:px-3"
            onClick={() => setFilter("followup")}
          >
            Follow Up
          </Button>
        </div>
      </div>
      <ScrollArea className="flex-1">
        <div className="p-3 sm:p-4 space-y-3 sm:space-y-4">
          {filteredMessages.map((message) => (
            <div
              key={message.id}
              className={cn(
                "flex gap-3 sm:gap-4 p-3 sm:p-4 rounded-lg border hover:bg-accent/5 transition-colors cursor-pointer relative group min-w-0",
                selectedMessageId === message.id &&
                  "bg-accent/5 border-primary",
              )}
              onClick={() => handleMessageSelect(message)}
            >
              <Avatar className="h-10 w-10 sm:h-12 sm:w-12 flex-shrink-0">
                <AvatarImage
                  src={message.sender.avatar}
                  alt={message.sender.name}
                />
                <AvatarFallback>
                  <UserRound className="h-5 w-5 sm:h-6 sm:w-6" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="min-w-0 flex-1">
                    <div className="font-medium flex items-center gap-2">
                      <span className="truncate">{message.sender.name}</span>
                      {message.status === "unread" && (
                        <span className="h-2 w-2 rounded-full bg-primary flex-shrink-0"></span>
                      )}
                    </div>
                    <div className="text-xs sm:text-sm text-muted-foreground truncate">
                      {message.sender.role}
                    </div>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground flex-shrink-0">
                    <Clock className="h-3 w-3" />
                    <span className="hidden sm:inline">
                      {message.lastActivity}
                    </span>
                    <span className="sm:hidden">
                      {message.lastActivity.split("/")[0]}/
                      {message.lastActivity.split("/")[1]}
                    </span>
                  </div>
                </div>
                <div className="mt-1 text-xs sm:text-sm text-muted-foreground line-clamp-2">
                  {message.content}
                </div>
                <div className="mt-2 flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">
                    {new Date(message.time).toLocaleDateString()}
                  </div>
                  <div className="flex items-center gap-1 sm:gap-2">
                    {message.status === "read" && (
                      <CheckCheck className="h-3 w-3 sm:h-4 sm:w-4 text-success" />
                    )}
                    {message.isStarred && (
                      <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-warning stroke-warning" />
                    )}
                    {message.followUp && (
                      <Bell className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                    )}
                    {message.reminder && (
                      <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};
