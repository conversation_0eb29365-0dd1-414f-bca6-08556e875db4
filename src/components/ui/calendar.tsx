import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { DayPicker } from "react-day-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

export type CalendarProps = React.ComponentProps<typeof DayPicker> & {
  showNavigation?: boolean;
};

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  showNavigation = true,
  ...props
}: CalendarProps) {
  const [month, setMonth] = React.useState<Date>(props.month || new Date());
  
  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];
  
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 100 }, (_, i) => currentYear - 50 + i);
  
  const handleMonthChange = (monthIndex: string) => {
    const newMonth = new Date(month);
    newMonth.setMonth(parseInt(monthIndex));
    setMonth(newMonth);
    props.onMonthChange?.(newMonth);
  };
  
  const handleYearChange = (year: string) => {
    const newMonth = new Date(month);
    newMonth.setFullYear(parseInt(year));
    setMonth(newMonth);
    props.onMonthChange?.(newMonth);
  };
  
  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(month);
    newMonth.setMonth(month.getMonth() + (direction === 'next' ? 1 : -1));
    setMonth(newMonth);
    props.onMonthChange?.(newMonth);
  };

  React.useEffect(() => {
    if (props.month && props.month.getTime() !== month.getTime()) {
      setMonth(props.month);
    }
  }, [props.month, month]);

  const renderNavigation = () => {
    if (!showNavigation) return null;
    
    return (
      <div className="flex items-center justify-between w-full mb-4 px-1">
        <button
          type="button"
          onClick={() => navigateMonth('prev')}
          className={cn(
            buttonVariants({ variant: "outline" }),
            "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
          )}
          aria-label="Previous month"
        >
          <ChevronLeft className="h-4 w-4" />
        </button>
        
        <div className="flex items-center space-x-1">
          <Select 
            value={month.getMonth().toString()} 
            onValueChange={handleMonthChange}
          >
            <SelectTrigger className="h-auto border-none bg-transparent px-1 py-0 font-medium text-sm hover:bg-accent focus:ring-0">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {months.map((monthName, index) => (
                <SelectItem key={monthName} value={index.toString()}>
                  {monthName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select 
            value={month.getFullYear().toString()} 
            onValueChange={handleYearChange}
          >
            <SelectTrigger className="h-auto border-none bg-transparent px-1 py-0 font-medium text-sm hover:bg-accent focus:ring-0">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {years.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <button
          type="button"
          onClick={() => navigateMonth('next')}
          className={cn(
            buttonVariants({ variant: "outline" }),
            "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
          )}
          aria-label="Next month"
        >
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>
    );
  };

  return (
    <div className={cn("p-3", className)}>
      {renderNavigation()}
      
      {/* Calendar */}
      <DayPicker
        showOutsideDays={showOutsideDays}
        className=""
        month={month}
        onMonthChange={setMonth}
        classNames={{
          months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
          month: "space-y-4",
          caption: "hidden",
          caption_label: "hidden",
          nav: "hidden",
          nav_button: "hidden",
          nav_button_previous: "hidden",
          nav_button_next: "hidden",
          table: "w-full border-collapse",
          head_row: "",
          head_cell: "text-muted-foreground w-9 h-9 font-normal text-[0.8rem]",
          row: "",
          cell: "text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
          day: cn(
            buttonVariants({ variant: "ghost" }),
            "h-9 w-9 p-0 font-normal aria-selected:opacity-100",
          ),
          day_range_end: "day-range-end",
          day_selected:
            "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
          day_today: "bg-accent text-accent-foreground",
          day_outside:
            "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
          day_disabled: "text-muted-foreground opacity-50",
          day_range_middle:
            "aria-selected:bg-accent aria-selected:text-accent-foreground",
          day_hidden: "invisible",
          ...classNames,
        }}
        components={{
          Chevron: ({ orientation, ...chevronProps }) => {
            const ChevronIcon =
              orientation === "left" ? ChevronLeft : ChevronRight;
            return <ChevronIcon className="h-4 w-4" {...chevronProps} />;
          },
        }}
        {...props}
      />
    </div>
  );
}
Calendar.displayName = "Calendar";

export { Calendar };