import { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { Loader2, QrCode, Copy, Check, KeyRound, Shield } from "lucide-react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

export function TwoFactorAuth() {
  const [isEnabled, setIsEnabled] = useState(false);
  const [isEnabling, setIsEnabling] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const handleToggle = (checked: boolean) => {
    if (checked && !isEnabled) {
      // Start enabling 2FA
      setIsEnabling(true);
      setShowQRCode(true);

      // Simulate API call to generate QR code
      setTimeout(() => {
        setIsEnabling(false);
      }, 1500);
    } else if (!checked && isEnabled) {
      // Disable 2FA
      setIsEnabled(false);
      setShowQRCode(false);
      setShowBackupCodes(false);
      toast({
        title: "Two-Factor Authentication Disabled",
        description:
          "Your account is now less secure. We recommend enabling 2FA for better security.",
      });
    }
  };

  const handleVerify = () => {
    if (verificationCode.length !== 6) {
      toast({
        title: "Invalid Code",
        description: "Please enter a valid 6-digit verification code.",
        variant: "destructive",
      });
      return;
    }

    // Simulate verification
    setIsEnabling(true);
    setTimeout(() => {
      setIsEnabled(true);
      setIsEnabling(false);
      setShowQRCode(false);

      // Generate backup codes
      const codes = Array.from({ length: 10 }, () =>
        Math.random().toString(36).substring(2, 8).toUpperCase(),
      );
      setBackupCodes(codes);
      setShowBackupCodes(true);

      toast({
        title: "Two-Factor Authentication Enabled",
        description: "Your account is now more secure with 2FA.",
      });
    }, 1500);
  };

  const copyBackupCodes = () => {
    navigator.clipboard.writeText(backupCodes.join("\n"));
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    toast({
      title: "Backup Codes Copied",
      description: "Store these codes in a safe place.",
    });
  };

  const downloadBackupCodes = () => {
    const element = document.createElement("a");
    const file = new Blob([backupCodes.join("\n")], { type: "text/plain" });
    element.href = URL.createObjectURL(file);
    element.download = "2fa-backup-codes.txt";
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    toast({
      title: "Backup Codes Downloaded",
      description: "Store these codes in a safe place.",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Two-Factor Authentication
        </CardTitle>
        <CardDescription>
          Add an extra layer of security to your account by enabling two-factor
          authentication.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="2fa-toggle">Enable Two-Factor Authentication</Label>
            <p className="text-sm text-muted-foreground">
              Protect your account with an additional verification step.
            </p>
          </div>
          <Switch
            id="2fa-toggle"
            checked={isEnabled || isEnabling}
            onCheckedChange={handleToggle}
            disabled={isEnabling}
          />
        </div>

        {showQRCode && (
          <div className="border rounded-lg p-4 space-y-4">
            <h3 className="font-medium">Set Up Two-Factor Authentication</h3>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                1. Scan this QR code with your authenticator app (Google
                Authenticator, Authy, etc.)
              </p>
              <div className="flex justify-center py-4">
                {isEnabling ? (
                  <Loader2 className="h-32 w-32 animate-spin text-muted-foreground" />
                ) : (
                  <QrCode className="h-32 w-32 text-primary" />
                )}
              </div>
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                2. Enter the 6-digit verification code from your authenticator
                app
              </p>
              <div className="flex justify-center py-2">
                <InputOTP
                  maxLength={6}
                  value={verificationCode}
                  onChange={setVerificationCode}
                  disabled={isEnabling}
                >
                  <InputOTPGroup>
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTPGroup>
                </InputOTP>
              </div>
            </div>

            <Button
              onClick={handleVerify}
              disabled={verificationCode.length !== 6 || isEnabling}
              className="w-full"
            >
              {isEnabling ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying...
                </>
              ) : (
                <>
                  <KeyRound className="mr-2 h-4 w-4" />
                  Verify and Enable
                </>
              )}
            </Button>
          </div>
        )}

        {showBackupCodes && (
          <div className="border rounded-lg p-4 space-y-4">
            <h3 className="font-medium">Backup Codes</h3>
            <p className="text-sm text-muted-foreground">
              Save these backup codes in a secure place. You can use them to
              sign in if you lose access to your authenticator app.
            </p>

            <div className="bg-muted p-3 rounded-md font-mono text-sm">
              <div className="grid grid-cols-2 gap-2">
                {backupCodes.map((code, index) => (
                  <div key={index} className="font-mono">
                    {code}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={copyBackupCodes}>
                {copied ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Copied
                  </>
                ) : (
                  <>
                    <Copy className="mr-2 h-4 w-4" />
                    Copy Codes
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={downloadBackupCodes}>
                Download Codes
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
