import { useState, useReducer, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Zap,
  Database,
  Bar<PERSON>hart,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
} from "lucide-react";
import { generateText } from "@/utils/gemini";

// Optimized state management using useReducer for related state
interface OptimizerState {
  isAnalyzing: boolean;
  isOptimizing: boolean;
  analyzeProgress: number;
  optimizeProgress: number;
  analysisComplete: boolean;
  optimizationComplete: boolean;
  aiRecommendations: string;
  isGeneratingRecommendations: boolean;
}

type OptimizerAction =
  | { type: "START_ANALYSIS" }
  | { type: "UPDATE_ANALYSIS_PROGRESS"; progress: number }
  | { type: "COMPLETE_ANALYSIS" }
  | { type: "START_OPTIMIZATION" }
  | { type: "UPDATE_OPTIMIZATION_PROGRESS"; progress: number }
  | { type: "COMPLETE_OPTIMIZATION" }
  | { type: "START_AI_RECOMMENDATIONS" }
  | { type: "SET_AI_RECOMMENDATIONS"; recommendations: string }
  | { type: "RESET" };

const initialState: OptimizerState = {
  isAnalyzing: false,
  isOptimizing: false,
  analyzeProgress: 0,
  optimizeProgress: 0,
  analysisComplete: false,
  optimizationComplete: false,
  aiRecommendations: "",
  isGeneratingRecommendations: false,
};

const optimizerReducer = (
  state: OptimizerState,
  action: OptimizerAction,
): OptimizerState => {
  switch (action.type) {
    case "START_ANALYSIS":
      return {
        ...state,
        isAnalyzing: true,
        analyzeProgress: 0,
        analysisComplete: false,
      };
    case "UPDATE_ANALYSIS_PROGRESS":
      return {
        ...state,
        analyzeProgress: action.progress,
      };
    case "COMPLETE_ANALYSIS":
      return {
        ...state,
        isAnalyzing: false,
        analyzeProgress: 100,
        analysisComplete: true,
      };
    case "START_OPTIMIZATION":
      return {
        ...state,
        isOptimizing: true,
        optimizeProgress: 0,
        optimizationComplete: false,
      };
    case "UPDATE_OPTIMIZATION_PROGRESS":
      return {
        ...state,
        optimizeProgress: action.progress,
      };
    case "COMPLETE_OPTIMIZATION":
      return {
        ...state,
        isOptimizing: false,
        optimizeProgress: 100,
        optimizationComplete: true,
      };
    case "START_AI_RECOMMENDATIONS":
      return {
        ...state,
        isGeneratingRecommendations: true,
      };
    case "SET_AI_RECOMMENDATIONS":
      return {
        ...state,
        isGeneratingRecommendations: false,
        aiRecommendations: action.recommendations,
      };
    case "RESET":
      return initialState;
    default:
      return state;
  }
};

export function PerformanceOptimizer() {
  const { toast } = useToast();
  const [state, dispatch] = useReducer(optimizerReducer, initialState);

  // Optimized handlers using useCallback to prevent unnecessary re-renders
  const handleAnalyze = useCallback(() => {
    dispatch({ type: "START_ANALYSIS" });

    // Optimized progress simulation with requestAnimationFrame for smoother updates
    let progress = 0;
    const updateProgress = () => {
      progress += 5;
      dispatch({ type: "UPDATE_ANALYSIS_PROGRESS", progress });

      if (progress >= 100) {
        dispatch({ type: "COMPLETE_ANALYSIS" });
        toast({
          title: "Analysis Complete",
          description: "Performance analysis has been completed successfully.",
        });
      } else {
        setTimeout(updateProgress, 200);
      }
    };

    updateProgress();
  }, [toast]);

  const generateAIRecommendations = useCallback(async () => {
    dispatch({ type: "START_AI_RECOMMENDATIONS" });

    try {
      const performanceData = {
        analysisComplete: state.analysisComplete,
        optimizationComplete: state.optimizationComplete,
        currentMetrics: {
          loadTime: "2.3s",
          memoryUsage: "45MB",
          queryPerformance: "Good",
          cacheHitRate: "78%",
        },
      };

      const prompt = `
Analyze the following performance optimization data and provide strategic recommendations:

Performance Status:
- Analysis Complete: ${performanceData.analysisComplete}
- Optimization Complete: ${performanceData.optimizationComplete}
- Current Load Time: ${performanceData.currentMetrics.loadTime}
- Memory Usage: ${performanceData.currentMetrics.memoryUsage}
- Query Performance: ${performanceData.currentMetrics.queryPerformance}
- Cache Hit Rate: ${performanceData.currentMetrics.cacheHitRate}

Provide recommendations including:
1. Priority optimization areas
2. Specific technical improvements
3. Performance monitoring strategies
4. Resource allocation suggestions
5. Long-term performance goals

Keep the response actionable and focused on practical implementation steps.
`;

      const systemPrompt =
        "You are an expert performance optimization consultant specializing in application performance. Provide strategic insights that help development teams improve system performance and user experience.";

      const recommendations = await generateText(prompt, systemPrompt);
      dispatch({ type: "SET_AI_RECOMMENDATIONS", recommendations });

      toast({
        title: "AI Recommendations Generated",
        description: "Performance optimization recommendations are ready.",
      });
    } catch (error) {
      console.error("Error generating AI recommendations:", error);
      toast({
        title: "Recommendations Unavailable",
        description: "Unable to generate AI recommendations at the moment.",
        variant: "destructive",
      });
      dispatch({ type: "SET_AI_RECOMMENDATIONS", recommendations: "" });
    }
  }, [state.analysisComplete, state.optimizationComplete, toast]);

  const handleOptimize = useCallback(() => {
    dispatch({ type: "START_OPTIMIZATION" });

    // Optimized progress simulation
    let progress = 0;
    const updateProgress = () => {
      progress += 2;
      dispatch({ type: "UPDATE_OPTIMIZATION_PROGRESS", progress });

      if (progress >= 100) {
        dispatch({ type: "COMPLETE_OPTIMIZATION" });
        toast({
          title: "Optimization Complete",
          description:
            "Performance optimization has been completed successfully.",
        });
      } else {
        setTimeout(updateProgress, 100);
      }
    };

    updateProgress();
  }, [toast]);

  const handleReset = useCallback(() => {
    dispatch({ type: "RESET" });
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Performance Optimizer
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="analyze">
          <TabsList className="mb-4">
            <TabsTrigger value="analyze">Analyze</TabsTrigger>
            <TabsTrigger value="optimize">Optimize</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>

          <TabsContent value="analyze" className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Performance Analysis</h3>
              <p className="text-muted-foreground">
                Analyze your application's performance to identify bottlenecks
                and areas for improvement.
              </p>
            </div>

            {state.isAnalyzing && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Analyzing performance...</span>
                  <span>{state.analyzeProgress}%</span>
                </div>
                <Progress value={state.analyzeProgress} className="h-2" />
              </div>
            )}

            {state.analysisComplete && (
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-medium flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Analysis Complete
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Performance analysis has identified several areas for
                    improvement.
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 shrink-0 mt-0.5" />
                    <div>
                      <h5 className="font-medium">Database Queries</h5>
                      <p className="text-sm text-muted-foreground">
                        Several database queries are not optimized and could be
                        improved with proper indexing.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 shrink-0 mt-0.5" />
                    <div>
                      <h5 className="font-medium">API Response Times</h5>
                      <p className="text-sm text-muted-foreground">
                        Some API endpoints have slow response times that could
                        be improved with caching.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <XCircle className="h-5 w-5 text-red-500 shrink-0 mt-0.5" />
                    <div>
                      <h5 className="font-medium">Memory Usage</h5>
                      <p className="text-sm text-muted-foreground">
                        High memory usage detected in the candidate search
                        functionality.
                      </p>
                    </div>
                  </div>
                </div>

                {/* AI Recommendations Section */}
                <div className="mt-6 space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium flex items-center gap-2">
                      <Sparkles className="h-4 w-4" />
                      AI Performance Recommendations
                    </h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={generateAIRecommendations}
                      disabled={state.isGeneratingRecommendations}
                    >
                      {state.isGeneratingRecommendations ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Generate Insights
                        </>
                      )}
                    </Button>
                  </div>

                  {state.aiRecommendations && (
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <TrendingUp className="h-4 w-4 text-blue-600" />
                        <h5 className="font-medium text-blue-900">
                          AI Optimization Strategy
                        </h5>
                      </div>
                      <div className="text-sm text-gray-700 whitespace-pre-wrap">
                        {state.aiRecommendations}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            <Button
              onClick={handleAnalyze}
              disabled={state.isAnalyzing}
              className="w-full"
            >
              {state.isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <BarChart className="mr-2 h-4 w-4" />
                  Analyze Performance
                </>
              )}
            </Button>
          </TabsContent>

          <TabsContent value="optimize" className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Performance Optimization</h3>
              <p className="text-muted-foreground">
                Apply recommended optimizations to improve your application's
                performance.
              </p>
            </div>

            {state.isOptimizing && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Optimizing performance...</span>
                  <span>{state.optimizeProgress}%</span>
                </div>
                <Progress value={state.optimizeProgress} className="h-2" />
              </div>
            )}

            {state.optimizationComplete && (
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-medium flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Optimization Complete
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Performance optimizations have been applied successfully.
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                    <div>
                      <h5 className="font-medium">Database Optimization</h5>
                      <p className="text-sm text-muted-foreground">
                        Added indexes to improve query performance by up to 45%.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                    <div>
                      <h5 className="font-medium">API Caching</h5>
                      <p className="text-sm text-muted-foreground">
                        Implemented caching for frequently accessed data,
                        reducing response times by 60%.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                    <div>
                      <h5 className="font-medium">Memory Optimization</h5>
                      <p className="text-sm text-muted-foreground">
                        Reduced memory usage in search functionality by
                        implementing lazy loading.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={handleOptimize}
              disabled={state.isOptimizing || !state.analysisComplete}
              className="w-full"
            >
              {state.isOptimizing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Apply Optimizations
                </>
              )}
            </Button>
          </TabsContent>

          <TabsContent value="results" className="space-y-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Performance Results</h3>
              <p className="text-muted-foreground">
                View the results of performance optimizations and improvements.
              </p>
            </div>

            {!state.optimizationComplete ? (
              <div className="text-center py-8 text-muted-foreground">
                No optimization results available yet. Please run the analysis
                and optimization first.
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="bg-muted/50">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Database</h4>
                          <p className="text-3xl font-bold text-green-500">
                            +45%
                          </p>
                        </div>
                        <Database className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Query performance improvement
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-muted/50">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">API</h4>
                          <p className="text-3xl font-bold text-green-500">
                            +60%
                          </p>
                        </div>
                        <Clock className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Response time improvement
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-muted/50">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">Memory</h4>
                          <p className="text-3xl font-bold text-green-500">
                            -30%
                          </p>
                        </div>
                        <Zap className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Memory usage reduction
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-2">Optimization History</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Today, 10:45 AM</span>
                      <span className="text-green-500">Successful</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Yesterday, 3:20 PM</span>
                      <span className="text-yellow-500">Partial</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>June 15, 2025</span>
                      <span className="text-green-500">Successful</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleReset}
                    variant="outline"
                    className="flex-1"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                  <Button onClick={handleAnalyze} className="flex-1">
                    <BarChart className="mr-2 h-4 w-4" />
                    Run New Analysis
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
