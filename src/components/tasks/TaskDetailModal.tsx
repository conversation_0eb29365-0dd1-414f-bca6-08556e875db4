import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  CalendarIcon,
  Edit,
  Save,
  X,
  Clock,
  User,
  Flag,
  Tag,
  FileText,
  Loader2,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Task, UpdateTaskData } from "@/services/TasksService";
import { useUpdateTask } from "@/hooks/useTasks";
import { useToast } from "@/hooks/use-toast";

interface TaskDetailModalProps {
  task: Task | null;
  isOpen: boolean;
  onClose: () => void;
  initialMode?: "view" | "edit";
}

export function TaskDetailModal({
  task,
  isOpen,
  onClose,
  initialMode = "view",
}: TaskDetailModalProps) {
  const [mode, setMode] = useState<"view" | "edit">(initialMode);
  const [editData, setEditData] = useState<{
    title: string;
    description: string;
    status: string;
    priority: string;
    assignee: string;
    due_date: Date | null;
    category: string;
  }>({
    title: "",
    description: "",
    status: "pending",
    priority: "medium",
    assignee: "",
    due_date: null,
    category: "general",
  });

  const updateTaskMutation = useUpdateTask();
  const { toast } = useToast();

  // Reset form when task changes or modal opens
  useEffect(() => {
    if (task) {
      setEditData({
        title: task.title || "",
        description: task.description || "",
        status: task.status || "pending",
        priority: task.priority || "medium",
        assignee: task.assignee || "",
        due_date: task.due_date ? new Date(task.due_date) : null,
        category: task.category || "general",
      });
    }
    setMode(initialMode);
  }, [task, initialMode, isOpen]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleSave = async () => {
    if (!task) return;

    if (!editData.title?.trim()) {
      toast({
        title: "Title Required",
        description: "Please enter a task title.",
        variant: "destructive",
      });
      return;
    }

    try {
      const updateData: UpdateTaskData = {
        title: editData.title.trim(),
        description: editData.description?.trim() || null,
        status: editData.status as UpdateTaskData["status"],
        priority: editData.priority as UpdateTaskData["priority"],
        assignee: editData.assignee?.trim() || null,
        due_date: editData.due_date ? editData.due_date.toISOString() : null,
        category: editData.category as UpdateTaskData["category"],
      };

      await updateTaskMutation.mutateAsync({
        taskId: task.id,
        updateData,
      });

      setMode("view");
      toast({
        title: "Task Updated",
        description: "Your task has been successfully updated.",
      });
    } catch (error) {
      console.error("Error updating task:", error);
      toast({
        title: "Error",
        description: "Failed to update task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    if (task) {
      setEditData({
        title: task.title || "",
        description: task.description || "",
        status: task.status || "pending",
        priority: task.priority || "medium",
        assignee: task.assignee || "",
        due_date: task.due_date ? new Date(task.due_date) : null,
        category: task.category || "general",
      });
    }
    setMode("view");
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl">
              {mode === "edit" ? "Edit Task" : "Task Details"}
            </DialogTitle>
            <div className="flex items-center gap-2">
              {mode === "view" ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMode("edit")}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleCancel}>
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={updateTaskMutation.isPending}
                  >
                    {updateTaskMutation.isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          </div>
          <DialogDescription>
            {mode === "edit"
              ? "Make changes to your task below."
              : "View and manage task details."}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Title */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Title
            </Label>
            {mode === "edit" ? (
              <Input
                value={editData.title}
                onChange={(e) =>
                  setEditData({ ...editData, title: e.target.value })
                }
                placeholder="Enter task title"
              />
            ) : (
              <p className="text-lg font-medium">{task.title}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label>Description</Label>
            {mode === "edit" ? (
              <Textarea
                value={editData.description}
                onChange={(e) =>
                  setEditData({ ...editData, description: e.target.value })
                }
                placeholder="Enter task description"
                className="resize-none"
                rows={4}
              />
            ) : (
              <p className="text-muted-foreground whitespace-pre-wrap">
                {task.description || "No description provided."}
              </p>
            )}
          </div>

          {/* Status and Priority */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Status
              </Label>
              {mode === "edit" ? (
                <Select
                  value={editData.status}
                  onValueChange={(value) =>
                    setEditData({ ...editData, status: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Badge className={getStatusColor(task.status)}>
                  {task.status === "in-progress"
                    ? "In Progress"
                    : task.status.charAt(0).toUpperCase() +
                      task.status.slice(1)}
                </Badge>
              )}
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Flag className="w-4 h-4" />
                Priority
              </Label>
              {mode === "edit" ? (
                <Select
                  value={editData.priority}
                  onValueChange={(value) =>
                    setEditData({ ...editData, priority: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Badge className={getPriorityColor(task.priority)}>
                  {task.priority.charAt(0).toUpperCase() +
                    task.priority.slice(1)}{" "}
                  Priority
                </Badge>
              )}
            </div>
          </div>

          {/* Category and Assignee */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Tag className="w-4 h-4" />
                Category
              </Label>
              {mode === "edit" ? (
                <Select
                  value={editData.category}
                  onValueChange={(value) =>
                    setEditData({ ...editData, category: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="recruitment">Recruitment</SelectItem>
                    <SelectItem value="screening">Screening</SelectItem>
                    <SelectItem value="interview">Interview</SelectItem>
                    <SelectItem value="onboarding">Onboarding</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Badge variant="outline">
                  {task.category.charAt(0).toUpperCase() +
                    task.category.slice(1)}
                </Badge>
              )}
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <User className="w-4 h-4" />
                Assignee
              </Label>
              {mode === "edit" ? (
                <Input
                  value={editData.assignee}
                  onChange={(e) =>
                    setEditData({ ...editData, assignee: e.target.value })
                  }
                  placeholder="Enter assignee name"
                />
              ) : (
                <p className="text-sm">{task.assignee || "Not assigned"}</p>
              )}
            </div>
          </div>

          {/* Due Date */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4" />
              Due Date
            </Label>
            {mode === "edit" ? (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !editData.due_date && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {editData.due_date
                      ? format(editData.due_date, "PPP")
                      : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={editData.due_date || undefined}
                    onSelect={(date) =>
                      setEditData({ ...editData, due_date: date || null })
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            ) : (
              <p className="text-sm">
                {task.due_date
                  ? format(new Date(task.due_date), "PPP")
                  : "No due date set"}
              </p>
            )}
          </div>

          {/* Timestamps */}
          {mode === "view" && (
            <div className="pt-4 border-t space-y-2">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-muted-foreground">
                <div>
                  <strong>Created:</strong>{" "}
                  {format(new Date(task.created_at), "PPP 'at' p")}
                </div>
                {task.updated_at && task.updated_at !== task.created_at && (
                  <div>
                    <strong>Updated:</strong>{" "}
                    {format(new Date(task.updated_at), "PPP 'at' p")}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
