import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  CalendarIcon,
  CheckCircle2,
  Clock,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Loader2,
  CheckSquare,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import {
  useTasks,
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
} from "@/hooks/useTasks";
import { CreateTaskData, TaskFilters, Task } from "@/services/TasksService";
import { useToast } from "@/hooks/use-toast";
import { TaskActionMenu } from "./TaskActionMenu";
import { TaskDetailModal } from "./TaskDetailModal";

export function TaskManager() {
  const { user } = useAuth();
  const { toast } = useToast();

  // States for filters and UI
  const [selectedFilter, setSelectedFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("all");

  // States for task detail modal
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskDetailOpen, setIsTaskDetailOpen] = useState(false);
  const [taskDetailMode, setTaskDetailMode] = useState<"view" | "edit">("view");

  // New task form state - using Date for UI, will convert to string for database
  const [newTask, setNewTask] = useState<{
    title: string;
    description: string;
    status: string;
    priority: string;
    assignee: string;
    due_date: Date | null;
    category: string;
  }>({
    title: "",
    description: "",
    status: "pending",
    priority: "medium",
    assignee: "",
    due_date: null,
    category: "general",
  });

  // Build filters for the query - only use selectedFilter for server-side filtering
  const taskFilters: TaskFilters = {};
  if (selectedFilter !== "all") {
    taskFilters.status = selectedFilter as TaskFilters["status"];
  }

  // Hooks for data fetching and mutations
  // Get all tasks for counting, then filter client-side based on activeTab
  const { data: allTasks = [], isLoading, error } = useTasks(taskFilters);
  const createTaskMutation = useCreateTask();
  const updateTaskMutation = useUpdateTask();
  const deleteTaskMutation = useDeleteTask();

  // Filter tasks based on activeTab and search term
  const filteredTasks = allTasks.filter((task) => {
    // Filter by active tab
    const matchesTab = activeTab === "all" || task.status === activeTab;
    
    // Filter by search term
    const matchesSearch =
      task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (task.description || "").toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesTab && matchesSearch;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleAddTask = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to create tasks.",
        variant: "destructive",
      });
      return;
    }

    if (!newTask.title?.trim()) {
      toast({
        title: "Title Required",
        description: "Please enter a task title.",
        variant: "destructive",
      });
      return;
    }

    try {
      const taskData: CreateTaskData = {
        title: newTask.title.trim(),
        description: newTask.description?.trim() || null,
        status: (newTask.status as CreateTaskData["status"]) || "pending",
        priority: (newTask.priority as CreateTaskData["priority"]) || "medium",
        assignee: newTask.assignee?.trim() || null,
        due_date: newTask.due_date ? newTask.due_date.toISOString() : null,
        category: (newTask.category as CreateTaskData["category"]) || "general",
        user_id: user.id,
      };

      await createTaskMutation.mutateAsync(taskData);

      // Reset form
      setNewTask({
        title: "",
        description: "",
        status: "pending",
        priority: "medium",
        assignee: "",
        due_date: null,
        category: "general",
      });
      setIsAddTaskOpen(false);

      toast({
        title: "Task Created",
        description: "Your task has been successfully created.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getTaskCount = (status: string) => {
    if (status === "all") return allTasks.length;
    return allTasks.filter((task) => task.status === status).length;
  };

  // Task interaction handlers
  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
    setTaskDetailMode("view");
    setIsTaskDetailOpen(true);
  };

  const handleTaskEdit = (task: Task) => {
    setSelectedTask(task);
    setTaskDetailMode("edit");
    setIsTaskDetailOpen(true);
  };

  const handleTaskDetailClose = () => {
    setIsTaskDetailOpen(false);
    setSelectedTask(null);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 min-w-0">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-8 h-8 animate-spin" />
          <span className="ml-2">Loading tasks...</span>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 min-w-0">
        <div className="text-center py-8">
          <h3 className="text-lg font-medium mb-2 text-red-600">
            Error Loading Tasks
          </h3>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error
              ? error.message
              : "Failed to load tasks. Please try again."}
          </p>
        </div>
      </div>
    );
  }

  // Show login required state
  if (!user) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 min-w-0">
        <div className="text-center py-8">
          <h3 className="text-lg font-medium mb-2">Authentication Required</h3>
          <p className="text-sm text-muted-foreground">
            Please log in to view and manage your tasks.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 min-w-0">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div className="flex items-center gap-2 min-w-0">
          <CheckSquare className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0" />
          <div className="min-w-0">
            <h1 className="text-2xl sm:text-3xl font-bold truncate">
              Task Manager
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground truncate">
              Organize and track recruitment tasks and activities
            </p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">

          <Dialog open={isAddTaskOpen} onOpenChange={setIsAddTaskOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="w-4 h-4 mr-2" />
                Add Task
              </Button>
            </DialogTrigger>
          <DialogContent className="mx-4 sm:mx-0 sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Task</DialogTitle>
              <DialogDescription>
                Create a new task to track recruitment activities.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={newTask.title || ""}
                  onChange={(e) =>
                    setNewTask({ ...newTask, title: e.target.value })
                  }
                  placeholder="Enter task title"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newTask.description || ""}
                  onChange={(e) =>
                    setNewTask({ ...newTask, description: e.target.value })
                  }
                  placeholder="Enter task description"
                  className="resize-none"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select
                    value={newTask.priority}
                    onValueChange={(value) =>
                      setNewTask({
                        ...newTask,
                        priority: value as CreateTaskData["priority"],
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={newTask.category}
                    onValueChange={(value) =>
                      setNewTask({
                        ...newTask,
                        category: value as CreateTaskData["category"],
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recruitment">Recruitment</SelectItem>
                      <SelectItem value="screening">Screening</SelectItem>
                      <SelectItem value="interview">Interview</SelectItem>
                      <SelectItem value="onboarding">Onboarding</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="assignee">Assignee</Label>
                <Input
                  id="assignee"
                  value={newTask.assignee || ""}
                  onChange={(e) =>
                    setNewTask({ ...newTask, assignee: e.target.value })
                  }
                  placeholder="Enter assignee name"
                />
              </div>
              <div className="grid gap-2">
                <Label>Due Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !newTask.due_date && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newTask.due_date
                        ? format(newTask.due_date, "PPP")
                        : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={newTask.due_date || undefined}
                      onSelect={(date) =>
                        setNewTask({ ...newTask, due_date: date || null })
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setIsAddTaskOpen(false)}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddTask}
                className="w-full sm:w-auto"
                disabled={createTaskMutation.isPending}
              >
                {createTaskMutation.isPending ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Add Task"
                )}
              </Button>
            </div>
          </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tasks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={selectedFilter} onValueChange={setSelectedFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tasks</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="in-progress">In Progress</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto">
          <TabsTrigger
            value="all"
            className="text-xs sm:text-sm px-2 sm:px-3 py-1"
          >
            <span className="hidden sm:inline">All Tasks</span>
            <span className="sm:hidden">All</span>
            <Badge variant="secondary" className="ml-1 text-xs">
              {getTaskCount("all")}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="pending"
            className="text-xs sm:text-sm px-2 sm:px-3 py-1"
          >
            <span className="hidden sm:inline">Pending</span>
            <span className="sm:hidden">Pend</span>
            <Badge variant="secondary" className="ml-1 text-xs">
              {getTaskCount("pending")}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="in-progress"
            className="text-xs sm:text-sm px-2 sm:px-3 py-1"
          >
            <span className="hidden sm:inline">In Progress</span>
            <span className="sm:hidden">Prog</span>
            <Badge variant="secondary" className="ml-1 text-xs">
              {getTaskCount("in-progress")}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="completed"
            className="text-xs sm:text-sm px-2 sm:px-3 py-1"
          >
            <span className="hidden sm:inline">Completed</span>
            <span className="sm:hidden">Done</span>
            <Badge variant="secondary" className="ml-1 text-xs">
              {getTaskCount("completed")}
            </Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4 mt-4 min-w-0">
          <div className="grid gap-4 min-w-0">
            {filteredTasks.map((task) => (
              <Card
                key={task.id}
                className="hover:shadow-md transition-shadow min-w-0 cursor-pointer"
                onClick={() => handleTaskClick(task)}
              >
                <CardHeader className="pb-2 pt-3 px-3">
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                    <div className="space-y-1 min-w-0 flex-1">
                      <CardTitle className="text-base sm:text-lg break-words">
                        {task.title}
                      </CardTitle>
                      {task.description && (
                        <CardDescription className="break-words">
                          {task.description}
                        </CardDescription>
                      )}
                    </div>
                    <div onClick={(e) => e.stopPropagation()}>
                      <TaskActionMenu task={task} onEdit={handleTaskEdit} />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="min-w-0 px-3 pb-3">
                  <div className="flex flex-wrap gap-2 mb-4">
                    <Badge className={getStatusColor(task.status)}>
                      {task.status === "in-progress"
                        ? "In Progress"
                        : task.status.charAt(0).toUpperCase() +
                          task.status.slice(1)}
                    </Badge>
                    <Badge className={getPriorityColor(task.priority)}>
                      <span className="hidden sm:inline">
                        {task.priority.charAt(0).toUpperCase() +
                          task.priority.slice(1)}{" "}
                        Priority
                      </span>
                      <span className="sm:hidden">
                        {task.priority.charAt(0).toUpperCase() +
                          task.priority.slice(1)}
                      </span>
                    </Badge>
                    <Badge variant="outline">
                      {task.category.charAt(0).toUpperCase() +
                        task.category.slice(1)}
                    </Badge>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-sm text-muted-foreground">
                    {task.assignee && (
                      <span className="break-words">
                        Assigned to: {task.assignee}
                      </span>
                    )}
                    {task.due_date && (
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>
                          Due: {format(new Date(task.due_date), "MMM dd, yyyy")}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTasks.length === 0 && (
            <div className="text-center py-8">
              <CheckCircle2 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-base sm:text-lg font-medium mb-2">
                No tasks found
              </h3>
              <p className="text-sm sm:text-base text-muted-foreground">
                {searchTerm || selectedFilter !== "all"
                  ? "Try adjusting your search or filter criteria."
                  : "Create your first task to get started."}
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Task Detail Modal */}
      <TaskDetailModal
        task={selectedTask}
        isOpen={isTaskDetailOpen}
        onClose={handleTaskDetailClose}
        initialMode={taskDetailMode}
      />
    </div>
  );
}
