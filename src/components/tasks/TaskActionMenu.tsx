import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle2,
  Circle,
  User,
  Flag,
  Clock,
} from "lucide-react";
import { Task } from "@/services/TasksService";
import { useUpdateTask, useDeleteTask } from "@/hooks/useTasks";
import { useToast } from "@/hooks/use-toast";

interface TaskActionMenuProps {
  task: Task;
  onEdit: (task: Task) => void;
}

export function TaskActionMenu({ task, onEdit }: TaskActionMenuProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [assigneeInput, setAssigneeInput] = useState(task.assignee || "");

  const updateTaskMutation = useUpdateTask();
  const deleteTaskMutation = useDeleteTask();
  const { toast } = useToast();

  const handleStatusChange = async (
    newStatus: "pending" | "in-progress" | "completed",
  ) => {
    try {
      await updateTaskMutation.mutateAsync({
        taskId: task.id,
        updateData: { status: newStatus },
      });
    } catch (error) {
      console.error("Error updating task status:", error);
    }
  };

  const handlePriorityChange = async (
    newPriority: "low" | "medium" | "high",
  ) => {
    try {
      await updateTaskMutation.mutateAsync({
        taskId: task.id,
        updateData: { priority: newPriority },
      });
    } catch (error) {
      console.error("Error updating task priority:", error);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteTaskMutation.mutateAsync(task.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Error deleting task:", error);
    }
  };

  const handleAssign = async () => {
    try {
      await updateTaskMutation.mutateAsync({
        taskId: task.id,
        updateData: { assignee: assigneeInput.trim() || null },
      });
      setShowAssignDialog(false);
      toast({
        title: "Task Updated",
        description: assigneeInput.trim()
          ? `Task assigned to ${assigneeInput.trim()}`
          : "Task assignee removed",
      });
    } catch (error) {
      console.error("Error assigning task:", error);
    }
  };

  const toggleComplete = () => {
    const newStatus = task.status === "completed" ? "pending" : "completed";
    handleStatusChange(newStatus);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>Task Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />

          <DropdownMenuItem onClick={() => onEdit(task)}>
            <Edit className="w-4 h-4 mr-2" />
            Edit Task
          </DropdownMenuItem>

          <DropdownMenuItem onClick={toggleComplete}>
            {task.status === "completed" ? (
              <>
                <Circle className="w-4 h-4 mr-2" />
                Mark Incomplete
              </>
            ) : (
              <>
                <CheckCircle2 className="w-4 h-4 mr-2" />
                Mark Complete
              </>
            )}
          </DropdownMenuItem>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Clock className="w-4 h-4 mr-2" />
              Change Status
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem
                onClick={() => handleStatusChange("pending")}
                disabled={task.status === "pending"}
              >
                Pending
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleStatusChange("in-progress")}
                disabled={task.status === "in-progress"}
              >
                In Progress
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleStatusChange("completed")}
                disabled={task.status === "completed"}
              >
                Completed
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Flag className="w-4 h-4 mr-2" />
              Change Priority
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              <DropdownMenuItem
                onClick={() => handlePriorityChange("low")}
                disabled={task.priority === "low"}
              >
                Low Priority
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handlePriorityChange("medium")}
                disabled={task.priority === "medium"}
              >
                Medium Priority
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handlePriorityChange("high")}
                disabled={task.priority === "high"}
              >
                High Priority
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuItem onClick={() => setShowAssignDialog(true)}>
            <User className="w-4 h-4 mr-2" />
            {task.assignee ? "Reassign Task" : "Assign Task"}
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete Task
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Task</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{task.title}"? This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteTaskMutation.isPending}
            >
              {deleteTaskMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Assign Task Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Task</DialogTitle>
            <DialogDescription>
              {task.assignee
                ? `Currently assigned to: ${task.assignee}`
                : "This task is not currently assigned to anyone."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="assignee">Assignee</Label>
              <Input
                id="assignee"
                value={assigneeInput}
                onChange={(e) => setAssigneeInput(e.target.value)}
                placeholder="Enter assignee name (leave empty to unassign)"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAssignDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssign}
              disabled={updateTaskMutation.isPending}
            >
              {updateTaskMutation.isPending ? "Updating..." : "Update"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
