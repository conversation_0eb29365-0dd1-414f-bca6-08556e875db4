import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle2,
  Clock,
  AlertTriangle,
  Plus,
  ArrowRight,
  Calendar,
  User,
  Flag,
} from "lucide-react";
import { format, isToday, isTomorrow, isPast } from "date-fns";
import { useAuth } from "@/contexts/AuthContext";
import {
  useTasks,
  useTaskStats,
  useOverdueTasks,
  useTasksDueToday,
} from "@/hooks/useTasks";
import { Task } from "@/services/TasksService";
import { TaskDetailModal } from "./TaskDetailModal";

interface TaskDashboardWidgetProps {
  showStats?: boolean;
  showRecentTasks?: boolean;
  showUpcomingTasks?: boolean;
  maxTasks?: number;
  onTaskClick?: (task: Task) => void;
  onCreateTask?: () => void;
}

export function TaskDashboardWidget({
  showStats = true,
  showRecentTasks = true,
  showUpcomingTasks = true,
  maxTasks = 5,
  onTaskClick,
  onCreateTask,
}: TaskDashboardWidgetProps) {
  const { user } = useAuth();
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isTaskDetailOpen, setIsTaskDetailOpen] = useState(false);

  // Hooks for task data
  const { data: allTasks = [], isLoading: tasksLoading } = useTasks();
  const { data: taskStats, isLoading: statsLoading } = useTaskStats();
  const { data: overdueTasks = [], isLoading: overdueLoading } =
    useOverdueTasks();
  const { data: tasksDueToday = [], isLoading: dueTodayLoading } =
    useTasksDueToday();

  // Get recent tasks (last 5 created or updated)
  const recentTasks = allTasks
    .sort(
      (a, b) =>
        new Date(b.updated_at || b.created_at).getTime() -
        new Date(a.updated_at || a.created_at).getTime(),
    )
    .slice(0, maxTasks);

  // Get upcoming tasks (due soon, not overdue)
  const upcomingTasks = allTasks
    .filter((task) => {
      if (!task.due_date || task.status === "completed") return false;
      const dueDate = new Date(task.due_date);
      const now = new Date();
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(now.getDate() + 3);
      return dueDate >= now && dueDate <= threeDaysFromNow;
    })
    .sort(
      (a, b) =>
        new Date(a.due_date!).getTime() - new Date(b.due_date!).getTime(),
    )
    .slice(0, maxTasks);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleTaskClick = (task: Task) => {
    if (onTaskClick) {
      onTaskClick(task);
    } else {
      setSelectedTask(task);
      setIsTaskDetailOpen(true);
    }
  };

  const formatDueDate = (dueDate: string) => {
    const date = new Date(dueDate);
    if (isToday(date)) return "Today";
    if (isTomorrow(date)) return "Tomorrow";
    if (isPast(date)) return `Overdue (${format(date, "MMM dd")})`;
    return format(date, "MMM dd");
  };

  if (!user) return null;

  const isLoading =
    tasksLoading || statsLoading || overdueLoading || dueTodayLoading;

  return (
    <div className="space-y-4">
      {/* Task Statistics */}
      {showStats && taskStats && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-md flex items-center gap-2">
              <CheckCircle2 className="w-5 h-5" />
              Task Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {taskStats.total}
                </div>
                <div className="text-sm text-muted-foreground">Total Tasks</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {taskStats.completed}
                </div>
                <div className="text-sm text-muted-foreground">Completed</div>
              </div>
            </div>

            {taskStats.total > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>
                    {Math.round((taskStats.completed / taskStats.total) * 100)}%
                  </span>
                </div>
                <Progress
                  value={(taskStats.completed / taskStats.total) * 100}
                  className="h-2"
                />
              </div>
            )}

            <div className="grid grid-cols-3 gap-2 text-center">
              <div>
                <div className="text-lg font-semibold text-orange-600">
                  {taskStats.pending}
                </div>
                <div className="text-xs text-muted-foreground">Pending</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-blue-600">
                  {taskStats.inProgress}
                </div>
                <div className="text-xs text-muted-foreground">In Progress</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-red-600">
                  {taskStats.overdue}
                </div>
                <div className="text-xs text-muted-foreground">Overdue</div>
              </div>
            </div>

            {onCreateTask && (
              <Button onClick={onCreateTask} className="w-full" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Create Task
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Overdue Tasks Alert */}
      {overdueTasks.length > 0 && (
        <Card className="border-red-100 bg-red-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-md flex items-center gap-2 text-red-700">
              <AlertTriangle className="w-5 h-5" />
              Overdue Tasks ({overdueTasks.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {overdueTasks.slice(0, 3).map((task) => (
              <div
                key={task.id}
                className="flex items-center justify-between p-2 bg-white rounded cursor-pointer hover:bg-gray-50 border-0"
                onClick={() => handleTaskClick(task)}
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{task.title}</p>
                  <p className="text-xs text-red-600">
                    Due {format(new Date(task.due_date!), "MMM dd")}
                  </p>
                </div>
                <Badge
                  className={getPriorityColor(task.priority)}
                  variant="secondary"
                >
                  {task.priority}
                </Badge>
              </div>
            ))}
            {overdueTasks.length > 3 && (
              <p className="text-xs text-muted-foreground text-center">
                +{overdueTasks.length - 3} more overdue tasks
              </p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Tasks Due Today */}
      {tasksDueToday.length > 0 && (
        <Card className="border-orange-100 bg-orange-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-md flex items-center gap-2 text-orange-700">
              <Calendar className="w-5 h-5" />
              Due Today ({tasksDueToday.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {tasksDueToday.slice(0, 3).map((task) => (
              <div
                key={task.id}
                className="flex items-center justify-between p-2 bg-white rounded cursor-pointer hover:bg-gray-50 border-0"
                onClick={() => handleTaskClick(task)}
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{task.title}</p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Badge
                      className={getStatusColor(task.status)}
                      variant="secondary"
                    >
                      {task.status}
                    </Badge>
                    {task.assignee && (
                      <span className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {task.assignee}
                      </span>
                    )}
                  </div>
                </div>
                <Badge
                  className={getPriorityColor(task.priority)}
                  variant="secondary"
                >
                  {task.priority}
                </Badge>
              </div>
            ))}
            {tasksDueToday.length > 3 && (
              <p className="text-xs text-muted-foreground text-center">
                +{tasksDueToday.length - 3} more tasks due today
              </p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Tasks */}
      {showRecentTasks && recentTasks.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-md flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Recent Tasks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {recentTasks.map((task) => (
              <div
                key={task.id}
                className="flex items-center justify-between p-2 rounded cursor-pointer hover:bg-gray-50 border-0"
                onClick={() => handleTaskClick(task)}
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{task.title}</p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Badge
                      className={getStatusColor(task.status)}
                      variant="secondary"
                    >
                      {task.status}
                    </Badge>
                    {task.due_date && (
                      <span className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {formatDueDate(task.due_date)}
                      </span>
                    )}
                  </div>
                </div>
                <Badge
                  className={getPriorityColor(task.priority)}
                  variant="secondary"
                >
                  {task.priority}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Upcoming Tasks */}
      {showUpcomingTasks && upcomingTasks.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-md flex items-center gap-2">
              <ArrowRight className="w-5 h-5" />
              Upcoming Tasks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {upcomingTasks.map((task) => (
              <div
                key={task.id}
                className="flex items-center justify-between p-2 rounded cursor-pointer hover:bg-gray-50 border-0"
                onClick={() => handleTaskClick(task)}
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{task.title}</p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Badge
                      className={getStatusColor(task.status)}
                      variant="secondary"
                    >
                      {task.status}
                    </Badge>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {formatDueDate(task.due_date!)}
                    </span>
                  </div>
                </div>
                <Badge
                  className={getPriorityColor(task.priority)}
                  variant="secondary"
                >
                  {task.priority}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Task Detail Modal */}
      <TaskDetailModal
        task={selectedTask}
        isOpen={isTaskDetailOpen}
        onClose={() => {
          setIsTaskDetailOpen(false);
          setSelectedTask(null);
        }}
        initialMode="view"
      />
    </div>
  );
}
