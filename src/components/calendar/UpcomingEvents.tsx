import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Brain,
  Clock,
  Tag,
  Bell,
  LayoutList,
  LayoutGrid,
  CalendarRange,
  CalendarDays,
  Calendar as CalendarIcon,
  Loader2,
  Trash2,
  ChevronRight,
  MapPin,
  Video,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { useDeleteEvent } from "@/hooks/useDeleteEvent";
import { generateText } from "@/utils/gemini";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { EventManager } from "@/components/calendar/EventManager";

import { useEvents, Event } from "@/hooks/useEvents";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEffect, useState } from "react";

export const UpcomingEvents = ({
  events: propEvents,
  onDeleteEvent,
}: {
  events?: Event[];
  onDeleteEvent?: (eventId: string) => void;
}) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [viewMode, setViewMode] = useState<
    "list" | "grid" | "timeline" | "calendar"
  >("list");
  const [generatingInsights, setGeneratingInsights] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);

  // Use propEvents if provided, otherwise useEvents hook
  const { data: events = [], isLoading: loading, error } = useEvents();
  const allEvents = propEvents || events;
  const deleteEvent = useDeleteEvent();

  // Filter upcoming events (next 7 days)
  const upcomingEvents = (allEvents as Event[]).filter((event) => {
    const eventDate = new Date(event.start_time);
    const now = new Date();
    const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    return eventDate >= now && eventDate <= weekFromNow;
  });

  const generateMeetingSummary = async (eventId: string) => {
    if (!user) return;

    setGeneratingInsights((prev) => ({ ...prev, [eventId]: true }));

    try {
      const event = allEvents.find((e) => e.id === eventId);
      if (!event) {
        toast({
          title: "Error",
          description: "Event not found.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "AI Summary Assistant",
        description: "Generating meeting summary...",
      });

      // Generate AI summary based on event details
      const prompt = `
Generate a comprehensive meeting summary and action items for the following event:

Event Title: ${event.title}
Event Type: ${event.event_type}
Category: ${event.category}
Description: ${event.description || "No description provided"}
Date/Time: ${new Date(event.start_time).toLocaleString()}
Location: ${event.location || "No location specified"}

Please provide:
1. A brief summary of what this meeting is likely about
2. Suggested preparation items
3. Key discussion points to cover
4. Potential action items and follow-ups
5. Success metrics for the meeting

Format the response in a clear, actionable manner for a recruitment professional.
`;

      const systemPrompt =
        "You are an expert meeting facilitator and recruitment consultant. Provide practical, actionable meeting summaries that help recruitment professionals prepare for and conduct effective meetings.";

      const summary = await generateText(prompt, systemPrompt);

      // Store the summary in the database for future reference
      await supabase
        .from("events")
        .update({
          description: event.description
            ? `${event.description}\n\n--- AI SUMMARY ---\n${summary}`
            : `--- AI SUMMARY ---\n${summary}`,
        })
        .eq("id", eventId)
        .eq("user_id", user.id);

      toast({
        title: "Meeting Summary Generated",
        description:
          "AI-powered insights and action items have been added to your event.",
      });
    } catch (error) {
      console.error("Error generating meeting summary:", error);
      toast({
        title: "Summary Generation Failed",
        description: "Unable to generate meeting summary. Please try again.",
        variant: "destructive",
      });
    } finally {
      setGeneratingInsights((prev) => ({ ...prev, [eventId]: false }));
    }
  };

  const handleSetReminder = async (eventId: string) => {
    if (!user) return;

    try {
      const event = allEvents.find((e) => e.id === eventId);
      if (!event) {
        toast({
          title: "Error",
          description: "Event not found.",
          variant: "destructive",
        });
        return;
      }

      // Calculate reminder time (15 minutes before event)
      const eventTime = new Date(event.start_time);
      const reminderTime = new Date(eventTime.getTime() - 15 * 60 * 1000);

      // Create a notification record in the database
      const { error } = await supabase.from("notifications").insert({
        user_id: user.id,
        title: `Upcoming Event: ${event.title}`,
        message: `Your ${event.event_type} "${event.title}" starts in 15 minutes.`,
        type: "reminder",
        scheduled_for: reminderTime.toISOString(),
        is_read: false,
        metadata: {
          event_id: eventId,
          event_type: event.event_type,
          event_location: event.location,
          meeting_link: event.meeting_link,
        },
      });

      if (error) throw error;

      toast({
        title: "Reminder Set",
        description: `You'll be notified 15 minutes before "${event.title}".`,
      });
    } catch (error) {
      console.error("Error setting reminder:", error);
      toast({
        title: "Reminder Failed",
        description: "Unable to set reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleUpdateCategory = async (eventId: string) => {
    toast({
      title: "Category Updated",
      description: "Event category has been updated successfully.",
    });
  };

  const handleDeleteEvent = async (eventId: string) => {
    try {
      await deleteEvent.mutateAsync(eventId);
      if (onDeleteEvent) onDeleteEvent(eventId);
      setIsEventDialogOpen(false);
    } catch (error) {
      console.error("Error deleting event:", error);
    }
  };

  const handleEventClick = (event: Event) => {
    setSelectedEvent(event);
    setIsEventDialogOpen(true);
  };

  const formatEventTime = (event: Event) => {
    const start = new Date(event.start_time);
    const end = new Date(event.end_time);
    const duration = Math.round(
      (end.getTime() - start.getTime()) / (1000 * 60),
    );

    const timeStr = start.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    const durationStr =
      duration >= 60
        ? `${Math.floor(duration / 60)}h ${duration % 60}m`
        : `${duration}m`;

    return { time: timeStr, duration: durationStr };
  };
  const renderEvents = () => {
    if (loading) {
      return <div className="text-center p-4">Loading events...</div>;
    }

    if (upcomingEvents.length === 0) {
      return (
        <div className="text-center p-4 text-muted-foreground">
          No upcoming events in the next 7 days.
        </div>
      );
    }

    switch (viewMode) {
      case "grid":
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {upcomingEvents.map((event) => {
              const { time, duration } = formatEventTime(event);
              return (
                <Card
                  key={event.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-4">
                    <div
                      className="cursor-pointer"
                      onClick={() => handleEventClick(event)}
                    >
                      <div className="mb-2">
                        <h3 className="font-medium line-clamp-1">
                          {event.title}
                        </h3>
                      </div>

                      <div className="space-y-2 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <CalendarIcon className="h-3 w-3" />
                          <span>
                            {new Date(event.start_time).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-3 w-3" />
                          <span>
                            {time} • {duration}
                          </span>
                        </div>
                        {event.location && (
                          <div className="flex items-center gap-2">
                            <MapPin className="h-3 w-3" />
                            <span className="line-clamp-1">
                              {event.location}
                            </span>
                          </div>
                        )}
                        {event.meeting_link && (
                          <div className="flex items-center gap-2">
                            <Video className="h-3 w-3" />
                            <span>Video call</span>
                          </div>
                        )}
                      </div>

                      <div className="flex justify-end mt-3">
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex gap-1">
                          <Badge variant="outline" className="text-xs h-5">
                            {event.event_type}
                          </Badge>
                          <Badge
                            variant={
                              event.priority === "high"
                                ? "destructive"
                                : "secondary"
                            }
                            className="text-xs h-5"
                          >
                            {event.priority}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex justify-end gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            generateMeetingSummary(event.id);
                          }}
                          disabled={generatingInsights[event.id]}
                          className="h-8 w-8 p-0"
                        >
                          {generatingInsights[event.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Brain className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetReminder(event.id);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Bell className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUpdateCategory(event.id);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Tag className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteEvent(event.id);
                          }}
                          className="text-destructive h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        );

      case "timeline":
        return (
          <div className="space-y-4 relative before:absolute before:left-2 before:top-0 before:bottom-0 before:w-0.5 before:bg-border">
            {upcomingEvents.map((event) => {
              const { time, duration } = formatEventTime(event);
              return (
                <div key={event.id} className="pl-8 relative">
                  <div className="absolute left-0 top-2 w-4 h-4 rounded-full bg-primary"></div>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div
                        className="cursor-pointer"
                        onClick={() => handleEventClick(event)}
                      >
                        <div className="mb-2">
                          <h3 className="font-medium">{event.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {new Date(event.start_time).toLocaleDateString()} at{" "}
                            {time}
                          </p>
                        </div>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <div className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {duration}
                            </span>
                            {event.location && (
                              <span className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {event.location}
                              </span>
                            )}
                          </div>
                          <ChevronRight className="h-4 w-4" />
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex gap-1">
                            <Badge variant="outline" className="text-xs h-5">
                              {event.event_type}
                            </Badge>
                            <Badge
                              variant={
                                event.priority === "high"
                                  ? "destructive"
                                  : "secondary"
                              }
                              className="text-xs h-5"
                            >
                              {event.priority}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              generateMeetingSummary(event.id);
                            }}
                            disabled={generatingInsights[event.id]}
                            className="h-8 w-8 p-0"
                          >
                            {generatingInsights[event.id] ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Brain className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSetReminder(event.id);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Bell className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleUpdateCategory(event.id);
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Tag className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteEvent(event.id);
                            }}
                            className="text-destructive h-8 w-8 p-0"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>
        );

      case "calendar":
        return (
          <div className="grid grid-cols-7 gap-1">
            {Array.from({ length: 7 }, (_, i) => (
              <div key={i} className="text-center text-sm font-medium p-2">
                {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"][i]}
              </div>
            ))}
            {Array.from({ length: 35 }, (_, i) => (
              <div
                key={`day-${i}`}
                className="aspect-square p-1 border rounded-md"
              >
                <div className="text-xs text-right text-muted-foreground">
                  {i + 1}
                </div>
                {upcomingEvents.some(
                  (e) => new Date(e.start_time).getDate() === i + 1,
                ) && (
                  <div className="mt-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        );

      default: // list view
        return (
          <div className="space-y-3">
            {upcomingEvents.map((event) => {
              const { time, duration } = formatEventTime(event);
              return (
                <Card
                  key={event.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-4">
                    <div
                      className="cursor-pointer"
                      onClick={() => handleEventClick(event)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-medium mb-1">{event.title}</h3>
                          <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <CalendarIcon className="h-3 w-3" />
                              {new Date(event.start_time).toLocaleDateString()}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {time} • {duration}
                            </span>
                            {event.location && (
                              <span className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {event.location}
                              </span>
                            )}
                            {event.meeting_link && (
                              <span className="flex items-center gap-1">
                                <Video className="h-3 w-3" />
                                Video call
                              </span>
                            )}
                          </div>
                        </div>
                        <ChevronRight className="h-5 w-5 text-muted-foreground ml-2 flex-shrink-0" />
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex gap-1">
                          <Badge variant="outline" className="text-xs h-5">
                            {event.event_type}
                          </Badge>
                          <Badge
                            variant={
                              event.priority === "high"
                                ? "destructive"
                                : "secondary"
                            }
                            className="text-xs h-5"
                          >
                            {event.priority}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex justify-end gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            generateMeetingSummary(event.id);
                          }}
                          disabled={generatingInsights[event.id]}
                          className="h-8 w-8 p-0"
                          title="Generate AI Summary"
                        >
                          {generatingInsights[event.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Brain className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSetReminder(event.id);
                          }}
                          className="h-8 w-8 p-0"
                          title="Set Reminder"
                        >
                          <Bell className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUpdateCategory(event.id);
                          }}
                          className="h-8 w-8 p-0"
                          title="Update Category"
                        >
                          <Tag className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteEvent(event.id);
                          }}
                          className="text-destructive h-8 w-8 p-0"
                          title="Delete Event"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        );
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="mb-3">
            <span>Upcoming Events</span>
          </CardTitle>
          <div className="flex justify-start">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="shrink-0">
                  {viewMode === "list" && (
                    <LayoutList className="h-4 w-4 mr-2" />
                  )}
                  {viewMode === "grid" && (
                    <LayoutGrid className="h-4 w-4 mr-2" />
                  )}
                  {viewMode === "timeline" && (
                    <CalendarRange className="h-4 w-4 mr-2" />
                  )}
                  {viewMode === "calendar" && (
                    <CalendarDays className="h-4 w-4 mr-2" />
                  )}
                  <span className="hidden sm:inline">
                    View as {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
                  </span>
                  <span className="sm:hidden">
                    {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setViewMode("list")}>
                  <LayoutList className="h-4 w-4 mr-2" />
                  List View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setViewMode("grid")}>
                  <LayoutGrid className="h-4 w-4 mr-2" />
                  Grid View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setViewMode("timeline")}>
                  <CalendarRange className="h-4 w-4 mr-2" />
                  Timeline View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setViewMode("calendar")}>
                  <CalendarDays className="h-4 w-4 mr-2" />
                  Calendar View
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>{renderEvents()}</CardContent>
      </Card>

      {selectedEvent && (
        <EventManager
          event={selectedEvent}
          isOpen={isEventDialogOpen}
          onClose={() => {
            setIsEventDialogOpen(false);
            setSelectedEvent(null);
          }}
          mode="edit"
          onEventEdited={(updatedEvent) => {
            // Refresh events if needed
            setIsEventDialogOpen(false);
            setSelectedEvent(null);
          }}
          onEventDeleted={(eventId) => {
            handleDeleteEvent(eventId);
          }}
        />
      )}
    </>
  );
};
