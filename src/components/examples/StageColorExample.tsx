import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { getStageColorClasses } from "@/utils/stageUtils";

/**
 * Example component demonstrating all the supported stage colors
 * using the new getStageColorClasses function
 */
export function StageColorExample() {
  const stageCategories = [
    {
      category: "Initial Application",
      stages: ["Applied", "New"],
    },
    {
      category: "Screening",
      stages: ["Phone Screen", "Screening"],
    },
    {
      category: "Interview",
      stages: ["Interview", "Technical Interview", "Behavioral Interview"],
    },
    {
      category: "Final Stages",
      stages: ["Final Round", "Final Interview"],
    },
    {
      category: "Decision",
      stages: ["Offer", "Offer Extended", "Offer Accepted"],
    },
    {
      category: "Completion",
      stages: ["Hired", "Onboarding"],
    },
    {
      category: "Rejection",
      stages: ["Rejected", "Declined", "Withdrawn"],
    },
    {
      category: "On Hold",
      stages: ["On Hold", "Paused"],
    },
  ];

  return (
    <Card className="max-w-4xl">
      <CardHeader>
        <CardTitle>Stage Color Reference</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {stageCategories.map((category) => (
          <div key={category.category}>
            <h3 className="text-sm font-medium mb-2 text-muted-foreground">
              {category.category}
            </h3>
            <div className="flex flex-wrap gap-2">
              {category.stages.map((stage) => (
                <Badge
                  key={stage}
                  variant="secondary"
                  className={getStageColorClasses(stage)}
                >
                  {stage}
                </Badge>
              ))}
            </div>
          </div>
        ))}
        
        <div className="pt-4 border-t">
          <h3 className="text-sm font-medium mb-2 text-muted-foreground">
            Unknown/Default Stage
          </h3>
          <Badge
            variant="secondary"
            className={getStageColorClasses("Unknown Stage")}
          >
            Unknown Stage
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
