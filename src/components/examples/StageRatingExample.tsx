import { Badge } from "@/components/ui/badge";
import { Star } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { getStageColor, STAR_RATING_CONFIG } from "@/utils/stageUtils";

/**
 * Example component demonstrating the consistent usage of stage badges and rating stars
 * across the application for displaying candidate/application information.
 */
export function StageRatingExample() {

  // Render star rating component using shared config
  const renderStars = (rating: number, size: keyof typeof STAR_RATING_CONFIG.sizes = "md") => {
    return (
      <div className="flex items-center gap-0.5">
        {Array.from({ length: STAR_RATING_CONFIG.maxStars }).map((_, index) => (
          <Star
            key={index}
            className={`${STAR_RATING_CONFIG.sizes[size]} ${
              index < rating
                ? STAR_RATING_CONFIG.filledClass
                : STAR_RATING_CONFIG.emptyClass
            }`}
          />
        ))}
      </div>
    );
  };

  // Example application data
  const exampleApplication = {
    name: "<PERSON>",
    stage: "Interview",
    rating: 4,
  };

  return (
    <Card className="max-w-md">
      <CardHeader>
        <CardTitle>Stage & Rating Visual Components</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="text-sm font-medium mb-2">Stage Badge:</h3>
          <Badge
            variant="secondary"
            className={getStageColor(exampleApplication.stage)}
          >
            {exampleApplication.stage}
          </Badge>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Rating Stars:</h3>
          {renderStars(exampleApplication.rating)}
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Combined Display:</h3>
          <div className="flex items-center gap-3">
            <span className="font-medium">{exampleApplication.name}</span>
            <Badge
              variant="secondary"
              className={getStageColor(exampleApplication.stage)}
            >
              {exampleApplication.stage}
            </Badge>
            {renderStars(exampleApplication.rating)}
          </div>
        </div>

        <div className="pt-4 border-t">
          <h3 className="text-sm font-medium mb-2">Alternative Simple Rating:</h3>
          <div className="text-lg">
            {"⭐".repeat(exampleApplication.rating)}
            {"☆".repeat(5 - exampleApplication.rating)}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
