import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Plus,
  Edit,
  Trash2,
  FileText,
  Star,
  Sparkles,
  Loader2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { generateText } from "@/utils/gemini";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { useCreateMessageTemplate } from "@/hooks/useCreateMessageTemplate";
import { useUpdateMessageTemplate } from "@/hooks/useUpdateMessageTemplate";
import { useDeleteMessageTemplate } from "@/hooks/useDeleteMessageTemplate";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export function EmailTemplateManager() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<any>(null);
  const [newTemplate, setNewTemplate] = useState({
    name: "",
    subject: "",
    content: "",
    template_category: "general" as const,
  });
  const [isGeneratingTemplate, setIsGeneratingTemplate] = useState(false);
  const [aiPrompt, setAiPrompt] = useState("");

  const { toast } = useToast();
  const { data: templates = [], isLoading } = useMessageTemplates();
  const createTemplate = useCreateMessageTemplate();
  const updateTemplate = useUpdateMessageTemplate();
  const deleteTemplate = useDeleteMessageTemplate();

  const templateCategories = [
    { value: "general", label: "General" },
    { value: "interview", label: "Interview" },
    { value: "follow_up", label: "Follow Up" },
    { value: "rejection", label: "Rejection" },
    { value: "offer", label: "Job Offer" },
  ];

  const handleCreateTemplate = async () => {
    if (!newTemplate.name || !newTemplate.subject || !newTemplate.content) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createTemplate.mutateAsync(newTemplate);
      setNewTemplate({
        name: "",
        subject: "",
        content: "",
        template_category: "general",
      });
      setIsCreateDialogOpen(false);
      toast({
        title: "Template Created",
        description: "Email template has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create template. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleUpdateTemplate = async () => {
    if (!editingTemplate) return;

    try {
      await updateTemplate.mutateAsync({
        id: editingTemplate.id,
        name: editingTemplate.name,
        subject: editingTemplate.subject,
        content: editingTemplate.content,
        template_category: editingTemplate.template_category,
      });
      setEditingTemplate(null);
      toast({
        title: "Template Updated",
        description: "Email template has been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update template. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    try {
      await deleteTemplate.mutateAsync(templateId);
      toast({
        title: "Template Deleted",
        description: "Email template has been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete template. Please try again.",
        variant: "destructive",
      });
    }
  };

  const generateAITemplate = async () => {
    if (!aiPrompt.trim() || isGeneratingTemplate) return;

    setIsGeneratingTemplate(true);
    try {
      const prompt = `
Create a professional email template for recruitment purposes based on this request:
"${aiPrompt}"

Generate a complete email template with:
1. A clear, professional subject line
2. Personalized email content with appropriate placeholders like {candidateName}, {companyName}, {jobTitle}, etc.
3. Professional tone suitable for recruitment communication
4. Clear call-to-action where appropriate

Return the response in this exact JSON format:
{
  "name": "Template name (descriptive title)",
  "subject": "Subject line with placeholders if needed",
  "content": "Full email content with placeholders"
}
`;

      const systemPrompt =
        "You are an expert recruitment communication specialist. Create professional, engaging email templates that follow best practices for candidate outreach and communication.";

      const response = await generateText(prompt, systemPrompt);
      const cleanedResponse = response
        .replace(/```json\s*/, "")
        .replace(/```\s*$/, "")
        .trim();
      const templateData = JSON.parse(cleanedResponse);

      if (templateData.name && templateData.subject && templateData.content) {
        setNewTemplate({
          name: templateData.name,
          subject: templateData.subject,
          content: templateData.content,
          template_category: "general",
        });

        toast({
          title: "Template Generated",
          description:
            "AI has generated a professional email template for you.",
        });
      }
    } catch (error) {
      console.error("Error generating template:", error);
      toast({
        title: "Generation Failed",
        description: "Unable to generate template. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingTemplate(false);
      setAiPrompt("");
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "interview":
        return "bg-blue-100 text-blue-800";
      case "follow_up":
        return "bg-green-100 text-green-800";
      case "rejection":
        return "bg-red-100 text-red-800";
      case "offer":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Email Templates
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Email Templates
          </CardTitle>
          <Dialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
          >
            <DialogTrigger asChild>
              <Button size="sm" className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl w-[95vw] max-h-[90vh] overflow-y-auto mx-4">
              <DialogHeader>
                <DialogTitle>Create Email Template</DialogTitle>
                <DialogDescription>
                  Create a reusable email template for your recruitment process.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                {/* AI Template Generation */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Sparkles className="h-4 w-4 text-blue-600" />
                    <h3 className="font-medium text-blue-900">
                      AI Template Generator
                    </h3>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label htmlFor="ai-prompt">
                        Describe the email template you need
                      </Label>
                      <Textarea
                        id="ai-prompt"
                        placeholder="e.g., Interview invitation for senior developer position, follow-up email after phone screening, rejection email with feedback..."
                        value={aiPrompt}
                        onChange={(e) => setAiPrompt(e.target.value)}
                        rows={2}
                      />
                    </div>
                    <Button
                      onClick={generateAITemplate}
                      disabled={!aiPrompt.trim() || isGeneratingTemplate}
                      className="w-full"
                      variant="outline"
                    >
                      {isGeneratingTemplate ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating Template...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Generate AI Template
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="template-name">Template Name *</Label>
                    <Input
                      id="template-name"
                      placeholder="e.g., Initial Interview Invitation"
                      value={newTemplate.name}
                      onChange={(e) =>
                        setNewTemplate((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Category</Label>
                    <Select
                      value={newTemplate.template_category}
                      onValueChange={(value: any) =>
                        setNewTemplate((prev) => ({
                          ...prev,
                          template_category: value,
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {templateCategories.map((category) => (
                          <SelectItem
                            key={category.value}
                            value={category.value}
                          >
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="template-subject">Subject Line *</Label>
                  <Input
                    id="template-subject"
                    placeholder="e.g., Interview Opportunity at [Company Name]"
                    value={newTemplate.subject}
                    onChange={(e) =>
                      setNewTemplate((prev) => ({
                        ...prev,
                        subject: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="template-content">Email Content *</Label>
                  <Textarea
                    id="template-content"
                    placeholder="Use {candidateName} for dynamic candidate name insertion..."
                    value={newTemplate.content}
                    onChange={(e) =>
                      setNewTemplate((prev) => ({
                        ...prev,
                        content: e.target.value,
                      }))
                    }
                    rows={8}
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button
                    onClick={handleCreateTemplate}
                    disabled={createTemplate.isPending}
                    className="w-full sm:w-auto"
                  >
                    {createTemplate.isPending
                      ? "Creating..."
                      : "Create Template"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                    className="w-full sm:w-auto"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {templates.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Templates Yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first email template to streamline your communication.
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Template
            </Button>
          </div>
        ) : (
          <ScrollArea className="h-64 sm:h-96">
            <div className="space-y-3">
              {templates.map((template) => (
                <div key={template.id} className="p-3 sm:p-4 border rounded-lg">
                  <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-2 sm:gap-0 mb-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                        <h4 className="font-medium text-sm sm:text-base truncate">
                          {template.name}
                        </h4>
                        <Badge
                          className={`${getCategoryColor(template.template_category)} text-xs w-fit`}
                        >
                          {
                            templateCategories.find(
                              (c) => c.value === template.template_category,
                            )?.label
                          }
                        </Badge>
                      </div>
                      <p className="text-xs sm:text-sm text-muted-foreground mb-2 line-clamp-1">
                        Subject: {template.subject}
                      </p>
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {template.content.substring(0, 120)}...
                      </p>
                    </div>
                    <div className="flex gap-1 sm:ml-4 self-end sm:self-start">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingTemplate(template)}
                        className="h-8 w-8"
                      >
                        <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDeleteTemplate(template.id)}
                        className="h-8 w-8"
                      >
                        <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}

        {/* Edit Template Dialog */}
        <Dialog
          open={!!editingTemplate}
          onOpenChange={() => setEditingTemplate(null)}
        >
          <DialogContent className="max-w-2xl w-[95vw] max-h-[90vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle>Edit Email Template</DialogTitle>
              <DialogDescription>
                Update your email template details.
              </DialogDescription>
            </DialogHeader>
            {editingTemplate && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Template Name *</Label>
                    <Input
                      value={editingTemplate.name}
                      onChange={(e) =>
                        setEditingTemplate((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Category</Label>
                    <Select
                      value={editingTemplate.template_category}
                      onValueChange={(value) =>
                        setEditingTemplate((prev) => ({
                          ...prev,
                          template_category: value,
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {templateCategories.map((category) => (
                          <SelectItem
                            key={category.value}
                            value={category.value}
                          >
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Subject Line *</Label>
                  <Input
                    value={editingTemplate.subject}
                    onChange={(e) =>
                      setEditingTemplate((prev) => ({
                        ...prev,
                        subject: e.target.value,
                      }))
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label>Email Content *</Label>
                  <Textarea
                    value={editingTemplate.content}
                    onChange={(e) =>
                      setEditingTemplate((prev) => ({
                        ...prev,
                        content: e.target.value,
                      }))
                    }
                    rows={8}
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button
                    onClick={handleUpdateTemplate}
                    disabled={updateTemplate.isPending}
                    className="w-full sm:w-auto"
                  >
                    {updateTemplate.isPending
                      ? "Updating..."
                      : "Update Template"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setEditingTemplate(null)}
                    className="w-full sm:w-auto"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
