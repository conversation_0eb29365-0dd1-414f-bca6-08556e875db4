import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PerformanceOptimizer } from "@/components/performance/PerformanceOptimizer";
import { Zap } from "lucide-react";

export function SettingsPerformance() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Performance Settings
          </CardTitle>
          <CardDescription>
            Optimize application performance and resource usage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PerformanceOptimizer />
        </CardContent>
      </Card>
    </div>
  );
}
