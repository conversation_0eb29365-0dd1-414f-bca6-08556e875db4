import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  useProfile,
  useUpdateProfile,
  useUploadAvatar,
} from "@/hooks/useProfiles";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2, User, Upload, Camera, Trash2 } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

export const SettingsProfile = () => {
  const { user } = useAuth();
  const { data: profile, isLoading: profileLoading } = useProfile();
  const updateProfile = useUpdateProfile();
  const uploadAvatar = useUploadAvatar();
  const { toast } = useToast();
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [isAvatarRemoved, setIsAvatarRemoved] = useState(false);

  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    company: "",
    role: "",
    avatar_url: "",
  });

  // Update form when profile loads
  useEffect(() => {
    if (profile) {
      setFormData({
        first_name: profile.first_name || "",
        last_name: profile.last_name || "",
        company: profile.company || "",
        role: profile.role || "",
        avatar_url: profile.avatar_url || "",
      });
      setIsAvatarRemoved(false);
    }
  }, [profile]);

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      console.log("Avatar file selected:", file.name, file.size, file.type);
      setAvatarFile(file);
      setIsAvatarRemoved(false);
      
      // Create a preview URL for immediate display
      const previewUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, avatar_url: previewUrl }));
    }
  };

  const handleDeleteAvatar = () => {
    setAvatarFile(null);
    setFormData(prev => ({ ...prev, avatar_url: "" }));
    setIsAvatarRemoved(true);
    toast({
      title: "Avatar marked for removal",
      description: "Click 'Save Changes' to permanently remove your avatar.",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const updatedFormData = { ...formData };

    // If company provided, provision company and link profile to it
    const companyToProvision = updatedFormData.company?.trim();
    if (user?.id && companyToProvision) {
      try {
        await supabase.rpc("provision_company_and_profile", {
          p_user_id: user.id,
          p_company_name: companyToProvision,
        });
      } catch (err) {
        console.error("Error provisioning company:", err);
        toast({
          title: "Error",
          description: "Could not create or join that company.",
          variant: "destructive",
        });
        // Do not proceed to update profile if provisioning failed
        return;
      }
    }

    // Upload avatar if a new one was selected
    if (avatarFile) {
      try {
        console.log("Uploading avatar file...");
        const avatarUrl = await uploadAvatar.mutateAsync(avatarFile);
        console.log("Avatar uploaded, URL:", avatarUrl);
        updatedFormData.avatar_url = avatarUrl;
      } catch (error) {
        console.error("Error uploading avatar:", error);
        // Don't update the profile if avatar upload fails
        return;
      }
    } else if (isAvatarRemoved) {
      // Explicitly set avatar_url to empty string for removal
      updatedFormData.avatar_url = "";
    }

    updateProfile.mutate(updatedFormData);
  };

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  if (profileLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Profile Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  const firstNameInitial = formData.first_name ? formData.first_name[0] : "";
  const lastNameInitial = formData.last_name ? formData.last_name[0] : "";
  const fullName =
    `${formData.first_name} ${formData.last_name}`.trim() || "User";

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="w-5 h-5" />
          Profile Settings
        </CardTitle>
        <CardDescription>
          Manage your personal information and account details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex flex-col items-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <Avatar className="h-24 w-24">
                <AvatarImage 
                  src={!isAvatarRemoved ? (formData.avatar_url || profile?.avatar_url || "") : ""} 
                  alt={fullName} 
                  onError={(e) => {
                    console.error("Avatar image failed to load:", e);
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <AvatarFallback>
                  {firstNameInitial}
                  {lastNameInitial}
                </AvatarFallback>
              </Avatar>
              <label
                htmlFor="avatar-upload"
                className="absolute bottom-0 right-0 p-1 bg-primary text-white rounded-full cursor-pointer hover:bg-primary/90"
              >
                <Camera className="h-4 w-4" />
                <input
                  id="avatar-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleAvatarChange}
                />
              </label>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium">Profile Picture</h3>
              <p className="text-sm text-muted-foreground mb-2">
                Upload a profile picture to personalize your account.
              </p>
              {avatarFile && (
                <div className="text-sm text-muted-foreground">
                  Selected: {avatarFile.name}
                </div>
              )}
              {((formData.avatar_url || profile?.avatar_url) && !isAvatarRemoved) && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleDeleteAvatar}
                  className="mt-2"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove Avatar
                </Button>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="first_name">First Name</Label>
              <Input
                id="first_name"
                value={formData.first_name}
                onChange={(e) => handleChange("first_name", e.target.value)}
                placeholder="Enter your first name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="last_name">Last Name</Label>
              <Input
                id="last_name"
                value={formData.last_name}
                onChange={(e) => handleChange("last_name", e.target.value)}
                placeholder="Enter your last name"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={user?.email || ""}
              disabled
              className="bg-muted"
            />
            <p className="text-xs text-muted-foreground">
              Email cannot be changed through this interface
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="company">Company</Label>
            <Input
              id="company"
              value={formData.company}
              onChange={(e) => handleChange("company", e.target.value)}
              placeholder="Enter your company name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Input
              id="role"
              value={formData.role}
              onChange={(e) => handleChange("role", e.target.value)}
              placeholder="Enter your role/position"
            />
          </div>

          <Button
            type="submit"
            disabled={updateProfile.isPending}
            className="w-full md:w-auto"
          >
            {updateProfile.isPending && (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            )}
            Save Changes
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
