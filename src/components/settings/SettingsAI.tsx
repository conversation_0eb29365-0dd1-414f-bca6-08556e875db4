import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { But<PERSON> } from "@/components/ui/button";
import { useAISettings } from "@/hooks/useAISettings";
import { Loader2, Brain } from "lucide-react";

export const SettingsAI = () => {
  const { settings, updateSetting, saveSettings, isLoading } = useAISettings();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="w-5 h-5" />
          AI Settings
        </CardTitle>
        <CardDescription>
          Configure AI-powered features for recruitment automation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="ai-matching">AI-Powered Matching</Label>
              <p className="text-sm text-muted-foreground">
                Use AI to automatically match candidates with job positions
              </p>
            </div>
            <Switch
              id="ai-matching"
              checked={settings.aiMatching}
              onCheckedChange={(checked) =>
                updateSetting("aiMatching", checked)
              }
            />
          </div>

          <div className="space-y-2">
            <Label>Matching Threshold: {settings.matchingThreshold}%</Label>
            <p className="text-sm text-muted-foreground">
              Minimum confidence score for AI recommendations
            </p>
            <Slider
              value={[settings.matchingThreshold]}
              onValueChange={(value) =>
                updateSetting("matchingThreshold", value[0])
              }
              max={100}
              step={1}
              className="w-full"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="ai-screening">Automated Screening</Label>
              <p className="text-sm text-muted-foreground">
                Automatically screen candidates based on job requirements
              </p>
            </div>
            <Switch
              id="ai-screening"
              checked={settings.aiScreening}
              onCheckedChange={(checked) =>
                updateSetting("aiScreening", checked)
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="ai-suggestions">AI Suggestions</Label>
              <p className="text-sm text-muted-foreground">
                Get AI-powered suggestions for improving job posts and candidate
                outreach
              </p>
            </div>
            <Switch
              id="ai-suggestions"
              checked={settings.aiSuggestions}
              onCheckedChange={(checked) =>
                updateSetting("aiSuggestions", checked)
              }
            />
          </div>
        </div>

        <Button
          onClick={saveSettings}
          disabled={isLoading}
          className="w-full md:w-auto"
        >
          {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          Save AI Settings
        </Button>
      </CardContent>
    </Card>
  );
};
