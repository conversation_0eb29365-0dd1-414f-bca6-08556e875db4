import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { CircleChevronDown, Loader2 } from "lucide-react";
import { useHiringTrends } from "@/hooks/useHiringTrends";
import { Skeleton } from "@/components/ui/skeleton";

export const HiringTrends = ({ expanded = false }: { expanded?: boolean }) => {
  const { data: hiringTrendsData = [], isLoading, error } = useHiringTrends();

  // Transform database data for chart
  const predictedHires = hiringTrendsData.map((trend) => ({
    name: trend.period,
    predicted: trend.predicted_hires,
  }));

  if (isLoading) {
    return (
      <div className={expanded ? "h-full" : ""}>
        <Card className={expanded ? "h-full border-0 shadow-none" : ""}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CircleChevronDown className="h-4 w-4" />
              <Skeleton className="h-6 w-48" />
            </CardTitle>
          </CardHeader>
          <CardContent
            className={expanded ? "h-[calc(100%-80px)]" : "h-[300px]"}
          >
            <Skeleton className="h-full w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || hiringTrendsData.length === 0) {
    return (
      <div className={expanded ? "h-full" : ""}>
        <Card className={expanded ? "h-full border-0 shadow-none" : ""}>
          <CardHeader>
            <CardTitle id="hiring-trends-title" className="flex items-center gap-2">
              <CircleChevronDown className="h-4 w-4" />
              Predicted Hiring Trends
            </CardTitle>
          </CardHeader>
          <CardContent
            className={expanded ? "h-[calc(100%-80px)]" : "h-[300px]"}
          >
            <div className="flex items-center justify-center h-full">
              <p className="text-muted-foreground">
                No hiring trends data available
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={expanded ? "h-full" : ""}>
      <Card className={expanded ? "h-full border-0 shadow-none" : ""}>
        <CardHeader>
          <CardTitle id="hiring-trends-title" className="flex items-center gap-2">
            <CircleChevronDown className="h-4 w-4" />
            Predicted Hiring Trends
          </CardTitle>
        </CardHeader>
        <CardContent className={expanded ? "h-[calc(100%-80px)]" : "h-[300px]"}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={predictedHires}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="predicted"
                stroke="#4f46e5"
                name="Predicted Hires"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};
