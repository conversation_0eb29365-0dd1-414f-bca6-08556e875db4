import { MetricsCards } from "./MetricsCards";
import { ApplicationsChart } from "./ApplicationsChart";
import { HiringTrends } from "./HiringTrends";
import { SkillGapAnalysis } from "./SkillGapAnalysis";
import { DiversityMetrics } from "./DiversityMetrics";
import { SourceEffectiveness } from "./SourceEffectiveness";
import { SalaryRecommendations } from "./SalaryRecommendations";
import { useState } from "react";
import { Maximize2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";

export const AnalyticsDashboard = () => {
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  const handleExpand = (cardId: string) => {
    setExpandedCard(cardId);
  };

  const handleClose = () => {
    setExpandedCard(null);
  };

  return (
    <div className="space-y-6">
      <MetricsCards />
      <ApplicationsChart />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="relative">
          <button
            onClick={() => handleExpand("hiring")}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <HiringTrends expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand("skills")}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SkillGapAnalysis expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand("diversity")}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <DiversityMetrics expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand("source")}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SourceEffectiveness expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand("salary")}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SalaryRecommendations expanded={false} />
        </div>
      </div>

      <Dialog open={expandedCard === "hiring"} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none" aria-labelledby="hiring-trends-title">
          <HiringTrends expanded={true} />
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === "skills"} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none" aria-labelledby="skill-gap-analysis-title">
          <SkillGapAnalysis expanded={true} />
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === "diversity"} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none" aria-labelledby="diversity-metrics-title">
          <DiversityMetrics expanded={true} />
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === "source"} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none" aria-labelledby="source-effectiveness-title">
          <SourceEffectiveness expanded={true} />
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === "salary"} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none" aria-labelledby="salary-recommendations-title">
          <SalaryRecommendations expanded={true} />
        </DialogContent>
      </Dialog>
    </div>
  );
};
