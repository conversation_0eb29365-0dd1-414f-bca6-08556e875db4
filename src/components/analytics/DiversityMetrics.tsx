import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { Target } from "lucide-react";
import { useAnalyticsDiversity } from "@/hooks/useAnalyticsDiversity";
import { Skeleton } from "@/components/ui/skeleton";

export const DiversityMetrics = ({
  expanded = false,
}: {
  expanded?: boolean;
}) => {
  const { data: diversityMetrics = [], isLoading } = useAnalyticsDiversity();

  // Group by category and subcategory, and ensure all possible subcategories are present
  // 1. Get all unique subcategories (e.g., Gender - Male, Ethnicity - Asian), but avoid 'undefined'
  const allSubcategories = Array.from(
    new Set(
      diversityMetrics.map((metric) =>
        metric.subcategory
          ? `${metric.category} - ${metric.subcategory}`
          : metric.category,
      ),
    ),
  );

  // 2. Build a lookup for quick access
  const metricMap = new Map(
    diversityMetrics.map((metric) => [
      metric.subcategory
        ? `${metric.category} - ${metric.subcategory}`
        : metric.category,
      metric,
    ]),
  );

  // 3. Build chart data, filling missing subcategories with value 0
  const chartData = allSubcategories.map((name) => {
    const metric = metricMap.get(name);
    return {
      name,
      value: metric?.value ?? 0,
      total: metric?.total ?? 0,
    };
  });

  if (isLoading) {
    return (
      <div className={expanded ? "h-full" : ""}>
        <Card className={expanded ? "h-full border-0 shadow-none" : ""}>
          <CardHeader className="flex flex-row items-center justify-between">
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent
            className={expanded ? "h-[calc(100%-80px)]" : "h-[300px]"}
          >
            <Skeleton className="h-full w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }
  return (
    <div className={expanded ? "h-full" : ""}>
      <Card className={expanded ? "h-full border-0 shadow-none" : ""}>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle id="diversity-metrics-title" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              DEI Progress Metrics
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className={expanded ? "h-[calc(100%-80px)]" : "h-[300px]"}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              layout="vertical"
              margin={{ top: 10, right: 30, left: 70, bottom: 10 }}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#f0f0f0"
                horizontal={false}
              />
              <XAxis
                type="number"
                domain={[0, 100]}
                axisLine={false}
                tickLine={false}
                style={{
                  fontSize: "12px",
                  fontFamily: "inherit",
                }}
              />
              <YAxis
                dataKey="name"
                type="category"
                axisLine={false}
                tickLine={false}
                style={{
                  fontSize: "12px",
                  fontFamily: "inherit",
                }}
              />
              <Tooltip
                cursor={{ fill: "rgba(236, 237, 254, 0.4)" }}
                contentStyle={{
                  backgroundColor: "rgba(255, 255, 255, 0.9)",
                  borderRadius: "8px",
                  border: "1px solid #e2e8f0",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
                }}
              />
              <Bar dataKey="value" name="Current %" radius={[0, 4, 4, 0]}>
                {chartData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={`url(#gradient-${index % 4})`}
                  />
                ))}
              </Bar>
              <defs>
                {[
                  ["#8B5CF6", "#D946EF"], // Purple to Pink
                  ["#6366F1", "#8B5CF6"], // Indigo to Purple
                  ["#3B82F6", "#6366F1"], // Blue to Indigo
                  ["#4F46E5", "#3B82F6"], // Indigo to Blue
                ].map((colors, index) => (
                  <linearGradient
                    key={`gradient-${index}`}
                    id={`gradient-${index}`}
                    x1="0"
                    y1="0"
                    x2="1"
                    y2="0"
                  >
                    <stop offset="0%" stopColor={colors[0]} stopOpacity={0.8} />
                    <stop
                      offset="100%"
                      stopColor={colors[1]}
                      stopOpacity={0.9}
                    />
                  </linearGradient>
                ))}
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};
