import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { getStageColor, getStageProgressColor } from "@/utils/stageUtils";
import { Users } from "lucide-react";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useProfile } from "@/hooks/useProfiles";
import { Skeleton } from "@/components/ui/skeleton";

interface PipelineStage {
  id?: string;
  stage: string;
  count: number;
  percentage: number;
}

export function HiringPipeline() {
  const { user } = useAuth();
  const { data: profile } = useProfile();
  const companyId = profile?.company_id || null;

  // Real-time hiring pipeline data subscription (subscribe to candidates so counts stay live)
  const { records: pipelineData = [], isLoading } = useRealtimeCollection(
    "pipeline_candidates",
    async (): Promise<PipelineStage[]> => {
      if (!user || !companyId) return [];

      try {
        // Fetch ordered stage definitions (for label, color, and ordering)
        const { data: stages, error: stagesError } = await supabase
          .from("pipeline_stages")
          .select("id, name, color, stage_order")
          .eq("user_id", user.id)
          .eq("company_id", companyId)
          .order("stage_order", { ascending: true });
        if (stagesError) throw stagesError;

        // Fetch candidates to compute live counts by stage
        const { data: candidates, error: candidatesError } = await supabase
          .from("pipeline_candidates")
          .select("stage")
          .eq("user_id", user.id)
          .eq("company_id", companyId);
        if (candidatesError) throw candidatesError;

        const stageCounts: Record<string, number> = {};
        candidates?.forEach((c) => {
          stageCounts[c.stage] = (stageCounts[c.stage] || 0) + 1;
        });

        const totalCandidates = candidates?.length || 0;

        // If no stage definitions, derive from counts (alphabetical)
        if (!stages || stages.length === 0) {
          return Object.entries(stageCounts)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([stage, count]) => ({
              stage,
              count,
              percentage:
                totalCandidates > 0
                  ? Math.round((count / totalCandidates) * 100)
                  : 0,
            }));
        }

        // Otherwise, follow defined stage order and append any unknown stages at the end
        const ordered = stages.map((s) => ({
          id: s.id,
          stage: s.name,
          count: stageCounts[s.name] || 0,
          percentage:
            totalCandidates > 0
              ? Math.round(((stageCounts[s.name] || 0) / totalCandidates) * 100)
              : 0,
        }));

        const knownNames = new Set(stages.map((s) => s.name));
        const extras = Object.entries(stageCounts)
          .filter(([name]) => !knownNames.has(name))
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([name, count]) => ({
            stage: name,
            count,
            percentage:
              totalCandidates > 0
                ? Math.round((count / totalCandidates) * 100)
                : 0,
          }));

        return [...ordered, ...extras];
      } catch (error) {
        console.error("Error fetching hiring pipeline data:", error);
        return [];
      }
    },
    "public",
    companyId ? `company_id=eq.${companyId}` : undefined,
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <Skeleton className="h-4 w-32" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-1">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          Hiring Pipeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pipelineData?.map((stage, index) => (
            <div key={index} className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{stage.stage}</span>
                <span className="text-sm text-muted-foreground">
                  {stage.count} candidates
                </span>
              </div>
              <Progress
                value={stage.percentage}
                className="h-2"
                indicatorClassName={getStageProgressColor(stage.stage)}
              />
              <span className="text-xs text-muted-foreground">
                {stage.percentage.toFixed(1)}% of pipeline
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
