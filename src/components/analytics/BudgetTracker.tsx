import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { DollarSign, Loader2, TrendingUp, AlertCircle, Plus } from "lucide-react";
import { useBudgetSummary } from "@/hooks/useBudget";
import { useEnhancedBudgetSummary, useBudgetUtilization } from "@/hooks/useBudgetAllocations";
import { useJobExpenseSummary } from "@/hooks/useJobExpenses";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AddExpenseDialog } from "./AddExpenseDialog";
import { useState } from "react";

export function BudgetTracker() {
  const [showEnhanced, setShowEnhanced] = useState(true);

  // Try enhanced budget system first, fallback to legacy
  const { data: enhancedSummary, isLoading: enhancedLoading } = useEnhancedBudgetSummary();
  const { data: budgetUtilization, isLoading: utilizationLoading } = useBudgetUtilization();
  const { data: jobExpenseSummary, isLoading: expenseLoading } = useJobExpenseSummary();
  const { data: legacyBudgetSummary, isLoading: legacyLoading, error } = useBudgetSummary();

  // Determine which data to use
  const hasEnhancedData = enhancedSummary && enhancedSummary.length > 0;
  const isLoading = hasEnhancedData ? (enhancedLoading || utilizationLoading) : legacyLoading;

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <Skeleton className="h-6 w-32" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-12" />
              </div>
              <Skeleton className="h-2 w-full" />
              <div className="flex justify-between mt-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>

            <div className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <div key={i}>
                  <div className="flex items-center justify-between mb-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-1 w-full" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || (!hasEnhancedData && !legacyBudgetSummary)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budget Tracker
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-muted-foreground">
              Failed to load budget data. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Prepare data based on available system
  let total_budget: number, spent_amount: number, categories: any[], remaining: number, percentageSpent: number;

  if (hasEnhancedData && budgetUtilization) {
    // Use enhanced budget system data
    total_budget = budgetUtilization.total_allocated;
    spent_amount = budgetUtilization.total_spent;
    percentageSpent = budgetUtilization.utilization_percentage;
    remaining = total_budget - spent_amount;

    // Convert enhanced data to category format for display
    categories = enhancedSummary!.map((allocation) => ({
      category: allocation.budget_name,
      amount: allocation.spent_amount,
      percentage: allocation.percentage_used,
      budget_amount: allocation.total_budget,
      remaining: allocation.remaining_amount,
    }));
  } else if (legacyBudgetSummary) {
    // Use legacy budget system data
    ({ total_budget, spent_amount, categories } = legacyBudgetSummary);
    percentageSpent = total_budget > 0 ? (spent_amount / total_budget) * 100 : 0;
    remaining = total_budget - spent_amount;
  } else {
    // No data available
    total_budget = 0;
    spent_amount = 0;
    categories = [];
    percentageSpent = 0;
    remaining = 0;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budget Tracker
            {hasEnhancedData && (
              <Badge variant="secondary" className="text-xs">
                Real-time
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {hasEnhancedData && <AddExpenseDialog />}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Overall Budget Summary */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Total Budget Usage</span>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">
                  {percentageSpent.toFixed(1)}%
                </span>
                {percentageSpent > 80 && (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                )}
              </div>
            </div>
            <Progress
              value={percentageSpent}
              className={`h-2 ${percentageSpent > 90 ? 'bg-red-100' : percentageSpent > 80 ? 'bg-orange-100' : ''}`}
            />
            <div className="flex justify-between mt-2 text-sm text-muted-foreground">
              <span>${spent_amount.toLocaleString()}</span>
              <span>${remaining.toLocaleString()} remaining</span>
            </div>
          </div>

          {/* Enhanced Budget Details */}
          {hasEnhancedData && budgetUtilization && (
            <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {budgetUtilization.active_allocations}
                </div>
                <div className="text-xs text-muted-foreground">Active Budgets</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  ${(total_budget - spent_amount).toLocaleString()}
                </div>
                <div className="text-xs text-muted-foreground">Available</div>
              </div>
            </div>
          )}

          {/* Category/Allocation Breakdown */}
          <div className="space-y-4">
            {categories.map((category, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{category.category}</span>
                    {hasEnhancedData && category.budget_amount && (
                      <Badge variant="outline" className="text-xs">
                        ${category.budget_amount.toLocaleString()} allocated
                      </Badge>
                    )}
                  </div>
                  <span className="text-sm font-medium">
                    ${category.amount.toLocaleString()}
                  </span>
                </div>
                <Progress value={category.percentage} className="h-1" />
                {hasEnhancedData && category.remaining !== undefined && (
                  <div className="text-xs text-muted-foreground text-right">
                    ${category.remaining.toLocaleString()} remaining
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Job Expense Summary (Enhanced Mode Only) */}
          {hasEnhancedData && jobExpenseSummary && jobExpenseSummary.length > 0 && (
            <div className="border-t pt-4">
              <div className="flex items-center gap-2 mb-3">
                <TrendingUp className="h-4 w-4" />
                <span className="text-sm font-medium">Top Spending Jobs</span>
              </div>
              <div className="space-y-2">
                {jobExpenseSummary.slice(0, 3).map((job, index) => (
                  <div key={job.job_id} className="flex items-center justify-between text-sm">
                    <span className="truncate flex-1">{job.job_title}</span>
                    <span className="font-medium">${job.total_expenses.toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* System Status */}
          <div className="text-xs text-muted-foreground text-center">
            {hasEnhancedData ? (
              <span className="flex items-center justify-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Real-time expense tracking active
              </span>
            ) : (
              <span>Using static budget data</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
