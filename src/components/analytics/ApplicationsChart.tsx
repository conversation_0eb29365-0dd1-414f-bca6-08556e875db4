import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useAnalyticsApplications } from "@/hooks/useAnalyticsApplications";
import { Skeleton } from "@/components/ui/skeleton";

const aiInsights = [
  "Application volume has increased by 25% compared to last quarter",
  "Best performing job categories: Engineering and Product Management",
  "Recommended action: Increase recruitment efforts in Marketing",
];

export const ApplicationsChart = () => {
  const { data: applicationData = [], isLoading } = useAnalyticsApplications();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <Skeleton className="h-6 w-64" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[300px] w-full" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Applications vs Interviews</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={applicationData}
                margin={{ top: 10, right: 30, left: 0, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  style={{
                    fontSize: "12px",
                    fontFamily: "inherit",
                  }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  style={{
                    fontSize: "12px",
                    fontFamily: "inherit",
                  }}
                />
                <Tooltip
                  cursor={{ fill: "rgba(236, 237, 254, 0.4)" }}
                  contentStyle={{
                    backgroundColor: "rgba(255, 255, 255, 0.9)",
                    borderRadius: "8px",
                    border: "1px solid #e2e8f0",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
                  }}
                />
                <defs>
                  {applicationData.map((_, index) => (
                    <linearGradient
                      key={`applicationGradient-${index}`}
                      id={`applicationGradient-${index}`}
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.8} />
                      <stop
                        offset="100%"
                        stopColor="#3b82f6"
                        stopOpacity={0.3}
                      />
                    </linearGradient>
                  ))}
                  {applicationData.map((_, index) => (
                    <linearGradient
                      key={`interviewGradient-${index}`}
                      id={`interviewGradient-${index}`}
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#10b981" stopOpacity={0.8} />
                      <stop
                        offset="100%"
                        stopColor="#10b981"
                        stopOpacity={0.3}
                      />
                    </linearGradient>
                  ))}
                </defs>
                <Bar
                  dataKey="applications"
                  name="Applications"
                  radius={[4, 4, 0, 0]}
                >
                  {applicationData.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#applicationGradient-${index})`}
                    />
                  ))}
                </Bar>
                <Bar
                  dataKey="interviews"
                  name="Interviews"
                  radius={[4, 4, 0, 0]}
                >
                  {applicationData.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#interviewGradient-${index})`}
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>AI Insights</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {aiInsights.map((insight, index) => (
            <Alert key={index}>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Insight {index + 1}</AlertTitle>
              <AlertDescription>{insight}</AlertDescription>
            </Alert>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};
