import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useDashboardFilters } from "@/contexts/DashboardFiltersContext";
import {
  BarChart3,
  Users,
  Briefcase,
  TrendingUp,
  Zap,
  Calendar,
  RotateCcw,
  Eye,
  EyeOff,
} from "lucide-react";

const componentConfig = [
  {
    key: "dashboardStats" as const,
    label: "Dashboard Stats",
    description: "Key metrics and statistics overview",
    icon: BarChart3,
  },
  {
    key: "recentCandidates" as const,
    label: "Recent Candidates",
    description: "Latest candidate applications",
    icon: Users,
  },
  {
    key: "recentJobs" as const,
    label: "Recent Jobs",
    description: "Active job postings",
    icon: Briefcase,
  },
  {
    key: "hiringPipeline" as const,
    label: "Hiring Pipeline",
    description: "Recruitment process overview",
    icon: TrendingUp,
  },
  {
    key: "quickActions" as const,
    label: "Quick Actions",
    description: "Frequently used actions",
    icon: Zap,
  },
  {
    key: "upcomingEvents" as const,
    label: "Upcoming Events",
    description: "Calendar events and interviews",
    icon: Calendar,
  },
];

export function DashboardFilters() {
  const { componentVisibility, toggleComponent, resetToDefaults } =
    useDashboardFilters();

  const visibleCount =
    Object.values(componentVisibility).filter(Boolean).length;
  const totalCount = Object.keys(componentVisibility).length;

  const toggleAll = () => {
    const allVisible = visibleCount === totalCount;
    const newVisibility = Object.keys(componentVisibility).reduce(
      (acc, key) => {
        acc[key as keyof typeof componentVisibility] = !allVisible;
        return acc;
      },
      {} as any,
    );

    // We'll need to update the context to support bulk updates
    Object.entries(newVisibility).forEach(([key, value]) => {
      if (
        componentVisibility[key as keyof typeof componentVisibility] !== value
      ) {
        toggleComponent(key as keyof typeof componentVisibility);
      }
    });
  };

  return (
    <div className="space-y-6 mt-6 max-h-[80vh] overflow-y-auto">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Eye className="h-5 w-5" />
            Component Visibility
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {visibleCount} of {totalCount} components visible
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleAll}
              className="flex-1"
            >
              {visibleCount === totalCount ? (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Hide All
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Show All
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetToDefaults}
              className="flex-1"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Component Toggles */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Dashboard Components</CardTitle>
          <p className="text-sm text-muted-foreground">
            Choose which components to display on your dashboard
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {componentConfig.map((component, index) => {
            const Icon = component.icon;
            const isVisible = componentVisibility[component.key];

            return (
              <div key={component.key}>
                <div className="flex items-center justify-between space-x-2">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <Label
                        htmlFor={component.key}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {component.label}
                      </Label>
                      <p className="text-xs text-muted-foreground truncate">
                        {component.description}
                      </p>
                    </div>
                  </div>
                  <Switch
                    id={component.key}
                    checked={isVisible}
                    onCheckedChange={() => toggleComponent(component.key)}
                  />
                </div>
                {index < componentConfig.length - 1 && (
                  <Separator className="mt-4" />
                )}
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Help Text */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-sm text-muted-foreground space-y-2">
            <p className="font-medium">💡 Tips:</p>
            <ul className="space-y-1 ml-4">
              <li>• Hide components you don't frequently use</li>
              <li>• Your preferences are automatically saved</li>
              <li>• Use "Reset" to restore default visibility</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
