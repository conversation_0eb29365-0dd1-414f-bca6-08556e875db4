import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, Briefcase, MapPin } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useJobs } from "@/hooks/useJobs";
import { useJobApplicantCounts } from "@/hooks/useJobApplicantCounts";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/contexts/AuthContext";

export function RecentJobs() {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Use unified jobs hook for real-time updates
  const { data: jobs = [], isLoading } = useJobs();
  
  // Get real applicant counts
  const jobIds = jobs?.map(job => job.id) || [];
  const { data: applicantCounts } = useJobApplicantCounts(jobIds);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Active Jobs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <div className="flex gap-2">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-20" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const activeJobs = jobs?.filter((job) => job.is_active).slice(0, 5) || [];

  return (
    <Card className="h-fit" data-testid="recent-jobs">
      <CardHeader className="flex flex-row items-center justify-between pb-3">
        <CardTitle className="flex items-center gap-1 sm:gap-2">
          <Briefcase className="h-4 w-4" />
          Active Jobs
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate("/jobs")}
          className="flex items-center gap-1 sm:gap-2"
        >
          View All
          <ArrowRight className="h-3 w-3" />
        </Button>
      </CardHeader>
      <CardContent className="pt-0">
        {activeJobs.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Briefcase className="h-12 w-12 text-muted-foreground/50 mb-3" />
            <p className="text-sm text-muted-foreground">
              No active jobs. Create your first job posting to get started!
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {activeJobs.map((job) => (
              <div
                key={job.id}
                className="cursor-pointer hover:bg-muted/50 p-2 rounded-lg transition-colors"
                onClick={() => navigate(`/jobs/${job.id}`)}
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium truncate">{job.title}</h4>
                  {job.is_urgent && (
                    <Badge
                      variant="destructive"
                      className="text-xs ml-2 shrink-0"
                    >
                      Urgent
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                  <span>{job.department_name || job.department}</span>
                  <span>•</span>
                  <div className="flex items-center gap-1.5">
                    <MapPin className="w-4 h-4" />
                    {job.location_name || job.location}
                  </div>
                </div>
                <div className="flex items-center gap-1.5">
                  <Briefcase className="w-4 h-4" />
                  {job.job_type_name || job.job_type}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
