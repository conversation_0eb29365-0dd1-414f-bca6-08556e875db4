import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Shield,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Loader2,
} from "lucide-react";
import { useSystemHealth } from "@/hooks/useSystemHealth";
import { useAuth } from "@/contexts/AuthContext";

export function SystemHealth() {
  const { user } = useAuth();
  const { data: systemStatuses = [], isLoading, error } = useSystemHealth();
  // Removed manual polling interval - real-time subscription handles updates automatically

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "operational":
        return CheckCircle;
      case "degraded":
        return AlertTriangle;
      case "down":
        return XCircle;
      default:
        return CheckCircle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "operational":
        return "text-green-600";
      case "degraded":
        return "text-yellow-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "operational":
        return "default";
      case "degraded":
        return "secondary";
      case "down":
        return "destructive";
      default:
        return "outline";
    }
  };

  const overallHealth = systemStatuses.reduce((acc, status) => {
    return (
      acc +
      (status.status === "operational"
        ? 25
        : status.status === "degraded"
          ? 15
          : 0)
    );
  }, 0);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Failed to load system health data
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Health Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Health</span>
            <span className="text-2xl font-bold text-green-600">
              {overallHealth}%
            </span>
          </div>
          <Progress value={overallHealth} className="h-3" />
        </div>

        {/* Individual System Statuses */}
        <div className="space-y-3">
          {systemStatuses.map((system) => {
            const StatusIcon = getStatusIcon(system.status);
            return (
              <div key={system.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <StatusIcon
                      className={`h-4 w-4 ${getStatusColor(system.status)}`}
                    />
                    <span className="text-sm font-medium">{system.name}</span>
                  </div>
                  <Badge variant={getStatusBadge(system.status) as any}>
                    {system.status}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                  <div>
                    <span className="block">Uptime</span>
                    <span className="font-medium text-foreground">
                      {system.uptime}%
                    </span>
                  </div>
                  <div>
                    <span className="block">Response</span>
                    <span className="font-medium text-foreground">
                      {Math.round(system.response_time)}ms
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* System Resources */}
        <div className="pt-4 border-t space-y-3">
          <h4 className="text-sm font-medium">Resource Usage</h4>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>CPU Usage</span>
              <span>23%</span>
            </div>
            <Progress value={23} className="h-2" />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Memory Usage</span>
              <span>67%</span>
            </div>
            <Progress value={67} className="h-2" />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Storage Usage</span>
              <span>34%</span>
            </div>
            <Progress value={34} className="h-2" />
          </div>
        </div>

        {/* Last Updated */}
        <div className="pt-2 text-xs text-muted-foreground">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  );
}
