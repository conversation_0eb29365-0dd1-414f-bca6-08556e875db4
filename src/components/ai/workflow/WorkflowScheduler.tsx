import { useState, useEffect, useCallback } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  useWorkflowSchedules,
  useCreateWorkflowSchedule,
  useUpdateWorkflowSchedule,
  useDeleteWorkflowSchedule,
} from "@/hooks/useWorkflowSchedules";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import {
  Calendar as CalendarIcon,
  Clock,
  Save,
  Trash2,
  Loader2,
  RefreshCw,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

interface WorkflowSchedule {
  id: string;
  workflow_id: string;
  cron_schedule: string;
  context: any;
  is_active: boolean;
  last_run: string | null;
  next_run: string | null;
  created_at: string;
  updated_at: string;
}

interface WorkflowSchedulerProps {
  workflowId: string;
  workflowName: string;
}

export function WorkflowScheduler({
  workflowId,
  workflowName,
}: WorkflowSchedulerProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const {
    data: schedules = [],
    isLoading: schedulesLoading,
    refetch: fetchSchedules,
  } = useWorkflowSchedules(workflowId);
  const createSchedule = useCreateWorkflowSchedule();
  const updateSchedule = useUpdateWorkflowSchedule();
  const deleteSchedule = useDeleteWorkflowSchedule();
  const [isSaving, setIsSaving] = useState(false);
  const [scheduleType, setScheduleType] = useState("daily");
  const [customCron, setCustomCron] = useState("0 9 * * *");
  const [isActive, setIsActive] = useState(true);
  const [contextData, setContextData] = useState("{}");
  const [editingScheduleId, setEditingScheduleId] = useState<string | null>(
    null,
  );

  const resetForm = useCallback(() => {
    setEditingScheduleId(null);
    setScheduleType("daily");
    setCustomCron("0 9 * * *");
    setIsActive(true);
    setContextData("{}");
  }, []);

  // Handle editing schedule form population
  useEffect(() => {
    if (editingScheduleId && schedules.length > 0) {
      const schedule = schedules.find((s) => s.id === editingScheduleId);
      if (schedule) {
        // Determine schedule type from cron expression
        let type = "custom";
        const cronSchedule = schedule.cron_schedule || "";
        if (cronSchedule === "0 * * * *") type = "hourly";
        else if (cronSchedule === "0 9 * * *") type = "daily";
        else if (cronSchedule === "0 9 * * 1") type = "weekly";
        else if (cronSchedule === "0 9 1 * *") type = "monthly";

        setScheduleType(type);
        setCustomCron(cronSchedule);
        setIsActive(schedule.is_active);
        setContextData(JSON.stringify(schedule.context || {}, null, 2));
      }
    }
  }, [editingScheduleId, schedules]);

  const getCronExpression = (type: string): string => {
    switch (type) {
      case "hourly":
        return "0 * * * *"; // Every hour
      case "daily":
        return "0 9 * * *"; // Every day at 9 AM
      case "weekly":
        return "0 9 * * 1"; // Every Monday at 9 AM
      case "monthly":
        return "0 9 1 * *"; // First day of month at 9 AM
      case "custom":
        return customCron;
      default:
        return "0 9 * * *"; // Default to daily at 9 AM
    }
  };

  const handleScheduleTypeChange = (value: string) => {
    setScheduleType(value);
    if (value !== "custom") {
      setCustomCron(getCronExpression(value));
    }
  };

  const handleSaveSchedule = async () => {
    setIsSaving(true);
    try {
      // Validate context data
      let parsedContext = {};
      try {
        parsedContext = JSON.parse(contextData);
      } catch (error) {
        toast({
          title: "Invalid Context Data",
          description: "Please enter valid JSON for context data",
          variant: "destructive",
        });
        setIsSaving(false);
        return;
      }

      // Get cron expression
      const cronExpression = getCronExpression(scheduleType);

      if (editingScheduleId) {
        // Update existing schedule
        await updateSchedule.mutateAsync({
          id: editingScheduleId,
          cron_schedule: cronExpression,
          context: parsedContext,
          is_active: isActive,
        });
      } else {
        // Create new schedule
        await createSchedule.mutateAsync({
          workflow_id: workflowId,
          cron_schedule: cronExpression,
          context: parsedContext,
          is_active: isActive,
        });
      }

      // Reset form and refresh schedules
      resetForm();
      fetchSchedules();
    } catch (error) {
      console.error("Error saving workflow schedule:", error);
      toast({
        title: "Error",
        description: "Failed to save workflow schedule",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleEditSchedule = (schedule: WorkflowSchedule) => {
    setEditingScheduleId(schedule.id);
  };

  const handleDeleteSchedule = async (scheduleId: string) => {
    if (!scheduleId) return;

    try {
      await deleteSchedule.mutateAsync(scheduleId);
      fetchSchedules();
    } catch (error) {
      console.error("Error deleting workflow schedule:", error);
      toast({
        title: "Error",
        description: "Failed to delete workflow schedule",
        variant: "destructive",
      });
    }
  };

  const formatNextRun = (nextRun: string | null) => {
    if (!nextRun) return "Not scheduled";
    return new Date(nextRun).toLocaleString();
  };

  const describeCronSchedule = (cronExpression: string): string => {
    // This is a simplified description - a real implementation would be more comprehensive
    if (cronExpression === "0 * * * *") return "Every hour";
    if (cronExpression === "0 9 * * *") return "Daily at 9:00 AM";
    if (cronExpression === "0 9 * * 1") return "Weekly on Monday at 9:00 AM";
    if (cronExpression === "0 9 1 * *") return "Monthly on the 1st at 9:00 AM";
    return "Custom schedule";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule Workflow: {workflowName}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Schedule Type</Label>
            <Select
              value={scheduleType}
              onValueChange={handleScheduleTypeChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select schedule type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hourly">Hourly</SelectItem>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {scheduleType === "custom" && (
            <div className="space-y-2">
              <Label htmlFor="cron-expression">Cron Expression</Label>
              <Input
                id="cron-expression"
                value={customCron}
                onChange={(e) => setCustomCron(e.target.value)}
                placeholder="0 9 * * *"
              />
              <p className="text-xs text-muted-foreground">
                Enter a valid cron expression (e.g., "0 9 * * *" for daily at 9
                AM)
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="context-data">Context Data (JSON)</Label>
            <Textarea
              id="context-data"
              value={contextData}
              onChange={(e) => setContextData(e.target.value)}
              placeholder='{"candidateId": "123", "jobId": "456"}'
              rows={5}
            />
            <p className="text-xs text-muted-foreground">
              Enter JSON data to provide context for the workflow execution
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="is-active">Active</Label>
              <p className="text-sm text-muted-foreground">
                Enable or disable this schedule
              </p>
            </div>
            <Switch
              id="is-active"
              checked={isActive}
              onCheckedChange={setIsActive}
            />
          </div>

          <div className="flex justify-between">
            <Button variant="outline" onClick={resetForm} disabled={isSaving}>
              Cancel
            </Button>
            <Button onClick={handleSaveSchedule} disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {editingScheduleId ? "Update Schedule" : "Save Schedule"}
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Existing Schedules</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchSchedules()}
              disabled={schedulesLoading}
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${schedulesLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>

          {schedulesLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : schedules.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No schedules found. Create your first schedule above.
            </div>
          ) : (
            <ScrollArea className="h-[300px]">
              <div className="space-y-4">
                {schedules.map((schedule) => (
                  <div key={schedule.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-4 w-4 text-primary" />
                        <span className="font-medium">
                          {describeCronSchedule(schedule.cron_schedule)}
                        </span>
                        {schedule.is_active ? (
                          <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700"
                          >
                            Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="bg-gray-50 text-gray-700"
                          >
                            Inactive
                          </Badge>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditSchedule(schedule)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteSchedule(schedule.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>
                          Next run: {formatNextRun(schedule.next_run)}
                        </span>
                      </div>
                      {schedule.last_run && (
                        <div className="text-muted-foreground">
                          Last run:{" "}
                          {new Date(schedule.last_run).toLocaleString()}
                        </div>
                      )}
                    </div>

                    {schedule.context &&
                      Object.keys(schedule.context).length > 0 && (
                        <div className="mt-2 p-2 bg-muted rounded-md text-xs font-mono">
                          {JSON.stringify(schedule.context, null, 2)}
                        </div>
                      )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
