import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type {
  WorkflowConfigProps,
  LinkedInIntegrationConfig,
  JobBoardIntegrationConfig,
  ATSIntegrationConfig,
  CalendarIntegrationConfig,
} from "@/types/workflow";

export function LinkedInIntegrationConfig({
  config,
  setConfig,
}: WorkflowConfigProps<LinkedInIntegrationConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Action Type</Label>
        <Select
          value={config.actionType}
          onValueChange={(value: LinkedInIntegrationConfig["actionType"]) =>
            setConfig({ ...config, actionType: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select action type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="post-job">Post Job</SelectItem>
            <SelectItem value="search-candidates">Search Candidates</SelectItem>
            <SelectItem value="send-message">Send Message</SelectItem>
            <SelectItem value="get-profile">Get Profile</SelectItem>
            <SelectItem value="company-update">Company Update</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.actionType === "post-job" && (
        <>
          <div className="space-y-2">
            <Label>Job Title</Label>
            <Input
              placeholder="Enter job title"
              value={config.jobTitle}
              onChange={(e) =>
                setConfig({ ...config, jobTitle: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Job Description</Label>
            <Textarea
              placeholder="Enter job description"
              value={config.jobDescription}
              onChange={(e) =>
                setConfig({ ...config, jobDescription: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Location</Label>
            <Input
              placeholder="Enter job location"
              value={config.location}
              onChange={(e) =>
                setConfig({ ...config, location: e.target.value })
              }
            />
          </div>
        </>
      )}

      {config.actionType === "search-candidates" && (
        <>
          <div className="space-y-2">
            <Label>Search Keywords</Label>
            <Input
              placeholder="Enter search keywords"
              value={config.searchKeywords}
              onChange={(e) =>
                setConfig({ ...config, searchKeywords: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Location</Label>
            <Input
              placeholder="Enter location"
              value={config.location}
              onChange={(e) =>
                setConfig({ ...config, location: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Maximum Results</Label>
            <Input
              type="number"
              min="1"
              max="100"
              value={config.maxResults}
              onChange={(e) =>
                setConfig({ ...config, maxResults: e.target.value })
              }
            />
          </div>
        </>
      )}

      {config.actionType === "send-message" && (
        <>
          <div className="space-y-2">
            <Label>Recipient</Label>
            <Input
              placeholder="Enter recipient LinkedIn ID or variable"
              value={config.recipient}
              onChange={(e) =>
                setConfig({ ...config, recipient: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Message</Label>
            <Textarea
              placeholder="Enter message content"
              value={config.message}
              onChange={(e) =>
                setConfig({ ...config, message: e.target.value })
              }
            />
          </div>
        </>
      )}

      {config.actionType === "get-profile" && (
        <div className="space-y-2">
          <Label>Profile ID</Label>
          <Input
            placeholder="Enter LinkedIn profile ID or variable"
            value={config.profileId}
            onChange={(e) =>
              setConfig({ ...config, profileId: e.target.value })
            }
          />
        </div>
      )}

      <div className="flex items-center justify-between">
        <Label>Use Company Page</Label>
        <Switch
          checked={config.useCompanyPage}
          onCheckedChange={(checked) =>
            setConfig({ ...config, useCompanyPage: checked })
          }
        />
      </div>

      <div className="flex items-center justify-between">
        <Label>Track Analytics</Label>
        <Switch
          checked={config.trackAnalytics}
          onCheckedChange={(checked) =>
            setConfig({ ...config, trackAnalytics: checked })
          }
        />
      </div>
    </>
  );
}

export function JobBoardIntegrationConfig({
  config,
  setConfig,
}: WorkflowConfigProps<JobBoardIntegrationConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Job Board</Label>
        <Select
          value={config.jobBoard}
          onValueChange={(value: JobBoardIntegrationConfig["jobBoard"]) =>
            setConfig({ ...config, jobBoard: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select job board" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="indeed">Indeed</SelectItem>
            <SelectItem value="glassdoor">Glassdoor</SelectItem>
            <SelectItem value="monster">Monster</SelectItem>
            <SelectItem value="ziprecruiter">ZipRecruiter</SelectItem>
            <SelectItem value="dice">Dice</SelectItem>
            <SelectItem value="custom">Custom Job Board</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.jobBoard === "custom" && (
        <div className="space-y-2">
          <Label>Custom Job Board</Label>
          <Input
            placeholder="Enter job board name"
            value={config.customJobBoard}
            onChange={(e) =>
              setConfig({ ...config, customJobBoard: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>Action Type</Label>
        <Select
          value={config.actionType}
          onValueChange={(value: JobBoardIntegrationConfig["actionType"]) =>
            setConfig({ ...config, actionType: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select action type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="post-job">Post Job</SelectItem>
            <SelectItem value="update-job">Update Job</SelectItem>
            <SelectItem value="close-job">Close Job</SelectItem>
            <SelectItem value="get-applications">Get Applications</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {(config.actionType === "post-job" ||
        config.actionType === "update-job") && (
        <>
          <div className="space-y-2">
            <Label>Job ID</Label>
            <Input
              placeholder="Enter job ID or variable"
              value={config.jobId}
              onChange={(e) => setConfig({ ...config, jobId: e.target.value })}
            />
            <p className="text-xs text-muted-foreground">
              Required for updating jobs, optional for posting new jobs
            </p>
          </div>
          <div className="space-y-2">
            <Label>Job Data Source</Label>
            <Select
              value={config.jobDataSource}
              onValueChange={(
                value: JobBoardIntegrationConfig["jobDataSource"],
              ) => setConfig({ ...config, jobDataSource: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select job data source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="context">From Workflow Context</SelectItem>
                <SelectItem value="database">From Database</SelectItem>
                <SelectItem value="custom">Custom Data</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {config.jobDataSource === "custom" && (
            <div className="space-y-2">
              <Label>Custom Job Data</Label>
              <Textarea
                placeholder="Enter job data (JSON format)"
                value={config.customJobData}
                onChange={(e) =>
                  setConfig({ ...config, customJobData: e.target.value })
                }
              />
            </div>
          )}
        </>
      )}

      {config.actionType === "close-job" && (
        <div className="space-y-2">
          <Label>Job ID</Label>
          <Input
            placeholder="Enter job ID or variable"
            value={config.jobId}
            onChange={(e) => setConfig({ ...config, jobId: e.target.value })}
          />
        </div>
      )}

      {config.actionType === "get-applications" && (
        <>
          <div className="space-y-2">
            <Label>Job ID</Label>
            <Input
              placeholder="Enter job ID or variable"
              value={config.jobId}
              onChange={(e) => setConfig({ ...config, jobId: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>Maximum Results</Label>
            <Input
              type="number"
              min="1"
              max="1000"
              value={config.maxResults}
              onChange={(e) =>
                setConfig({ ...config, maxResults: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Filter Status</Label>
            <Select
              value={config.filterStatus}
              onValueChange={(
                value: JobBoardIntegrationConfig["filterStatus"],
              ) => setConfig({ ...config, filterStatus: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Applications</SelectItem>
                <SelectItem value="new">New Applications</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="contacted">Contacted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}

      <div className="flex items-center justify-between">
        <Label>Sponsored Listing</Label>
        <Switch
          checked={config.sponsoredListing}
          onCheckedChange={(checked) =>
            setConfig({ ...config, sponsoredListing: checked })
          }
        />
      </div>

      <div className="flex items-center justify-between">
        <Label>Auto-Import Responses</Label>
        <Switch
          checked={config.autoImportResponses}
          onCheckedChange={(checked) =>
            setConfig({ ...config, autoImportResponses: checked })
          }
        />
      </div>
    </>
  );
}

export function ATSIntegrationConfig({
  config,
  setConfig,
}: WorkflowConfigProps<ATSIntegrationConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>ATS Provider</Label>
        <Select
          value={config.atsProvider}
          onValueChange={(value: ATSIntegrationConfig["atsProvider"]) =>
            setConfig({ ...config, atsProvider: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select ATS provider" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="greenhouse">Greenhouse</SelectItem>
            <SelectItem value="lever">Lever</SelectItem>
            <SelectItem value="workday">Workday</SelectItem>
            <SelectItem value="bamboohr">BambooHR</SelectItem>
            <SelectItem value="workable">Workable</SelectItem>
            <SelectItem value="custom">Custom ATS</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.atsProvider === "custom" && (
        <div className="space-y-2">
          <Label>Custom ATS Name</Label>
          <Input
            placeholder="Enter ATS name"
            value={config.customAtsName}
            onChange={(e) =>
              setConfig({ ...config, customAtsName: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>Action Type</Label>
        <Select
          value={config.actionType}
          onValueChange={(value: ATSIntegrationConfig["actionType"]) =>
            setConfig({ ...config, actionType: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select action type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="create-candidate">Create Candidate</SelectItem>
            <SelectItem value="update-candidate">Update Candidate</SelectItem>
            <SelectItem value="add-note">Add Note</SelectItem>
            <SelectItem value="schedule-interview">
              Schedule Interview
            </SelectItem>
            <SelectItem value="update-status">Update Status</SelectItem>
            <SelectItem value="get-candidate">Get Candidate</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {(config.actionType === "create-candidate" ||
        config.actionType === "update-candidate") && (
        <>
          <div className="space-y-2">
            <Label>Candidate ID</Label>
            <Input
              placeholder="Enter candidate ID or variable"
              value={config.candidateId}
              onChange={(e) =>
                setConfig({ ...config, candidateId: e.target.value })
              }
            />
            <p className="text-xs text-muted-foreground">
              Required for updating candidates, optional for creating new
              candidates
            </p>
          </div>
          <div className="space-y-2">
            <Label>Candidate Data Source</Label>
            <Select
              value={config.candidateDataSource}
              onValueChange={(
                value: ATSIntegrationConfig["candidateDataSource"],
              ) => setConfig({ ...config, candidateDataSource: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select data source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="context">From Workflow Context</SelectItem>
                <SelectItem value="database">From Database</SelectItem>
                <SelectItem value="custom">Custom Data</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {config.candidateDataSource === "custom" && (
            <div className="space-y-2">
              <Label>Custom Candidate Data</Label>
              <Textarea
                placeholder="Enter candidate data (JSON format)"
                value={config.customCandidateData}
                onChange={(e) =>
                  setConfig({ ...config, customCandidateData: e.target.value })
                }
              />
            </div>
          )}
        </>
      )}

      {config.actionType === "add-note" && (
        <>
          <div className="space-y-2">
            <Label>Candidate ID</Label>
            <Input
              placeholder="Enter candidate ID or variable"
              value={config.candidateId}
              onChange={(e) =>
                setConfig({ ...config, candidateId: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Note Content</Label>
            <Textarea
              placeholder="Enter note content"
              value={config.noteContent}
              onChange={(e) =>
                setConfig({ ...config, noteContent: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Note Visibility</Label>
            <Select
              value={config.noteVisibility}
              onValueChange={(value: ATSIntegrationConfig["noteVisibility"]) =>
                setConfig({ ...config, noteVisibility: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select note visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="private">Private</SelectItem>
                <SelectItem value="team">Team Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}

      {config.actionType === "schedule-interview" && (
        <>
          <div className="space-y-2">
            <Label>Candidate ID</Label>
            <Input
              placeholder="Enter candidate ID or variable"
              value={config.candidateId}
              onChange={(e) =>
                setConfig({ ...config, candidateId: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Interview Type</Label>
            <Select
              value={config.interviewType}
              onValueChange={(value: ATSIntegrationConfig["interviewType"]) =>
                setConfig({ ...config, interviewType: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select interview type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="phone">Phone Screen</SelectItem>
                <SelectItem value="technical">Technical</SelectItem>
                <SelectItem value="behavioral">Behavioral</SelectItem>
                <SelectItem value="onsite">Onsite</SelectItem>
                <SelectItem value="final">Final Round</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Interview Date</Label>
            <Input
              type="date"
              value={config.interviewDate}
              onChange={(e) =>
                setConfig({ ...config, interviewDate: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Interview Time</Label>
            <Input
              type="time"
              value={config.interviewTime}
              onChange={(e) =>
                setConfig({ ...config, interviewTime: e.target.value })
              }
            />
          </div>
        </>
      )}

      {config.actionType === "update-status" && (
        <>
          <div className="space-y-2">
            <Label>Candidate ID</Label>
            <Input
              placeholder="Enter candidate ID or variable"
              value={config.candidateId}
              onChange={(e) =>
                setConfig({ ...config, candidateId: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>New Status</Label>
            <Select
              value={config.newStatus}
              onValueChange={(value: ATSIntegrationConfig["newStatus"]) =>
                setConfig({ ...config, newStatus: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="applied">Applied</SelectItem>
                <SelectItem value="screening">Screening</SelectItem>
                <SelectItem value="interview">Interview</SelectItem>
                <SelectItem value="offer">Offer</SelectItem>
                <SelectItem value="hired">Hired</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="custom">Custom Status</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {config.newStatus === "custom" && (
            <div className="space-y-2">
              <Label>Custom Status</Label>
              <Input
                placeholder="Enter custom status"
                value={config.customStatus}
                onChange={(e) =>
                  setConfig({ ...config, customStatus: e.target.value })
                }
              />
            </div>
          )}
        </>
      )}

      {config.actionType === "get-candidate" && (
        <div className="space-y-2">
          <Label>Candidate ID</Label>
          <Input
            placeholder="Enter candidate ID or variable"
            value={config.candidateId}
            onChange={(e) =>
              setConfig({ ...config, candidateId: e.target.value })
            }
          />
        </div>
      )}

      <div className="flex items-center justify-between">
        <Label>Sync Bidirectionally</Label>
        <Switch
          checked={config.syncBidirectional}
          onCheckedChange={(checked) =>
            setConfig({ ...config, syncBidirectional: checked })
          }
        />
      </div>
    </>
  );
}

export function CalendarIntegrationConfig({
  config,
  setConfig,
}: WorkflowConfigProps<CalendarIntegrationConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Calendar Provider</Label>
        <Select
          value={config.calendarProvider}
          onValueChange={(
            value: CalendarIntegrationConfig["calendarProvider"],
          ) => setConfig({ ...config, calendarProvider: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select calendar provider" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="google">Google Calendar</SelectItem>
            <SelectItem value="outlook">Microsoft Outlook</SelectItem>
            <SelectItem value="apple">Apple Calendar</SelectItem>
            <SelectItem value="zoom">Zoom</SelectItem>
            <SelectItem value="custom">Custom Provider</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.calendarProvider === "custom" && (
        <div className="space-y-2">
          <Label>Custom Provider</Label>
          <Input
            placeholder="Enter provider name"
            value={config.customProvider}
            onChange={(e) =>
              setConfig({ ...config, customProvider: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>Action Type</Label>
        <Select
          value={config.actionType}
          onValueChange={(value: CalendarIntegrationConfig["actionType"]) =>
            setConfig({ ...config, actionType: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select action type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="create-event">Create Event</SelectItem>
            <SelectItem value="update-event">Update Event</SelectItem>
            <SelectItem value="delete-event">Delete Event</SelectItem>
            <SelectItem value="get-availability">Get Availability</SelectItem>
            <SelectItem value="find-meeting-time">Find Meeting Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {(config.actionType === "create-event" ||
        config.actionType === "update-event") && (
        <>
          <div className="space-y-2">
            <Label>Event ID</Label>
            <Input
              placeholder="Enter event ID or variable"
              value={config.eventId}
              onChange={(e) =>
                setConfig({ ...config, eventId: e.target.value })
              }
            />
            <p className="text-xs text-muted-foreground">
              Required for updating events, optional for creating new events
            </p>
          </div>
          <div className="space-y-2">
            <Label>Event Title</Label>
            <Input
              placeholder="Enter event title"
              value={config.eventTitle}
              onChange={(e) =>
                setConfig({ ...config, eventTitle: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Start Date/Time</Label>
            <Input
              type="datetime-local"
              value={config.startDateTime}
              onChange={(e) =>
                setConfig({ ...config, startDateTime: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>End Date/Time</Label>
            <Input
              type="datetime-local"
              value={config.endDateTime}
              onChange={(e) =>
                setConfig({ ...config, endDateTime: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Attendees</Label>
            <Textarea
              placeholder="Enter attendee emails (one per line)"
              value={config.attendees}
              onChange={(e) =>
                setConfig({ ...config, attendees: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Location</Label>
            <Input
              placeholder="Enter location or meeting link"
              value={config.location}
              onChange={(e) =>
                setConfig({ ...config, location: e.target.value })
              }
            />
          </div>
        </>
      )}

      {config.actionType === "delete-event" && (
        <div className="space-y-2">
          <Label>Event ID</Label>
          <Input
            placeholder="Enter event ID or variable"
            value={config.eventId}
            onChange={(e) => setConfig({ ...config, eventId: e.target.value })}
          />
        </div>
      )}

      {config.actionType === "get-availability" && (
        <>
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Input
              type="date"
              value={config.startDate}
              onChange={(e) =>
                setConfig({ ...config, startDate: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>End Date</Label>
            <Input
              type="date"
              value={config.endDate}
              onChange={(e) =>
                setConfig({ ...config, endDate: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Calendar IDs</Label>
            <Input
              placeholder="Enter calendar IDs (comma-separated)"
              value={config.calendarIds}
              onChange={(e) =>
                setConfig({ ...config, calendarIds: e.target.value })
              }
            />
          </div>
        </>
      )}

      {config.actionType === "find-meeting-time" && (
        <>
          <div className="space-y-2">
            <Label>Attendees</Label>
            <Textarea
              placeholder="Enter attendee emails (one per line)"
              value={config.attendees}
              onChange={(e) =>
                setConfig({ ...config, attendees: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Duration (minutes)</Label>
            <Input
              type="number"
              min="15"
              max="180"
              step="15"
              value={config.duration}
              onChange={(e) =>
                setConfig({ ...config, duration: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Date Range</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="date"
                value={config.startDate}
                onChange={(e) =>
                  setConfig({ ...config, startDate: e.target.value })
                }
              />
              <Input
                type="date"
                value={config.endDate}
                onChange={(e) =>
                  setConfig({ ...config, endDate: e.target.value })
                }
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label>Working Hours</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="time"
                value={config.workingHoursStart}
                onChange={(e) =>
                  setConfig({ ...config, workingHoursStart: e.target.value })
                }
              />
              <Input
                type="time"
                value={config.workingHoursEnd}
                onChange={(e) =>
                  setConfig({ ...config, workingHoursEnd: e.target.value })
                }
              />
            </div>
          </div>
        </>
      )}

      <div className="flex items-center justify-between">
        <Label>Send Notifications</Label>
        <Switch
          checked={config.sendNotifications}
          onCheckedChange={(checked) =>
            setConfig({ ...config, sendNotifications: checked })
          }
        />
      </div>
    </>
  );
}
