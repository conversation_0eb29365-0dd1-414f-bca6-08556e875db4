import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import type {
  WorkflowConfigProps,
  NewApplicationTriggerConfig,
  ApplicationStatusTriggerConfig,
  ScheduledTriggerConfig,
  DocumentUploadTriggerConfig,
  WebhookTriggerConfig,
  DatabaseTriggerConfig,
} from "@/types/workflow";

export function NewApplicationConfig({
  config,
  setConfig,
}: WorkflowConfigProps<NewApplicationTriggerConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Job Positions</Label>
        <Select
          value={config.jobTypes}
          onValueChange={(value: NewApplicationTriggerConfig["jobTypes"]) =>
            setConfig({ ...config, jobTypes: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select positions to monitor" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Positions</SelectItem>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="design">Design</SelectItem>
            <SelectItem value="marketing">Marketing</SelectItem>
            <SelectItem value="sales">Sales</SelectItem>
            <SelectItem value="product">Product</SelectItem>
            <SelectItem value="management">Management</SelectItem>
            <SelectItem value="custom">Custom Filter</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.jobTypes === "custom" && (
        <div className="space-y-2">
          <Label>Custom Job Filter</Label>
          <Input
            placeholder="Enter job titles (comma-separated)"
            value={config.customJobFilter}
            onChange={(e) =>
              setConfig({ ...config, customJobFilter: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>Source Filter</Label>
        <Select
          value={config.sourceFilter}
          onValueChange={(value: NewApplicationTriggerConfig["sourceFilter"]) =>
            setConfig({ ...config, sourceFilter: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select application source" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Sources</SelectItem>
            <SelectItem value="website">Company Website</SelectItem>
            <SelectItem value="linkedin">LinkedIn</SelectItem>
            <SelectItem value="indeed">Indeed</SelectItem>
            <SelectItem value="referral">Referrals</SelectItem>
            <SelectItem value="custom">Custom Source</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.sourceFilter === "custom" && (
        <div className="space-y-2">
          <Label>Custom Source</Label>
          <Input
            placeholder="Enter source name"
            value={config.customSource}
            onChange={(e) =>
              setConfig({ ...config, customSource: e.target.value })
            }
          />
        </div>
      )}

      <div className="flex items-center justify-between">
        <Label htmlFor="notify-team">Notify Team</Label>
        <Switch
          id="notify-team"
          checked={config.notifyTeam}
          onCheckedChange={(checked) =>
            setConfig({ ...config, notifyTeam: checked })
          }
        />
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="auto-screen">Auto-Screen Candidates</Label>
        <Switch
          id="auto-screen"
          checked={config.autoScreen}
          onCheckedChange={(checked) =>
            setConfig({ ...config, autoScreen: checked })
          }
        />
      </div>
    </>
  );
}

export function ApplicationStatusConfig({
  config,
  setConfig,
}: WorkflowConfigProps<ApplicationStatusTriggerConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Status Changes</Label>
        <Select
          value={config.statusType}
          onValueChange={(
            value: ApplicationStatusTriggerConfig["statusType"],
          ) => setConfig({ ...config, statusType: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select status change type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any">Any Change</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="pending">Pending Review</SelectItem>
            <SelectItem value="interview">Interview Scheduled</SelectItem>
            <SelectItem value="offer">Offer Extended</SelectItem>
            <SelectItem value="hired">Hired</SelectItem>
            <SelectItem value="custom">Custom Status</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.statusType === "custom" && (
        <div className="space-y-2">
          <Label>Custom Status</Label>
          <Input
            placeholder="Enter custom status"
            value={config.customStatus}
            onChange={(e) =>
              setConfig({ ...config, customStatus: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>Job Filter</Label>
        <Select
          value={config.jobFilter}
          onValueChange={(value: ApplicationStatusTriggerConfig["jobFilter"]) =>
            setConfig({ ...config, jobFilter: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select job filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Jobs</SelectItem>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="design">Design</SelectItem>
            <SelectItem value="marketing">Marketing</SelectItem>
            <SelectItem value="sales">Sales</SelectItem>
            <SelectItem value="product">Product</SelectItem>
            <SelectItem value="custom">Custom Filter</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.jobFilter === "custom" && (
        <div className="space-y-2">
          <Label>Custom Job Filter</Label>
          <Input
            placeholder="Enter job titles (comma-separated)"
            value={config.customJobFilter}
            onChange={(e) =>
              setConfig({ ...config, customJobFilter: e.target.value })
            }
          />
        </div>
      )}

      <div className="flex items-center justify-between">
        <Label htmlFor="include-internal">
          Include Internal Status Changes
        </Label>
        <Switch
          id="include-internal"
          checked={config.includeInternal}
          onCheckedChange={(checked) =>
            setConfig({ ...config, includeInternal: checked })
          }
        />
      </div>
    </>
  );
}

export function ScheduledTriggerConfig({
  config,
  setConfig,
}: WorkflowConfigProps<ScheduledTriggerConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Schedule Type</Label>
        <Select
          value={config.scheduleType}
          onValueChange={(value: ScheduledTriggerConfig["scheduleType"]) =>
            setConfig({ ...config, scheduleType: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select schedule type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="daily">Daily</SelectItem>
            <SelectItem value="weekly">Weekly</SelectItem>
            <SelectItem value="monthly">Monthly</SelectItem>
            <SelectItem value="custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.scheduleType === "weekly" && (
        <div className="space-y-2">
          <Label>Day of Week</Label>
          <Select
            value={config.dayOfWeek}
            onValueChange={(value) =>
              setConfig({ ...config, dayOfWeek: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select day of week" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">Monday</SelectItem>
              <SelectItem value="2">Tuesday</SelectItem>
              <SelectItem value="3">Wednesday</SelectItem>
              <SelectItem value="4">Thursday</SelectItem>
              <SelectItem value="5">Friday</SelectItem>
              <SelectItem value="6">Saturday</SelectItem>
              <SelectItem value="0">Sunday</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {config.scheduleType === "monthly" && (
        <div className="space-y-2">
          <Label>Day of Month</Label>
          <Input
            type="number"
            min="1"
            max="31"
            value={config.dayOfMonth}
            onChange={(e) =>
              setConfig({ ...config, dayOfMonth: e.target.value })
            }
          />
        </div>
      )}

      {config.scheduleType === "custom" && (
        <div className="space-y-2">
          <Label>Cron Expression</Label>
          <Input
            placeholder="Enter cron expression"
            value={config.cronExpression}
            onChange={(e) =>
              setConfig({ ...config, cronExpression: e.target.value })
            }
          />
          <p className="text-xs text-muted-foreground">
            Format: minute hour day-of-month month day-of-week (e.g., "0 9 * *
            1" for every Monday at 9 AM)
          </p>
        </div>
      )}

      <div className="space-y-2">
        <Label>Time (24-hour format)</Label>
        <Input
          type="time"
          value={config.time}
          onChange={(e) => setConfig({ ...config, time: e.target.value })}
        />
      </div>

      <div className="space-y-2">
        <Label>Execution Context (JSON)</Label>
        <Textarea
          placeholder='{"candidateId": "123", "jobId": "456"}'
          value={config.context}
          onChange={(e) => setConfig({ ...config, context: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Optional JSON data to provide context for workflow execution
        </p>
      </div>
    </>
  );
}

export function DocumentUploadConfig({
  config,
  setConfig,
}: WorkflowConfigProps<DocumentUploadTriggerConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Document Types</Label>
        <Select
          value={config.documentType}
          onValueChange={(value: DocumentUploadTriggerConfig["documentType"]) =>
            setConfig({ ...config, documentType: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select document type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Documents</SelectItem>
            <SelectItem value="resume">Resumes</SelectItem>
            <SelectItem value="cover_letter">Cover Letters</SelectItem>
            <SelectItem value="portfolio">Portfolios</SelectItem>
            <SelectItem value="id">ID Documents</SelectItem>
            <SelectItem value="certificate">Certificates</SelectItem>
            <SelectItem value="custom">Custom Type</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.documentType === "custom" && (
        <div className="space-y-2">
          <Label>Custom Document Type</Label>
          <Input
            placeholder="Enter document type"
            value={config.customDocumentType}
            onChange={(e) =>
              setConfig({ ...config, customDocumentType: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>File Format Filter</Label>
        <Select
          value={config.fileFormat}
          onValueChange={(value: DocumentUploadTriggerConfig["fileFormat"]) =>
            setConfig({ ...config, fileFormat: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select file format" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Formats</SelectItem>
            <SelectItem value="pdf">PDF</SelectItem>
            <SelectItem value="doc">Word Documents</SelectItem>
            <SelectItem value="image">Images</SelectItem>
            <SelectItem value="text">Text Files</SelectItem>
            <SelectItem value="custom">Custom Format</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.fileFormat === "custom" && (
        <div className="space-y-2">
          <Label>Custom File Format</Label>
          <Input
            placeholder="Enter file extension (e.g., xlsx)"
            value={config.customFileFormat}
            onChange={(e) =>
              setConfig({ ...config, customFileFormat: e.target.value })
            }
          />
        </div>
      )}

      <div className="flex items-center justify-between">
        <Label htmlFor="auto-process">Auto-Process Document</Label>
        <Switch
          id="auto-process"
          checked={config.autoProcess}
          onCheckedChange={(checked) =>
            setConfig({ ...config, autoProcess: checked })
          }
        />
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="notify-upload">Notify on Upload</Label>
        <Switch
          id="notify-upload"
          checked={config.notifyUpload}
          onCheckedChange={(checked) =>
            setConfig({ ...config, notifyUpload: checked })
          }
        />
      </div>
    </>
  );
}

export function WebhookTriggerConfig({
  config,
  setConfig,
}: WorkflowConfigProps<WebhookTriggerConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Webhook Event</Label>
        <Select
          value={config.event}
          onValueChange={(value: WebhookTriggerConfig["event"]) =>
            setConfig({ ...config, event: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select webhook event" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Events</SelectItem>
            <SelectItem value="candidate_created">Candidate Created</SelectItem>
            <SelectItem value="status_changed">Status Changed</SelectItem>
            <SelectItem value="document_uploaded">Document Uploaded</SelectItem>
            <SelectItem value="interview_scheduled">
              Interview Scheduled
            </SelectItem>
            <SelectItem value="custom">Custom Event</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.event === "custom" && (
        <div className="space-y-2">
          <Label>Custom Event Name</Label>
          <Input
            placeholder="Enter custom event name"
            value={config.customEvent}
            onChange={(e) =>
              setConfig({ ...config, customEvent: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>Secret Key</Label>
        <Input
          placeholder="Enter webhook secret key"
          value={config.secretKey}
          onChange={(e) => setConfig({ ...config, secretKey: e.target.value })}
        />
        <p className="text-xs text-muted-foreground">
          Used to verify webhook authenticity
        </p>
      </div>

      <div className="space-y-2">
        <Label>Payload Filter (JSON Path)</Label>
        <Input
          placeholder="e.g., $.event.type"
          value={config.payloadFilter}
          onChange={(e) =>
            setConfig({ ...config, payloadFilter: e.target.value })
          }
        />
        <p className="text-xs text-muted-foreground">
          Optional JSON path to filter webhook events
        </p>
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="validate-signature">Validate Signature</Label>
        <Switch
          id="validate-signature"
          checked={config.validateSignature}
          onCheckedChange={(checked) =>
            setConfig({ ...config, validateSignature: checked })
          }
        />
      </div>
    </>
  );
}

export function DatabaseTriggerConfig({
  config,
  setConfig,
}: WorkflowConfigProps<DatabaseTriggerConfig>) {
  return (
    <>
      <div className="space-y-2">
        <Label>Database Table</Label>
        <Select
          value={config.table}
          onValueChange={(value: DatabaseTriggerConfig["table"]) =>
            setConfig({ ...config, table: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select database table" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="candidates">Candidates</SelectItem>
            <SelectItem value="jobs">Jobs</SelectItem>
            <SelectItem value="applications">Applications</SelectItem>
            <SelectItem value="interviews">Interviews</SelectItem>
            <SelectItem value="documents">Documents</SelectItem>
            <SelectItem value="custom">Custom Table</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {config.table === "custom" && (
        <div className="space-y-2">
          <Label>Custom Table Name</Label>
          <Input
            placeholder="Enter table name"
            value={config.customTable}
            onChange={(e) =>
              setConfig({ ...config, customTable: e.target.value })
            }
          />
        </div>
      )}

      <div className="space-y-2">
        <Label>Operation Type</Label>
        <Select
          value={config.operation}
          onValueChange={(value: DatabaseTriggerConfig["operation"]) =>
            setConfig({ ...config, operation: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select operation type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Operations</SelectItem>
            <SelectItem value="insert">Insert</SelectItem>
            <SelectItem value="update">Update</SelectItem>
            <SelectItem value="delete">Delete</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Filter Condition (SQL WHERE clause)</Label>
        <Textarea
          placeholder="e.g., status = 'approved'"
          value={config.filterCondition}
          onChange={(e) =>
            setConfig({ ...config, filterCondition: e.target.value })
          }
        />
        <p className="text-xs text-muted-foreground">
          Optional SQL WHERE clause to filter database events
        </p>
      </div>

      <div className="flex items-center justify-between">
        <Label htmlFor="include-old-data">Include Old Data (for updates)</Label>
        <Switch
          id="include-old-data"
          checked={config.includeOldData}
          onCheckedChange={(checked) =>
            setConfig({ ...config, includeOldData: checked })
          }
        />
      </div>
    </>
  );
}
