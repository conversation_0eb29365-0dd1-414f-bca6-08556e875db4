import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { WorkflowExecutor } from "./WorkflowExecutor";
import { Loader2, CalendarIcon, Play, Clock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  useWorkflowSchedules,
  useCreateWorkflowSchedule,
} from "@/hooks/useWorkflowSchedules";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";

interface RunWorkflowDialogProps {
  isOpen: boolean;
  onClose: () => void;
  workflowId: string;
  workflowName: string;
}

export function RunWorkflowDialog({
  isOpen,
  onClose,
  workflowId,
  workflowName,
}: RunWorkflowDialogProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [isScheduling, setIsScheduling] = useState(false);
  const [candidateId, setCandidateId] = useState("");
  const [jobId, setJobId] = useState("");
  const [additionalContext, setAdditionalContext] = useState("");
  const [executionContext, setExecutionContext] = useState<Record<string, any>>(
    {},
  );
  const [scheduleWorkflow, setScheduleWorkflow] = useState(false);
  const [scheduleDate, setScheduleDate] = useState("");
  const [scheduleTime, setScheduleTime] = useState("");
  const createSchedule = useCreateWorkflowSchedule();
  const { toast } = useToast();

  const handlePrepareExecution = () => {
    // Prepare execution context
    const context: Record<string, any> = {};

    if (candidateId) {
      context.candidateId = candidateId;
    }

    if (jobId) {
      context.jobId = jobId;
    }

    // Parse additional context if provided
    if (additionalContext) {
      try {
        const parsedContext = JSON.parse(additionalContext);
        Object.assign(context, parsedContext);
      } catch (error) {
        // If not valid JSON, treat as a simple string
        context.additionalInfo = additionalContext;
      }
    }

    setExecutionContext(context);
    setIsRunning(true);
  };

  const handleComplete = () => {
    setIsRunning(false);
  };

  const handleScheduleWorkflow = async () => {
    if (!scheduleDate || !scheduleTime) {
      toast({
        title: "Missing Information",
        description: "Please select a date and time for scheduling",
        variant: "destructive",
      });
      return;
    }

    setIsScheduling(true);
    try {
      // Prepare context
      const context: Record<string, any> = {};

      if (candidateId) {
        context.candidateId = candidateId;
      }

      if (jobId) {
        context.jobId = jobId;
      }

      // Parse additional context if provided
      if (additionalContext) {
        try {
          const parsedContext = JSON.parse(additionalContext);
          Object.assign(context, parsedContext);
        } catch (error) {
          // If not valid JSON, treat as a simple string
          context.additionalInfo = additionalContext;
        }
      }

      // Calculate cron expression from date and time
      // This is a simple implementation - in a real app, you'd want to convert to a proper cron expression
      const scheduledDateTime = new Date(`${scheduleDate}T${scheduleTime}`);
      const now = new Date();

      // If the date is in the past, show an error
      if (scheduledDateTime <= now) {
        toast({
          title: "Invalid Date",
          description: "Please select a future date and time",
          variant: "destructive",
        });
        setIsScheduling(false);
        return;
      }

      // Create a one-time cron expression (simplified for this example)
      const minute = scheduledDateTime.getMinutes();
      const hour = scheduledDateTime.getHours();
      const day = scheduledDateTime.getDate();
      const month = scheduledDateTime.getMonth() + 1; // JavaScript months are 0-indexed

      const cronExpression = `${minute} ${hour} ${day} ${month} *`;

      // Create the schedule
      await createSchedule.mutateAsync({
        workflow_id: workflowId,
        cron_schedule: cronExpression,
        context,
        is_active: true,
      });

      toast({
        title: "Workflow Scheduled",
        description: `Workflow scheduled for ${scheduledDateTime.toLocaleString()}`,
      });

      onClose();
    } catch (error) {
      console.error("Error scheduling workflow:", error);
      toast({
        title: "Error",
        description: "Failed to schedule workflow. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsScheduling(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Run Workflow: {workflowName}</DialogTitle>
        </DialogHeader>

        {!isRunning ? (
          <div className="space-y-4 py-4">
            <div className="flex items-center space-x-2 mb-4">
              <Switch
                id="schedule-workflow"
                checked={scheduleWorkflow}
                onCheckedChange={setScheduleWorkflow}
              />
              <Label htmlFor="schedule-workflow">Schedule for later</Label>
            </div>

            {scheduleWorkflow && (
              <div className="grid grid-cols-2 gap-4 mb-4 p-4 border rounded-md">
                <div className="space-y-2">
                  <Label htmlFor="schedule-date">Date</Label>
                  <div className="flex">
                    <CalendarIcon className="w-4 h-4 mr-2 mt-3" />
                    <Input
                      id="schedule-date"
                      type="date"
                      value={scheduleDate}
                      onChange={(e) => setScheduleDate(e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="schedule-time">Time</Label>
                  <div className="flex">
                    <Clock className="w-4 h-4 mr-2 mt-3" />
                    <Input
                      id="schedule-time"
                      type="time"
                      value={scheduleTime}
                      onChange={(e) => setScheduleTime(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="candidate-id">Candidate ID (Optional)</Label>
              <Input
                id="candidate-id"
                placeholder="Enter candidate ID"
                value={candidateId}
                onChange={(e) => setCandidateId(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                If this workflow processes a candidate, enter their ID
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="job-id">Job ID (Optional)</Label>
              <Input
                id="job-id"
                placeholder="Enter job ID"
                value={jobId}
                onChange={(e) => setJobId(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                If this workflow is related to a job, enter the job ID
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="additional-context">
                Additional Context (Optional)
              </Label>
              <Textarea
                id="additional-context"
                placeholder="Enter additional context as JSON or text"
                value={additionalContext}
                onChange={(e) => setAdditionalContext(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                You can provide additional context as JSON or plain text
              </p>
            </div>

            <Button
              onClick={
                scheduleWorkflow
                  ? handleScheduleWorkflow
                  : handlePrepareExecution
              }
              className="w-full"
              disabled={
                (scheduleWorkflow && (!scheduleDate || !scheduleTime)) ||
                isScheduling
              }
            >
              {scheduleWorkflow ? (
                isScheduling ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Scheduling...
                  </>
                ) : (
                  <>
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    Schedule Workflow
                  </>
                )
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Start Execution
                </>
              )}
            </Button>
          </div>
        ) : (
          <WorkflowExecutor
            workflowId={workflowId}
            onComplete={handleComplete}
            context={executionContext}
          />
        )}

        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onClose} disabled={isRunning}>
            {isRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running...
              </>
            ) : (
              "Close"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
