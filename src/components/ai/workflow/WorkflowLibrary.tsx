import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { workflowNodes } from "./workflow-nodes";
import { WorkflowNodeConfig } from "./WorkflowNodeConfig";

interface WorkflowLibraryProps {
  onNodeSelect: (node: any) => void;
}

export function WorkflowLibrary({ onNodeSelect }: WorkflowLibraryProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedLibraryNode, setSelectedLibraryNode] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("triggers");

  const filteredNodes = (category: string) => {
    return workflowNodes
      .filter((node) => node.category === category)
      .filter(
        (node) =>
          node.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          node.description.toLowerCase().includes(searchQuery.toLowerCase()),
      );
  };

  const handleNodeClick = (node: any) => {
    onNodeSelect(node);
    setSelectedLibraryNode({
      id: "preview",
      data: {
        ...node,
        originalId: node.id,
      },
    });
  };

  return (
    <>
      <Card className="w-80 max-h-[calc(100vh-8rem)] overflow-y-auto">
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search nodes..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <Tabs
          defaultValue={activeTab}
          value={activeTab}
          onValueChange={setActiveTab}
          className="h-[calc(100%-5rem)] flex flex-col"
        >
          <TabsList className="w-full justify-start rounded-none border-b bg-transparent grid grid-cols-3 gap-x-2 min-h-[96px] py-2">
            <TabsTrigger value="triggers" className="flex-shrink-0">
              Triggers
            </TabsTrigger>
            <TabsTrigger value="actions" className="flex-shrink-0">
              Actions
            </TabsTrigger>
            <TabsTrigger value="conditions" className="flex-shrink-0">
              Conditions
            </TabsTrigger>
            <TabsTrigger value="outputs" className="flex-shrink-0">
              Outputs
            </TabsTrigger>
            <TabsTrigger value="transformations" className="flex-shrink-0">
              Transforms
            </TabsTrigger>
            <TabsTrigger value="integrations" className="flex-shrink-0">
              Integrations
            </TabsTrigger>
          </TabsList>
          <ScrollArea className="h-[calc(100%-3rem)] rounded-none">
            {[
              "triggers",
              "actions",
              "conditions",
              "outputs",
              "transformations",
              "integrations",
            ].map((category) => (
              <TabsContent key={category} value={category} className="m-0">
                <div className="p-4 grid gap-2">
                  {filteredNodes(category).map((node) => (
                    <div
                      key={node.id}
                      className="p-3 rounded-md border bg-card hover:bg-accent hover:border-primary cursor-pointer transition-all"
                      onClick={() => handleNodeClick(node)}
                      onDragStart={(e) => {
                        e.dataTransfer.setData(
                          "application/reactflow",
                          JSON.stringify({
                            ...node,
                            icon: null,
                          }),
                        );
                        e.dataTransfer.effectAllowed = "move";
                      }}
                      draggable
                    >
                      <div className="flex items-center gap-2 text-sm font-medium">
                        {node.icon}
                        {node.label}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {node.description}
                      </p>
                    </div>
                  ))}
                </div>
              </TabsContent>
            ))}
          </ScrollArea>
        </Tabs>
      </Card>
      {selectedLibraryNode && (
        <WorkflowNodeConfig
          node={selectedLibraryNode}
          onClose={() => setSelectedLibraryNode(null)}
          onUpdate={() => {}}
        />
      )}
    </>
  );
}
