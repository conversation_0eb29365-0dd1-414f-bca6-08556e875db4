import {
  BaseEdge,
  EdgeProps,
  getBezierPath,
  useReactFlow,
} from "@xyflow/react";

interface WorkflowEdgeProps extends EdgeProps {
  sourceHandle?: string;
}

export function WorkflowEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  id,
  sourceHandle,
}: WorkflowEdgeProps) {
  const { setEdges } = useReactFlow();
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    curvature: 0.3,
  });

  const onEdgeClick = (
    evt: React.MouseEvent<SVGGElement, MouseEvent>,
    id: string,
  ) => {
    evt.stopPropagation();
    setEdges((edges) => edges.filter((edge) => edge.id !== id));
  };

  // Determine edge color based on source handle (for conditional paths)
  let edgeColor = "#374151"; // Default color
  if (sourceHandle === "source-true") {
    edgeColor = "#10b981"; // Green for true path
  } else if (sourceHandle === "source-false") {
    edgeColor = "#ef4444"; // Red for false path
  }
  return (
    <g onClick={(event) => onEdgeClick(event, id)}>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={{
          ...style,
          strokeWidth: 2,
          stroke: edgeColor,
          cursor: "pointer",
        }}
      />
    </g>
  );
}
