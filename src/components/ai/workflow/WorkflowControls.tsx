import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Panel } from "@xyflow/react";
import {
  Save,
  Play,
  Undo,
  FolderOpen,
  Eye,
  CheckCircle,
  Activity,
} from "lucide-react";
import { toast } from "sonner";
import { SaveWorkflowDialog } from "./dialogs/SaveWorkflowDialog";
import { LoadWorkflowDialog } from "./dialogs/LoadWorkflowDialog";
import { supabase } from "@/integrations/supabase/client";
import { useEffect } from "react";
import { RunWorkflowDialog } from "./RunWorkflowDialog";

interface WorkflowControlsProps {
  onClear: () => void;
  onSave: () => void;
  onRun: () => void;
  hasNodes: boolean;
  nodes: any[];
  edges: any[];
  onLoad: (config: { nodes: any[]; edges: any[] }) => void;
  isEditing?: boolean;
  onToggleValidation?: () => void;
  onToggleExecutionLog?: () => void;
  showValidation?: boolean;
  showExecutionLog?: boolean;
}

export function WorkflowControls({
  onClear,
  onSave,
  onRun,
  hasNodes,
  nodes,
  edges,
  onLoad,
  isEditing = false,
  onToggleValidation,
  onToggleExecutionLog,
  showValidation = false,
  showExecutionLog = false,
}: WorkflowControlsProps) {
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isLoadDialogOpen, setIsLoadDialogOpen] = useState(false);
  const [isRunDialogOpen, setIsRunDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const [workflowId, setWorkflowId] = useState<string | null>(
    isEditing ? nodes[0]?.data?.workflowId : null,
  );

  useEffect(() => {
    // Get the current user's ID when the component mounts
    const getCurrentUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUserId(user?.id || null);
    };
    getCurrentUser();
  }, []);

  const handleSave = async (name: string, description: string) => {
    if (!hasNodes) {
      toast.error("Add some nodes to save the workflow");
      return;
    }

    if (!userId) {
      toast.error("You must be logged in to save workflows");
      return;
    }

    setIsSaving(true);
    try {
      const workflowData = {
        name,
        description,
        config: { nodes, edges },
        is_active: true,
      };

      if (isEditing && workflowId) {
        // Update existing workflow
        const { data, error } = await supabase
          .from("workflow_configurations")
          .update({
            name: workflowData.name,
            description: workflowData.description,
            config: workflowData.config,
            updated_at: new Date().toISOString(),
          })
          .eq("id", workflowId)
          .eq("created_by", userId)
          .select()
          .single();

        if (error) {
          console.error("Error updating workflow:", error);
          throw error;
        }

        console.log("Workflow updated successfully:", data);
        toast.success(`Workflow "${name}" updated successfully!`);
      } else {
        // Create new workflow
        const { data, error } = await supabase
          .from("workflow_configurations")
          .insert({
            name: workflowData.name,
            description: workflowData.description,
            config: workflowData.config,
            is_active: workflowData.is_active,
            created_by: userId,
          })
          .select()
          .single();

        if (error) {
          console.error("Error creating workflow:", error);
          throw error;
        }

        console.log("Workflow created successfully:", data);
        setWorkflowId(data.id);
        toast.success(`Workflow "${name}" saved successfully!`);
      }

      setIsSaveDialogOpen(false);
    } catch (error) {
      console.error("Error saving workflow:", error);
      // Check if it's a specific Supabase error
      if (error && typeof error === "object" && "message" in error) {
        toast.error(`Failed to save workflow: ${error.message}`);
      } else {
        toast.error("Failed to save workflow. Please try again.");
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleRun = () => {
    if (!hasNodes || nodes.length === 0) {
      toast.error("Add some nodes to run the workflow");
      return;
    }

    // Quick run with empty context
    toast.success("Workflow started");

    // In a real implementation, this would execute the workflow
    setTimeout(() => {
      toast.success("Workflow completed successfully");
    }, 2000);
  };

  return (
    <>
      <Panel position="top-right" className="flex gap-2">
        <Button variant="outline" size="sm" onClick={onClear}>
          <Undo className="w-4 h-4 mr-2" />
          Clear
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsLoadDialogOpen(true)}
        >
          <FolderOpen className="w-4 h-4 mr-2" />
          Load
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsSaveDialogOpen(true)}
        >
          <Save className="w-4 h-4 mr-2" />
          {isEditing ? "Update" : "Save"}
        </Button>
        <Button size="sm" onClick={handleRun}>
          <Play className="w-4 h-4 mr-2" />
          Run
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsRunDialogOpen(true)}
        >
          <Eye className="w-4 h-4 mr-2" />
          Run with Options
        </Button>
        {onToggleValidation && (
          <Button
            variant={showValidation ? "default" : "outline"}
            size="sm"
            onClick={onToggleValidation}
            title="Toggle workflow validation"
          >
            <CheckCircle className="w-4 h-4" />
          </Button>
        )}
        {onToggleExecutionLog && (
          <Button
            variant={showExecutionLog ? "default" : "outline"}
            size="sm"
            onClick={onToggleExecutionLog}
            title="Toggle execution log"
          >
            <Activity className="w-4 h-4" />
          </Button>
        )}
      </Panel>

      <SaveWorkflowDialog
        isOpen={isSaveDialogOpen}
        onClose={() => setIsSaveDialogOpen(false)}
        onSave={handleSave}
        isLoading={isSaving}
      />

      <LoadWorkflowDialog
        isOpen={isLoadDialogOpen}
        onClose={() => setIsLoadDialogOpen(false)}
        onLoad={onLoad}
      />

      {/* Run Workflow Dialog */}
      <RunWorkflowDialog
        isOpen={isRunDialogOpen}
        onClose={() => setIsRunDialogOpen(false)}
        workflowId={workflowId || ""}
        workflowName="Current Workflow"
      />
    </>
  );
}
