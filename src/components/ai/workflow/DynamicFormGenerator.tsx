import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";

interface JsonSchema {
  type: string;
  properties?: Record<string, any>;
  required?: string[];
  title?: string;
  description?: string;
  enum?: any[];
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  format?: string;
  items?: any;
  default?: any;
}

interface DynamicFormGeneratorProps {
  schema: JsonSchema;
  values: Record<string, any>;
  onChange: (values: Record<string, any>) => void;
  className?: string;
}

export function DynamicFormGenerator({
  schema,
  values,
  onChange,
  className,
}: DynamicFormGeneratorProps) {
  const handleFieldChange = (fieldName: string, value: any) => {
    onChange({
      ...values,
      [fieldName]: value,
    });
  };

  const renderField = (
    fieldName: string,
    fieldSchema: JsonSchema,
    parentPath = "",
  ) => {
    const fieldPath = parentPath ? `${parentPath}.${fieldName}` : fieldName;
    const fieldValue = parentPath
      ? values[parentPath]?.[fieldName]
      : values[fieldName];

    const isRequired = schema.required?.includes(fieldName);

    // Handle different field types
    switch (fieldSchema.type) {
      case "string":
        if (fieldSchema.enum) {
          // Render as select dropdown
          return (
            <div key={fieldPath} className="space-y-2">
              <Label htmlFor={fieldPath}>
                {fieldSchema.title || fieldName}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {fieldSchema.description && (
                <p className="text-xs text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
              <Select
                value={fieldValue || ""}
                onValueChange={(value) => handleFieldChange(fieldName, value)}
              >
                <SelectTrigger id={fieldPath}>
                  <SelectValue
                    placeholder={`Select ${fieldSchema.title || fieldName}`}
                  />
                </SelectTrigger>
                <SelectContent>
                  {fieldSchema.enum.map((option: string) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          );
        } else if (
          fieldSchema.format === "textarea" ||
          (fieldSchema.maxLength && fieldSchema.maxLength > 100)
        ) {
          // Render as textarea
          return (
            <div key={fieldPath} className="space-y-2">
              <Label htmlFor={fieldPath}>
                {fieldSchema.title || fieldName}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {fieldSchema.description && (
                <p className="text-xs text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
              <Textarea
                id={fieldPath}
                value={fieldValue || ""}
                onChange={(e) => handleFieldChange(fieldName, e.target.value)}
                placeholder={fieldSchema.default || ""}
                minLength={fieldSchema.minLength}
                maxLength={fieldSchema.maxLength}
              />
            </div>
          );
        } else {
          // Render as input
          return (
            <div key={fieldPath} className="space-y-2">
              <Label htmlFor={fieldPath}>
                {fieldSchema.title || fieldName}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {fieldSchema.description && (
                <p className="text-xs text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
              <Input
                id={fieldPath}
                type={fieldSchema.format || "text"}
                value={fieldValue || ""}
                onChange={(e) => handleFieldChange(fieldName, e.target.value)}
                placeholder={fieldSchema.default || ""}
                pattern={fieldSchema.pattern}
                minLength={fieldSchema.minLength}
                maxLength={fieldSchema.maxLength}
              />
            </div>
          );
        }

      case "number":
      case "integer":
        if (
          fieldSchema.minimum !== undefined &&
          fieldSchema.maximum !== undefined
        ) {
          // Render as slider
          return (
            <div key={fieldPath} className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor={fieldPath}>
                  {fieldSchema.title || fieldName}
                  {isRequired && <span className="text-red-500 ml-1">*</span>}
                </Label>
                <span className="text-sm text-muted-foreground">
                  {fieldValue || fieldSchema.default || fieldSchema.minimum}
                </span>
              </div>
              {fieldSchema.description && (
                <p className="text-xs text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
              <Slider
                id={fieldPath}
                value={[
                  fieldValue || fieldSchema.default || fieldSchema.minimum,
                ]}
                onValueChange={([value]) => handleFieldChange(fieldName, value)}
                min={fieldSchema.minimum}
                max={fieldSchema.maximum}
                step={1}
              />
            </div>
          );
        } else {
          // Render as number input
          return (
            <div key={fieldPath} className="space-y-2">
              <Label htmlFor={fieldPath}>
                {fieldSchema.title || fieldName}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {fieldSchema.description && (
                <p className="text-xs text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
              <Input
                id={fieldPath}
                type="number"
                value={fieldValue || ""}
                onChange={(e) =>
                  handleFieldChange(fieldName, parseFloat(e.target.value) || 0)
                }
                placeholder={fieldSchema.default?.toString() || ""}
                min={fieldSchema.minimum}
                max={fieldSchema.maximum}
              />
            </div>
          );
        }

      case "boolean":
        // Render as switch
        return (
          <div
            key={fieldPath}
            className="flex items-center justify-between space-y-2"
          >
            <div className="space-y-0.5">
              <Label htmlFor={fieldPath}>
                {fieldSchema.title || fieldName}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {fieldSchema.description && (
                <p className="text-xs text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
            </div>
            <Switch
              id={fieldPath}
              checked={fieldValue || fieldSchema.default || false}
              onCheckedChange={(checked) =>
                handleFieldChange(fieldName, checked)
              }
            />
          </div>
        );

      case "array":
        if (fieldSchema.items?.enum) {
          // Render as checkboxes
          return (
            <div key={fieldPath} className="space-y-2">
              <Label>
                {fieldSchema.title || fieldName}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {fieldSchema.description && (
                <p className="text-xs text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
              <div className="space-y-2">
                {fieldSchema.items.enum.map((option: string) => (
                  <div key={option} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${fieldPath}-${option}`}
                      checked={(fieldValue || []).includes(option)}
                      onCheckedChange={(checked) => {
                        const currentValues = fieldValue || [];
                        const newValues = checked
                          ? [...currentValues, option]
                          : currentValues.filter((v: string) => v !== option);
                        handleFieldChange(fieldName, newValues);
                      }}
                    />
                    <Label
                      htmlFor={`${fieldPath}-${option}`}
                      className="font-normal"
                    >
                      {option}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          );
        }
        // For other array types, render as comma-separated input
        return (
          <div key={fieldPath} className="space-y-2">
            <Label htmlFor={fieldPath}>
              {fieldSchema.title || fieldName}
              {isRequired && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {fieldSchema.description && (
              <p className="text-xs text-muted-foreground">
                {fieldSchema.description}
              </p>
            )}
            <Input
              id={fieldPath}
              value={(fieldValue || []).join(", ")}
              onChange={(e) =>
                handleFieldChange(
                  fieldName,
                  e.target.value.split(",").reduce((acc, s) => {
                    const trimmed = s.trim();
                    if (trimmed) acc.push(trimmed);
                    return acc;
                  }, [] as string[]),
                )
              }
              placeholder="Enter values separated by commas"
            />
          </div>
        );

      case "object":
        // Render nested object fields
        if (fieldSchema.properties) {
          return (
            <div key={fieldPath} className="space-y-4 p-4 border rounded-lg">
              <Label className="text-base font-semibold">
                {fieldSchema.title || fieldName}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {fieldSchema.description && (
                <p className="text-sm text-muted-foreground">
                  {fieldSchema.description}
                </p>
              )}
              {Object.entries(fieldSchema.properties).map(
                ([subFieldName, subFieldSchema]) =>
                  renderField(
                    subFieldName,
                    subFieldSchema as JsonSchema,
                    fieldName,
                  ),
              )}
            </div>
          );
        }
        break;

      default:
        return null;
    }
  };

  if (!schema || !schema.properties) {
    return <div>No schema provided</div>;
  }

  return (
    <div className={cn("space-y-4", className)}>
      {schema.title && (
        <h3 className="text-lg font-semibold">{schema.title}</h3>
      )}
      {schema.description && (
        <p className="text-sm text-muted-foreground">{schema.description}</p>
      )}

      {Object.entries(schema.properties).map(([fieldName, fieldSchema]) =>
        renderField(fieldName, fieldSchema as JsonSchema),
      )}
    </div>
  );
}
