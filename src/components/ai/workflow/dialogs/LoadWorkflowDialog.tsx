import { useState, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { supabase } from "@/integrations/supabase/client";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useAuth } from "@/contexts/AuthContext";

interface WorkflowConfig {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
}

interface LoadWorkflowDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (config: any) => void;
}

export function LoadWorkflowDialog({
  isOpen,
  onClose,
  onLoad,
}: LoadWorkflowDialogProps) {
  const { user } = useAuth();
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [isLoadingWorkflow, setIsLoadingWorkflow] = useState(false);

  // Real-time workflow configurations subscription
  const { records: allWorkflows = [], isLoading } = useRealtimeCollection(
    "workflow_configurations",
    async () => {
      if (!user) {
        console.log("No user found, returning empty array");
        return [];
      }

      try {
        const { data, error } = await supabase
          .from("workflow_configurations")
          .select("id, name, description, created_at, is_active")
          .eq("created_by", user.id)
          .eq("is_active", true)
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error loading workflows:", error);
          return [];
        }

        return data || [];
      } catch (error) {
        console.error("Error in workflow fetch:", error);
        return [];
      }
    },
    "public",
    `created_by=eq.${user?.id}`,
  );

  // Filter to active workflows only
  const workflows = useMemo(() => {
    return allWorkflows.filter((workflow) => workflow.is_active);
  }, [allWorkflows]);

  const handleLoad = async () => {
    if (!selectedId) return;

    try {
      setIsLoadingWorkflow(true);
      const { data, error } = await supabase
        .from("workflow_configurations")
        .select("config")
        .eq("id", selectedId)
        .single();

      if (error) throw error;
      if (data) {
        onLoad(data.config);
        onClose();
        toast.success("Workflow loaded successfully");
      }
    } catch (error) {
      console.error("Error loading workflow:", error);
      toast.error("Failed to load workflow");
    } finally {
      setIsLoadingWorkflow(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Load Workflow</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : workflows.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">
              No saved workflows found
            </p>
          ) : (
            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-2">
                {workflows.map((workflow) => (
                  <div
                    key={workflow.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                      selectedId === workflow.id
                        ? "border-primary bg-muted"
                        : "hover:bg-muted"
                    }`}
                    onClick={() => setSelectedId(workflow.id)}
                  >
                    <h4 className="font-medium">{workflow.name}</h4>
                    {workflow.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {workflow.description}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground mt-2">
                      Created:{" "}
                      {new Date(workflow.created_at).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleLoad}
            disabled={!selectedId || isLoadingWorkflow}
          >
            {isLoadingWorkflow ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load Workflow"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
