import { supabase } from "@/integrations/supabase/client";
import { executorRegistry } from "@/engine/ExecutorRegistry";
import {
  WorkflowExecutionConfig,
  ExecutionEvent,
  EdgeExecutionStrategy,
  ParallelExecutionResult,
  WaitAggregationStrategy,
} from "@/engine/types";
import { RealtimeChannel } from "@supabase/supabase-js";
import { realtimePublisher } from "@/engine/RealtimeEventPublisher";
import {
  workflowLogger,
  LogLevel,
  WorkflowMetrics,
} from "@/services/WorkflowLogger";
import { workflowAlertService } from "@/services/WorkflowAlertService";
import "@/engine/executors"; // Register all executors

// Configuration for the workflow execution
const workflowConfig: WorkflowExecutionConfig = {
  enableRealtime: true,
  realtimeChannel: "workflow_progress",
  maxParallelNodes: 5,
  globalTimeout: 60000,
};
export interface WorkflowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    id?: string;
    type: string;
    label: string;
    category?: string;
    config?: Record<string, unknown>;
    originalId?: string;
  };
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type: string;
  sourceHandle?: string;
  label?: string;
}

export interface ExecutionLog {
  nodeId: string;
  status: "success" | "error" | "pending" | "skipped";
  message: string;
  timestamp: Date;
  data?: unknown; // Optional data returned from node execution
}

export interface ExecutionResult {
  success: boolean;
  executionPath: string[];
  logs: ExecutionLog[];
  output?: unknown;
  error?: Error;
}

export class WorkflowExecutionEngine {
  private workflowId: string;
  private workflowName: string = "";
  private userId: string;
  private nodes: WorkflowNode[];
  private channel: RealtimeChannel | null = null;
  private edges: WorkflowEdge[];
  private executionPath: string[] = [];
  private executionLogs: ExecutionLog[] = [];
  private context: Record<string, unknown> = {};
  private executionId: string | null = null;
  private parallelExecutions: Map<string, ParallelExecutionResult> = new Map();
  private startTime: Date | null = null;
  private nodeErrors: Array<{
    nodeId: string;
    nodeName: string;
    error: string;
    timestamp: Date;
  }> = [];

  constructor(
    workflowId: string,
    userId: string,
    nodes: WorkflowNode[],
    edges: WorkflowEdge[],
    initialContext: Record<string, unknown> = {},
  ) {
    this.workflowId = workflowId;
    this.userId = userId;
    this.nodes = nodes;
    this.edges = edges;
    this.context = { ...initialContext };
  }

  private async trackWorkflowEvent(
    eventType: string,
    eventData: Record<string, unknown> = {},
  ) {
    try {
      await supabase.rpc("track_workflow_event", {
        p_user_id: this.userId,
        p_workflow_id: this.workflowId,
        p_event_type: eventType,
        p_event_data: eventData,
        p_session_id: sessionStorage.getItem("session_id") || null,
      });
    } catch (error) {
      console.error("Failed to track workflow event:", error);
    }
  }

  public async execute(): Promise<ExecutionResult> {
    this.startTime = new Date();

    try {
      // Get workflow name
      const { data: workflow } = await supabase
        .from("workflow_configurations")
        .select("name")
        .eq("id", this.workflowId)
        .single();

      if (workflow) {
        this.workflowName = workflow.name;
      }

      // Log workflow execution start
      await workflowLogger.log({
        level: "INFO",
        message: `Starting workflow execution: ${this.workflowName}`,
        workflowId: this.workflowId,
        userId: this.userId,
        metadata: { context: this.context },
      });

      // Track workflow execution start
      await this.trackWorkflowEvent("executed", {
        execution_id: this.executionId,
        node_count: this.nodes.length,
        edge_count: this.edges.length,
      });

      if (workflowConfig.enableRealtime) {
        this.channel = supabase.channel(
          workflowConfig.realtimeChannel || "workflow_progress",
        );
        this.channel.subscribe();
      }

      await this.logExecutionStart();
      this.emitEvent({
        type: "started",
        nodeId: this.workflowId,
        nodeName: "Workflow",
        timestamp: new Date(),
      });

      // Find trigger nodes (starting points)
      const triggerNodes = this.nodes.filter(
        (node) =>
          node.data.type === "trigger" || node.data.category === "triggers",
      );

      if (triggerNodes.length === 0) {
        throw new Error("No trigger node found in workflow");
      }

      // Start with the first trigger node
      const startNode = triggerNodes[0];

      try {
        // Execute the workflow starting from the trigger node
        const result = await this.executeNode(startNode);

        // Calculate and log metrics
        const endTime = new Date();
        const metrics = await this.calculateAndLogMetrics(endTime, "completed");

        // Send alerts
        await workflowAlertService.checkAndSendAlerts(
          this.workflowId,
          this.workflowName,
          this.executionId!,
          "completed",
          metrics.executionDurationMs,
        );

        // Log workflow execution completion
        await this.logExecutionComplete("completed");
        this.emitEvent({
          type: "success",
          nodeId: this.workflowId,
          nodeName: "Workflow",
          timestamp: new Date(),
        });

        // Track workflow completion
        await this.trackWorkflowEvent("completed", {
          execution_id: this.executionId,
          duration_ms: metrics.executionDurationMs,
          success_rate:
            this.executionLogs.filter((l) => l.status === "success").length /
            Math.max(this.executionLogs.length, 1),
          node_errors: this.nodeErrors.length,
        });

        if (this.channel) {
          this.channel.unsubscribe();
        }

        return {
          success: true,
          executionPath: this.executionPath,
          logs: this.executionLogs,
          output: result,
        };
      } catch (error) {
        const endTime = new Date();
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";

        // Log error
        await workflowLogger.log({
          level: "ERROR",
          message: `Workflow execution failed: ${errorMessage}`,
          workflowId: this.workflowId,
          executionId: this.executionId || undefined,
          error: error as Error,
        });

        // Calculate and log metrics
        const metrics = await this.calculateAndLogMetrics(endTime, "failed");

        // Send alerts
        await workflowAlertService.checkAndSendAlerts(
          this.workflowId,
          this.workflowName,
          this.executionId!,
          "failed",
          metrics.executionDurationMs,
          this.nodeErrors,
          errorMessage,
        );

        this.emitEvent({
          type: "error",
          nodeId: this.workflowId,
          nodeName: "Workflow",
          timestamp: new Date(),
          error,
        });
        console.error("Workflow execution error:", error);
        if (this.channel) {
          this.channel.unsubscribe();
        }

        // Log workflow execution failure
        await this.logExecutionComplete("failed", error as Error);

        // Track workflow error
        await this.trackWorkflowEvent("error", {
          execution_id: this.executionId,
          error_message: errorMessage,
          error_type: error instanceof Error ? error.name : "Unknown",
          failed_nodes: this.nodeErrors.length,
        });

        return {
          success: false,
          executionPath: this.executionPath,
          logs: this.executionLogs,
          error: error as Error,
        };
      }
    } catch (error) {
      console.error("Workflow execution setup error:", error);
      return {
        success: false,
        executionPath: [],
        logs: [],
        error: error as Error,
      };
    }
  }

  private async executeNode(
    node: WorkflowNode,
    previousResult?: unknown,
  ): Promise<unknown> {
    // Add node to execution path
    this.executionPath.push(node.id);
    console.log(`Executing node: ${node.id} (${node.data.label})`);

    // Log node execution start
    await workflowLogger.log({
      level: "DEBUG",
      message: `Executing node: ${node.data.label}`,
      workflowId: this.workflowId,
      executionId: this.executionId || undefined,
      nodeId: node.id,
      metadata: { nodeType: node.data.type, nodeCategory: node.data.category },
    });

    // Emit started event
    this.emitEvent({
      type: "started",
      nodeId: node.id,
      nodeName: node.data.label || node.id,
      timestamp: new Date(),
      data: { previousResult },
    });

    // Log node execution start
    this.executionLogs.push({
      nodeId: node.id,
      status: "pending",
      message: `Executing ${node.data.label}...`,
      timestamp: new Date(),
    });

    // Execute the node
    let success = false;
    let result = null;
    let errorMessage = "";

    try {
      // Get executor for this node type
      const nodeType = node.data.originalId || node.data.id || node.data.type;
      const executor = executorRegistry.getExecutor(nodeType);

      if (!executor) {
        throw new Error(`No executor found for node type: ${nodeType}`);
      }

      // Execute the node
      const executionResult = await executor.execute(node.data, this.context, {
        timeout: workflowConfig.defaultNodeTimeout || 30000,
        retries: Number(node.data.config?.retries) || 0,
        retryDelay: Number(node.data.config?.retryDelay) || 1000,
      });

      success = executionResult.success;
      result = executionResult.data;

      if (success) {
        console.log(`${node.data.label} executed successfully:`, result);

        // Log node success
        await workflowLogger.log({
          level: "DEBUG",
          message: `Node executed successfully: ${node.data.label}`,
          workflowId: this.workflowId,
          executionId: this.executionId || undefined,
          nodeId: node.id,
          metadata: { result },
        });

        // Emit success event
        this.emitEvent({
          type: "success",
          nodeId: node.id,
          nodeName: node.data.label || node.id,
          timestamp: new Date(),
          data: result,
        });

        // Log success
        this.executionLogs.push({
          nodeId: node.id,
          status: "success",
          message: `Successfully executed ${node.data.label}`,
          timestamp: new Date(),
          data: result,
        });
      } else {
        throw executionResult.error || new Error("Execution failed");
      }
    } catch (error) {
      success = false;
      errorMessage = error instanceof Error ? error.message : "Unknown error";
      console.error(`Error executing node ${node.data.label}:`, error);

      // Store node error for metrics
      this.nodeErrors.push({
        nodeId: node.id,
        nodeName: node.data.label || node.id,
        error: errorMessage,
        timestamp: new Date(),
      });

      // Log node error
      await workflowLogger.log({
        level: "ERROR",
        message: `Node execution failed: ${node.data.label}`,
        workflowId: this.workflowId,
        executionId: this.executionId || undefined,
        nodeId: node.id,
        error: error as Error,
        metadata: {
          nodeType: node.data.type,
          nodeCategory: node.data.category,
        },
      });

      // Emit error event
      this.emitEvent({
        type: "error",
        nodeId: node.id,
        nodeName: node.data.label || node.id,
        timestamp: new Date(),
        error: error as Error,
      });

      // Log error
      this.executionLogs.push({
        nodeId: node.id,
        status: "error",
        message: `Failed to execute ${node.data.label}: ${errorMessage}`,
        timestamp: new Date(),
      });
    }

    if (!success) {
      // If node execution failed, stop the workflow
      throw new Error(`Workflow execution failed at node: ${node.data.label}`);
    }

    // Update context with node result if available
    if (result) {
      this.context = {
        ...this.context,
        [node.id]: result,
        lastResult: result,
        lastNodeId: node.id,
        lastNodeType: node.data.type || node.data.category,
      };
    }

    // Find outgoing edges from this node
    const outgoingEdges = this.edges.filter((edge) => edge.source === node.id);

    // If this is a condition node, determine which path to take
    if (node.data.type === "condition" || node.data.category === "conditions") {
      const conditionResult =
        result?.result !== undefined ? result.result : result;

      // Find the appropriate edge based on condition result
      if (outgoingEdges.length > 0) {
        let nextEdge;

        // Look for true/false source handles
        if (conditionResult) {
          nextEdge = outgoingEdges.find(
            (edge) => edge.sourceHandle === "source-true",
          );
          if (!nextEdge) nextEdge = outgoingEdges[0]; // Fallback to first edge
        } else {
          nextEdge = outgoingEdges.find(
            (edge) => edge.sourceHandle === "source-false",
          );
          if (!nextEdge && outgoingEdges.length > 1)
            nextEdge = outgoingEdges[1]; // Fallback to second edge
          if (!nextEdge && outgoingEdges.length > 0)
            nextEdge = outgoingEdges[0]; // Fallback to first edge
        }

        if (nextEdge) {
          const nextNode = this.nodes.find((n) => n.id === nextEdge.target);
          if (nextNode) {
            // Execute the next node
            await this.executeNode(nextNode);
          }
        }
      }
    } else {
      // For non-condition nodes, determine execution strategy
      const executionStrategy = this.getEdgeExecutionStrategy(
        node,
        outgoingEdges,
      );

      if (executionStrategy.type === "parallel") {
        // Execute all paths in parallel
        await this.executeParallel(outgoingEdges, result);
      } else if (executionStrategy.type === "wait-all") {
        // Execute with wait aggregation
        await this.executeWithWaitAggregation(
          outgoingEdges,
          result,
          executionStrategy,
        );
      } else {
        // Sequential execution (default)
        for (const edge of outgoingEdges) {
          const nextNode = this.nodes.find((n) => n.id === edge.target);
          if (nextNode) {
            await this.executeNode(nextNode, result);
          }
        }
      }
    }

    return result;
  }

  private emitEvent(event: ExecutionEvent): void {
    if (workflowConfig.enableRealtime && workflowConfig.realtimeChannel) {
      // Use the realtime publisher for better event handling
      realtimePublisher.publishEvent(
        `${workflowConfig.realtimeChannel}_${this.workflowId}`,
        event,
      );
    }

    // Also send to the local channel if available
    if (this.channel) {
      this.channel.send({
        type: "broadcast",
        event: "workflow_event",
        payload: event,
      });
    }
  }

  private async logExecutionStart(): Promise<void> {
    try {
      // Create execution log entry
      const { data, error } = await supabase
        .from("workflow_executions")
        .insert({
          workflow_id: this.workflowId,
          status: "in_progress",
          started_at: new Date().toISOString(),
          execution_log: {
            context: this.context,
            nodes: [],
            executionPath: [],
          },
          created_by: this.userId,
        })
        .select()
        .single();

      if (error) {
        console.error("Error logging workflow execution start:", error);
        return;
      }

      // Store execution ID for later updates
      this.executionId = data?.id || null;
    } catch (error) {
      console.error("Error logging workflow execution start:", error);
    }
  }

  private async logExecutionComplete(
    status: "completed" | "failed",
    error?: Error,
  ): Promise<void> {
    if (!this.executionId) return;

    try {
      // Update execution log entry
      const { error: updateError } = await supabase
        .from("workflow_executions")
        .update({
          status: status,
          completed_at: new Date().toISOString(),
          execution_log: {
            context: this.context,
            nodes: this.executionLogs,
            executionPath: this.executionPath,
            error: error ? error.message : undefined,
          },
        })
        .eq("id", this.executionId || "");

      if (updateError) {
        console.error(
          "Error logging workflow execution completion:",
          updateError,
        );
      }
    } catch (error) {
      console.error("Error logging workflow execution completion:", error);
    }
  }

  /**
   * Calculate and log workflow execution metrics
   */
  private async calculateAndLogMetrics(
    endTime: Date,
    status: "completed" | "failed",
  ): Promise<WorkflowMetrics> {
    const executionDurationMs =
      endTime.getTime() - (this.startTime?.getTime() || 0);

    // Count nodes by status
    const totalNodesExecuted = this.executionLogs.length;
    const successfulNodes = this.executionLogs.filter(
      (log) => log.status === "success",
    ).length;
    const failedNodes = this.executionLogs.filter(
      (log) => log.status === "error",
    ).length;
    const skippedNodes = this.executionLogs.filter(
      (log) => log.status === "skipped",
    ).length;

    const metrics: WorkflowMetrics = {
      workflowId: this.workflowId,
      executionId: this.executionId!,
      executionDurationMs,
      startTime: this.startTime!,
      endTime,
      totalNodesExecuted,
      successfulNodes,
      failedNodes,
      skippedNodes,
      nodeErrors: this.nodeErrors,
      contextData: {
        finalContext: this.context,
        executionPath: this.executionPath,
      },
      errorDetails:
        status === "failed"
          ? this.nodeErrors.map((e) => e.error).join("; ")
          : undefined,
    };

    // Log metrics
    await workflowLogger.logMetrics(metrics);

    return metrics;
  }

  /**
   * Determine edge execution strategy based on node configuration
   */
  private getEdgeExecutionStrategy(
    node: any,
    edges: any[],
  ): EdgeExecutionStrategy {
    // Check if node has explicit execution strategy
    if (node.data.config?.executionStrategy) {
      return node.data.config.executionStrategy;
    }

    // Check if multiple edges indicate parallel execution
    if (edges.length > 1 && node.data.config?.parallel) {
      return { type: "parallel" };
    }

    // Default to sequential
    return { type: "sequential" };
  }

  /**
   * Execute multiple edges in parallel
   */
  private async executeParallel(
    edges: any[],
    previousResult: any,
  ): Promise<void> {
    const parallelTasks = edges.map((edge) => {
      const nextNode = this.nodes.find((n) => n.id === edge.target);
      if (nextNode) {
        return this.executeNode(nextNode, previousResult);
      }
      return Promise.resolve();
    });

    // Execute all tasks in parallel with concurrency limit
    const maxParallel = workflowConfig.maxParallelNodes || 5;
    const results = [];

    for (let i = 0; i < parallelTasks.length; i += maxParallel) {
      const batch = parallelTasks.slice(i, i + maxParallel);
      const batchResults = await Promise.allSettled(batch);
      results.push(...batchResults);
    }

    // Handle any failures
    const failures = results.filter((r) => r.status === "rejected");
    if (failures.length > 0) {
      console.error("Parallel execution had failures:", failures);
    }
  }

  /**
   * Execute with wait aggregation strategy
   */
  private async executeWithWaitAggregation(
    edges: any[],
    previousResult: any,
    strategy: EdgeExecutionStrategy,
  ): Promise<void> {
    const waitTimeout = strategy.waitTimeout || 30000;
    const startTime = Date.now();
    const parallelResults: ParallelExecutionResult[] = [];

    // Start all executions in parallel
    const executionPromises = edges.map(async (edge) => {
      const nextNode = this.nodes.find((n) => n.id === edge.target);
      if (!nextNode) return null;

      const result: ParallelExecutionResult = {
        nodeId: nextNode.id,
        result: { success: false },
        completed: false,
        startTime: new Date(),
      };

      parallelResults.push(result);

      try {
        const executionResult = await this.executeNode(
          nextNode,
          previousResult,
        );
        result.result = {
          success: true,
          data: executionResult as
            | string
            | number
            | boolean
            | Record<string, unknown>
            | unknown[],
        };
        result.completed = true;
        result.endTime = new Date();
      } catch (error) {
        result.result = { success: false, error: error as Error };
        result.completed = true;
        result.endTime = new Date();
      }

      return result;
    });

    // Wait for all or timeout
    await Promise.race([
      Promise.all(executionPromises),
      new Promise((resolve) => setTimeout(resolve, waitTimeout)),
    ]);

    // Apply aggregation strategy
    const aggregationStrategy: WaitAggregationStrategy = {
      type: "all-success",
      timeout: waitTimeout,
    };

    const allCompleted = parallelResults.every((r) => r.completed);
    const anySuccess = parallelResults.some((r) => r.result.success);
    const allSuccess = parallelResults.every((r) => r.result.success);

    // Emit aggregation result
    this.emitEvent({
      type: "progress",
      nodeId: "aggregation",
      nodeName: "Wait Aggregation",
      timestamp: new Date(),
      data: {
        allCompleted,
        anySuccess,
        allSuccess,
        parallelResults: parallelResults.map((r) => ({
          nodeId: r.nodeId,
          success: r.result.success,
          completed: r.completed,
        })),
      },
    });
  }
}
