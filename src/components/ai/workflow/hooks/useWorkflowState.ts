import { useState, useCallback } from "react";
import {
  Connection,
  Edge,
  Node,
  useNodesState,
  useEdgesState,
  addEdge,
} from "@xyflow/react";

// Counter for generating unique IDs
let edgeCounter = 0;
const getUniqueEdgeId = () => {
  edgeCounter += 1;
  return `edge-${edgeCounter}`;
};

export function useWorkflowState() {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);
  const [isMinimapExpanded, setIsMinimapExpanded] = useState(false);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);

  const onConnect = useCallback(
    (params: Connection) => {
      if (!params.source || !params.target) return;

      // Get source node to check if it's a condition
      const sourceNode = nodes.find((node) => node.id === params.source);
      const isCondition =
        sourceNode?.data?.type === "condition" ||
        sourceNode?.data?.category === "conditions";

      // Determine edge style based on source handle (for conditional paths)
      const edgeStyle = {
        stroke: "#374151",
        strokeWidth: 2,
      };

      if (params.sourceHandle === "source-true") {
        edgeStyle.stroke = "#10b981"; // Green for true path
      } else if (params.sourceHandle === "source-false") {
        edgeStyle.stroke = "#ef4444"; // Red for false path
      }
      const newEdge: Edge = {
        id: getUniqueEdgeId(),
        source: params.source,
        target: params.target,
        sourceHandle: params.sourceHandle,
        targetHandle: params.targetHandle,
        type: "default",
        animated: true,
        style: edgeStyle,
        data: {
          isConditionalPath:
            isCondition &&
            (params.sourceHandle === "source-true" ||
              params.sourceHandle === "source-false"),
          condition:
            params.sourceHandle === "source-true"
              ? "true"
              : params.sourceHandle === "source-false"
                ? "false"
                : undefined,
        },
      };

      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges],
  );

  // Reset edge counter when nodes are cleared
  const resetNodes = useCallback(() => {
    setNodes([]);
    edgeCounter = 0;
  }, [setNodes]);
  return {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    reactFlowInstance,
    setReactFlowInstance,
    isMinimapExpanded,
    setIsMinimapExpanded,
    selectedNode,
    setSelectedNode,
    setNodes,
    setEdges,
    resetNodes,
  };
}
