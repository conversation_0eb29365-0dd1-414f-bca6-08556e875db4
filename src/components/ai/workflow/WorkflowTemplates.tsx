import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { useCreateWorkflowConfiguration } from "@/hooks/useWorkflowConfigurations";
import {
  useWorkflowTemplates,
  useInitializeWorkflowTemplates,
} from "@/hooks/useWorkflowTemplates";
import { useAuth } from "@/contexts/AuthContext";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sparkles,
  Lightbulb,
  ArrowRight,
  CheckCircle,
  XCircle,
  Loader2,
  FileText,
  Users,
  Mail,
  Calendar,
  UserPlus,
  Settings,
} from "lucide-react";

export function WorkflowTemplates() {
  const { user } = useAuth();
  const userId = user?.id;

  const {
    data: workflowTemplatesData = [],
    isLoading: templatesLoading,
    error: templatesError,
  } = useWorkflowTemplates(userId || "");

  const initializeTemplates = useInitializeWorkflowTemplates();

  const { toast } = useToast();
  const navigate = useNavigate();
  const createWorkflow = useCreateWorkflowConfiguration();
  const [creatingTemplateId, setCreatingTemplateId] = useState<string | null>(
    null,
  );

  // Initialize data if empty
  useEffect(() => {
    if (
      userId &&
      workflowTemplatesData.length === 0 &&
      !templatesLoading &&
      !templatesError
    ) {
      initializeTemplates.mutate(userId);
    }
  }, [
    userId,
    workflowTemplatesData.length,
    templatesLoading,
    templatesError,
    initializeTemplates,
  ]);

  const getTemplateIcon = (iconName: string) => {
    const iconMap: Record<string, any> = {
      Users: Users,
      Calendar: Calendar,
      Mail: Mail,
      CheckCircle: CheckCircle,
      FileText: FileText,
      UserPlus: UserPlus,
      Settings: Settings,
    };
    return iconMap[iconName] || Settings;
  };

  const handleCreateFromTemplate = async (template: any) => {
    setCreatingTemplateId(template.id);

    try {
      const result = await createWorkflow.mutateAsync({
        name: template.name,
        description: template.description || "",
        config: template.config,
        is_active: true,
      });

      toast({
        title: "Workflow Created",
        description: `${template.name} workflow has been created successfully`,
      });

      // Navigate to edit the new workflow
      navigate(`/ai-workflows?edit=${result.id}`);
    } catch (error) {
      console.error("Error creating workflow from template:", error);
      toast({
        title: "Error",
        description: "Failed to create workflow from template",
        variant: "destructive",
      });
    } finally {
      setCreatingTemplateId(null);
    }
  };

  if (templatesLoading || initializeTemplates.isPending) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Workflow Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-4 w-3/4 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-32 mb-2" />
                      <Skeleton className="h-4 w-full mb-4" />
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (templatesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Workflow Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertDescription>
              Failed to load workflow templates: {templatesError.message}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Workflow Templates</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-6">
          Get started quickly with pre-built workflow templates for common
          recruitment scenarios. Select a template to create a new workflow
          based on it.
        </p>

        <ScrollArea className="h-[500px]">
          {workflowTemplatesData.length === 0 ? (
            <div className="text-center py-8">
              <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                No Templates Available
              </h3>
              <p className="text-muted-foreground">
                Workflow templates will appear here once they are initialized.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {workflowTemplatesData.map((template) => {
                const Icon = getTemplateIcon(template.icon);
                return (
                  <Card
                    key={template.id}
                    className="hover:bg-accent/5 transition-colors"
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="bg-primary/10 p-3 rounded-full">
                          <Icon className="h-6 w-6 text-primary" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{template.name}</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            {template.description}
                          </p>
                          <Button
                            onClick={() => handleCreateFromTemplate(template)}
                            disabled={creatingTemplateId === template.id}
                            size="sm"
                          >
                            {creatingTemplateId === template.id ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Creating...
                              </>
                            ) : (
                              "Use Template"
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
