import { ReactNode, forwardRef } from "react";
import { cn } from "@/lib/utils";

interface WorkflowContainerProps {
  className?: string;
  children: ReactNode;
  onDragOver: (event: React.DragEvent<HTMLDivElement>) => void;
  onDrop: (event: React.DragEvent<HTMLDivElement>) => void;
}

export const WorkflowContainer = forwardRef<
  HTMLDivElement,
  WorkflowContainerProps
>(({ className, children, onDragOver, onDrop }, ref) => {
  return (
    <div
      className={cn("border rounded-lg bg-background relative", className)}
      onDragOver={onDragOver}
      onDrop={onDrop}
      ref={ref}
    >
      {children}
    </div>
  );
});

WorkflowContainer.displayName = "WorkflowContainer";
