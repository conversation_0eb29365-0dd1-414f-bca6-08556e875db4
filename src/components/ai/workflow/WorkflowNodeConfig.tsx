import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Settings2, X } from "lucide-react";
import { DynamicFormGenerator } from "./DynamicFormGenerator";
import { nodeSchemas } from "./nodeSchemas";
import { ScrollArea } from "@/components/ui/scroll-area";

interface WorkflowNodeConfigProps {
  node: any;
  onClose: () => void;
  onUpdate: (nodeId: string, data: any) => void;
}

export function WorkflowNodeConfig({
  node,
  onClose,
  onUpdate,
}: WorkflowNodeConfigProps) {
  const nodeType = node.data.originalId || node.data.id;
  const schema = nodeSchemas[nodeType];
  const [config, setConfig] = useState(
    node.data.config || getDefaultConfig(nodeType),
  );

  const handleSave = () => {
    onUpdate(node.id, {
      ...node.data,
      config,
    });
    toast.success("Node configuration updated");
  };

  return (
    <Card className="fixed right-4 top-4 w-80 z-50 shadow-lg">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Settings2 className="w-4 h-4" />
          <h3 className="font-medium">Configure {node.data.label}</h3>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </div>
      <ScrollArea className="h-[calc(100vh-200px)]">
        <div className="p-4 space-y-4">
          {schema ? (
            <DynamicFormGenerator
              schema={schema}
              values={config}
              onChange={setConfig}
            />
          ) : (
            <div className="text-sm text-muted-foreground">
              No configuration options available for this node type.
            </div>
          )}
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        </div>
      </ScrollArea>
    </Card>
  );
}

function getDefaultConfig(nodeType: string) {
  switch (nodeType) {
    // Triggers
    case "new-application":
      return {
        jobTypes: "all",
        notifyTeam: true,
        autoScreen: false,
      };
    case "application-status":
      return {
        statusType: "any",
        includeInternal: false,
      };
    case "scheduled-trigger":
      return {
        scheduleType: "daily",
        time: "09:00",
      };
    case "document-upload":
      return {
        documentType: "all",
        fileFormat: ["pdf", "doc", "docx"],
        maxFileSize: 10,
        autoProcess: true,
        notifyUpload: true,
        virusScan: true,
      };
    case "webhook-trigger":
      return {
        event: "all",
        secretKey: "",
        validateSignature: true,
        allowedIPs: "",
        retryAttempts: 3,
        timeout: 30,
      };
    case "database-trigger":
      return {
        table: "candidates",
        operation: "all",
        conditions: "",
        includeOldData: false,
        batchChanges: false,
        batchTimeout: 30,
      };

    // Actions
    case "send-email":
      return {
        template: "welcome",
        ccRecruiter: false,
        trackOpens: true,
      };
    case "schedule-interview":
      return {
        interviewType: "technical",
        duration: "60",
        sendCalendarInvite: true,
      };
    case "ai-screen":
      return {
        criteria: "comprehensive",
        minScore: "70",
        generateSummary: true,
      };
    case "send-assessment":
      return {
        assessmentType: "technical",
        assessmentTemplate: "javascript_developer",
        timeLimit: 60,
        passingScore: 70,
        autoGrade: true,
        sendReminders: true,
        reminderInterval: 24,
        maxAttempts: 1,
        includeInstructions: true,
        customInstructions: "",
      };
    case "notify-team":
      return {
        notificationChannel: "email",
        recipients: "recruiters",
        customRecipients: "",
        messageTemplate: "new_application",
        customMessage: "",
        priority: "normal",
        includeCandidate: true,
        includeJob: true,
        actionRequired: false,
        actionDeadline: "",
      };
    case "data-enrichment":
      return {
        enrichmentSource: "linkedin",
        dataTypes: ["contact_info", "work_history", "skills"],
        apiKey: "",
        customEndpoint: "",
        autoUpdate: true,
        overwriteExisting: false,
        confidenceThreshold: 80,
        rateLimitDelay: 1000,
        retryFailures: true,
        maxRetries: 3,
      };
    case "create-task":
      return {
        taskTitle: "",
        taskDescription: "",
        assignee: "recruiter",
        customAssignee: "",
        priority: "medium",
        dueDate: "",
        dueDateOffset: 7,
        category: "screening",
        autoComplete: false,
        completionCondition: "status_change",
        sendReminders: true,
        reminderInterval: 24,
      };
    case "add-tag":
      return {
        tagCategory: "skills",
        tags: "",
        tagColor: "blue",
        overwriteExisting: false,
        visibility: "team",
        autoExpire: false,
        expireDays: 90,
        notifyChanges: false,
      };
    case "score-candidate":
      return {
        scoringMethod: "weighted_criteria",
        scoreRange: "1-10",
        criteria: [
          { name: "Technical Skills", weight: 30 },
          { name: "Experience", weight: 25 },
          { name: "Cultural Fit", weight: 20 },
          { name: "Communication", weight: 15 },
          { name: "Education", weight: 10 },
        ],
        manualScore: 0,
        aiModel: "gpt-4",
        scoreNote: "",
        updateExisting: true,
        notifyTeam: false,
        thresholdActions: false,
        highThreshold: 80,
        lowThreshold: 40,
      };

    // Conditions
    case "experience-check":
      return {
        minYears: "3",
        industry: "any",
        considerInternships: true,
      };
    case "education-check":
      return {
        minDegree: "bachelors",
        fieldOfStudy: "any",
        considerEquivalentExp: true,
      };
    case "skills-match":
      return {
        requiredSkills: "",
        minMatchPercentage: "70",
        considerSimilarSkills: true,
      };
    case "location-check":
      return {
        locationType: "onsite",
        requiredLocation: "",
        maxDistance: 50,
        allowRelocation: true,
        relocationAssistance: false,
        timeZoneRequirement: "none",
        travelRequirement: "none",
        visaSponsorship: false,
        remoteEquipment: false,
      };
    case "salary-check":
      return {
        salaryRange: {
          min: 50000,
          max: 100000,
          currency: "USD",
        },
        salaryType: "annual",
        includeBonus: true,
        bonusPercentage: 10,
        equityOffered: false,
        negotiable: true,
        experienceAdjustment: true,
        locationAdjustment: true,
        marketComparison: true,
        confidential: true,
      };
    case "availability-check":
      return {
        startDateRequirement: "within_month",
        specificStartDate: "",
        noticePeriod: 4,
        workSchedule: "full_time",
        hoursPerWeek: 40,
        shiftPreference: "day",
        weekendWork: false,
        overtimeExpected: false,
        seasonalWork: false,
        contractDuration: 12,
      };
    case "visa-check":
      return {
        workAuthRequired: true,
        countryRequirement: "United States",
        acceptedStatuses: ["citizen", "permanent_resident"],
        sponsorshipAvailable: false,
        sponsorshipTypes: [],
        currentVisaStatus: true,
        expirationCheck: true,
        minValidityPeriod: 6,
        renewalSupport: false,
        dependentSponsorship: false,
        backgroundCheckRequired: false,
      };
    case "filter-condition":
      return {
        filterField: "status",
        customField: "",
        operator: "equals",
        filterValue: "",
        valueList: "",
        caseSensitive: false,
        regexPattern: "",
        logicalOperator: "and",
        negateResult: false,
      };
    case "data-comparison":
      return {
        leftField: "",
        rightField: "",
        staticValue: "",
        comparisonType: "numeric",
        operator: "equals",
        tolerance: 0,
        dateFormat: "YYYY-MM-DD",
        caseSensitive: false,
        nullHandling: "fail",
      };

    // Outputs
    case "update-status":
      return {
        newStatus: "screening",
        statusNote: "",
        notifyCandidate: true,
      };
    case "add-to-pool":
      return {
        poolName: "engineering",
        tags: "",
        followUpDate: "",
      };
    case "send-message":
      return {
        messageType: "email",
        recipient: "candidate",
        customRecipient: "",
        messageTemplate: "status_update",
        customMessage: "",
        subject: "",
        personalize: true,
        includeAttachments: false,
        attachmentTypes: [],
        sendImmediately: true,
        scheduleDelay: 0,
        trackDelivery: true,
        trackOpens: true,
        requireResponse: false,
        responseDeadline: "",
      };
    case "export-data":
      return {
        exportFormat: "csv",
        dataSource: "candidate_profile",
        customQuery: "",
        fieldsToInclude: [],
        destination: "download",
        emailRecipient: "",
        ftpSettings: {
          host: "",
          username: "",
          password: "",
          path: "",
        },
        apiEndpoint: "",
        apiHeaders: "",
        filename: "export_{{date}}_{{time}}",
        includeHeaders: true,
        compressFile: false,
        encryptFile: false,
        encryptionKey: "",
      };
    case "archive-candidate":
      return {
        archiveReason: "position_filled",
        customReason: "",
        retentionPeriod: 24,
        notifyCandidate: false,
        notificationTemplate: "thank_you",
        customNotification: "",
        preserveData: ["profile", "resume", "assessments"],
        allowReactivation: true,
        reactivationConditions: "",
        gdprCompliant: true,
        anonymizeData: false,
        auditTrail: true,
      };
    case "generate-report":
      return {
        reportType: "candidate_summary",
        reportFormat: "pdf",
        timeRange: "last_30_days",
        customDateRange: {
          startDate: "",
          endDate: "",
        },
        includeCharts: true,
        chartTypes: ["bar", "pie"],
        groupBy: "position",
        filters: [],
        includeRawData: false,
        confidential: true,
        watermark: true,
        distribution: [],
        scheduleRecurring: false,
        recurringFrequency: "monthly",
      };

    // Transform Nodes
    case "data-filter":
      return {
        filterType: "include",
        filterCriteria: [],
        outputFormat: "original",
        sortBy: "",
        sortOrder: "asc",
        limit: null,
        includeMetadata: false,
        errorHandling: "fail",
      };
    case "data-merge":
      return {
        mergeStrategy: "union",
        primaryKey: "",
        dataSources: [],
        fieldMapping: [],
        conflictResolution: "first_wins",
        customResolution: "",
        validateOutput: true,
        outputSchema: "",
      };
    case "data-loop":
      return {
        loopType: "for_each",
        dataSource: "",
        loopVariable: "item",
        indexVariable: "index",
        condition: "",
        startIndex: 0,
        endIndex: 10,
        stepSize: 1,
        maxIterations: 1000,
        parallelExecution: false,
        maxConcurrency: 5,
        continueOnError: false,
        collectResults: true,
        breakCondition: "",
      };
    case "schedule":
      return {
        scheduleType: "delay",
        delayAmount: 1,
        delayUnit: "hours",
        specificDateTime: "",
        recurringPattern: "daily",
        cronExpression: "",
        condition: "",
        timezone: "UTC",
        businessDaysOnly: false,
        skipHolidays: false,
        maxExecutions: null,
        endDate: "",
        retryOnFailure: true,
        maxRetries: 3,
        retryDelay: 5,
      };

    // Integration Nodes
    case "linkedin-integration":
      return {
        actionType: "search_candidates",
        apiCredentials: {
          clientId: "",
          clientSecret: "",
          accessToken: "",
        },
        jobPostingDetails: {
          title: "",
          description: "",
          location: "",
          employmentType: "full_time",
          experienceLevel: "mid_senior",
        },
        searchCriteria: {
          keywords: "",
          location: "",
          industry: "",
          currentCompany: "",
          pastCompany: "",
          school: "",
        },
        maxResults: 100,
        useCompanyPage: true,
        trackAnalytics: true,
        rateLimitDelay: 1000,
      };
    case "job-board-integration":
      return {
        jobBoard: "indeed",
        customJobBoard: {
          name: "",
          apiUrl: "",
          authMethod: "api_key",
        },
        credentials: {
          apiKey: "",
          username: "",
          password: "",
          clientId: "",
          clientSecret: "",
        },
        jobDetails: {
          title: "",
          description: "",
          location: "",
          salaryRange: "",
          employmentType: "full_time",
          category: "",
          requirements: "",
          benefits: "",
        },
        postingOptions: {
          duration: 30,
          featured: false,
          urgentHiring: false,
          autoRenew: false,
        },
        syncSettings: {
          syncApplications: true,
          syncFrequency: "hourly",
          duplicateHandling: "skip",
        },
        trackingSettings: {
          trackViews: true,
          trackApplications: true,
          utmParameters: "",
        },
      };
    case "ats-integration":
      return {
        atsSystem: "greenhouse",
        customATS: {
          name: "",
          apiUrl: "",
          version: "",
        },
        connectionSettings: {
          apiKey: "",
          username: "",
          password: "",
          tenantId: "",
          environment: "production",
        },
        syncDirection: "bidirectional",
        dataMapping: [],
        syncFrequency: "hourly",
        conflictResolution: "newest_wins",
        errorHandling: {
          retryAttempts: 3,
          retryDelay: 60,
          notifyOnError: true,
          errorEmail: "",
        },
      };
    case "calendar-integration":
      return {
        calendarSystem: "google_calendar",
        authSettings: {
          clientId: "",
          clientSecret: "",
          redirectUri: "",
          scopes: ["calendar.read", "calendar.write"],
        },
        defaultCalendar: "primary",
        meetingDefaults: {
          duration: 60,
          location: "",
          meetingType: "video_call",
          bufferTime: 15,
        },
        availabilitySettings: {
          workingHours: {
            start: "09:00",
            end: "17:00",
          },
          workingDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
          timezone: "America/New_York",
          lookAheadDays: 30,
        },
        notificationSettings: {
          sendInvites: true,
          reminderMinutes: [15, 60],
          includeAgenda: true,
          includeJoinInfo: true,
        },
        syncSettings: {
          syncFrequency: "every_15_min",
          syncDirection: "bidirectional",
          conflictResolution: "calendar_wins",
        },
      };

    default:
      return {};
  }
}
