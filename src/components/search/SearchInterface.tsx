import { useState, useEffect, use<PERSON>emo, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { SearchResults } from "@/components/search/SearchResults";
import { SearchSuggestions } from "@/components/search/SearchSuggestions";
import { SavedSearches } from "@/components/search/SavedSearches";
import { AdvancedSearch } from "@/components/search/AdvancedSearch";
import {
  Search as SearchIcon,
  SlidersHorizontal,
  BookmarkPlus,
  HelpCircle,
  Sparkles,
  Loader2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { SearchFilters as SearchFiltersType } from "@/types/search";
import { generateText } from "@/utils/gemini";
import { useCandidates } from "@/hooks/useCandidates";
import { useJobs } from "@/hooks/useJobs";
import {
  useSavedSearches,
  useCreateSavedSearch,
  useDeleteSavedSearch,
  SavedSearch,
} from "@/hooks/useSearchHistory";
import { getCombinedSearchResults } from "@/utils/optimizedSearch";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export function SearchInterface() {
  const [query, setQuery] = useState("");
  const [category, setCategory] = useState("all");
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [filters, setFilters] = useState<SearchFiltersType>({});
  const [isGeneratingAISearch, setIsGeneratingAISearch] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Get real-time data using unified hooks
  const { data: candidates = [], isLoading: candidatesLoading } =
    useCandidates();
  const { data: jobs = [], isLoading: jobsLoading } = useJobs();
  const { data: savedSearches = [], setRecords: setSavedSearches } =
    useSavedSearches();
  const createSavedSearch = useCreateSavedSearch();
  const deleteSavedSearch = useDeleteSavedSearch();

  // Optimized search results using the new search utility
  const allResults = useMemo(() => {
    return getCombinedSearchResults(candidates, jobs, query, filters);
  }, [candidates, jobs, query, filters]);

  // Separate results for category filtering
  const candidateResults = allResults.candidates;
  const jobResults = allResults.jobs;

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.length < 3 && query.length > 0) {
      toast({
        title: "Search query too short",
        description: "Please enter at least 3 characters to search",
        variant: "destructive",
      });
      return;
    }
  };

  const handleAISearch = async () => {
    setIsGeneratingAISearch(true);
    try {
      const prompt = `
Generate ONLY a search query string for finding candidates or job postings. Do NOT include any conversational text, explanations, or formatting.

Examples of good search queries:
- "React developers with 5+ years experience"
- "remote software engineers"
- "healthcare technology jobs"
- "DevOps engineer with Kubernetes experience"

Return only the search query string, nothing else.
`;

      const systemPrompt =
        "You are an expert recruiter. Generate only a concise search query string. Do not include any explanatory text, formatting, or conversational elements. Return only the search terms.";

      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);

      // Set the search query
      setQuery(response.trim());

      toast({
        title: "AI Search Generated",
        description:
          "A search query has been generated based on common recruitment needs.",
      });
    } catch (error) {
      console.error("Error generating AI search:", error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate AI search query. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingAISearch(false);
    }
  };

  const handleSaveSearch = useCallback(async () => {
    if (!query.trim()) {
      toast({
        title: "No search to save",
        description: "Please enter a search query first",
        variant: "destructive",
      });
      return;
    }

    try {
      const newSearch = await createSavedSearch.mutateAsync({
        name: query,
        query,
        filters,
        category,
      });
      // Optimistically add the new saved search to the UI
      if (setSavedSearches && newSearch) {
        setSavedSearches((prev) => {
          // Avoid duplicates if the real-time event is fast
          if (prev.some((search) => search.id === newSearch.id)) return prev;
          return [newSearch, ...prev];
        });
      }
    } catch (error) {
      // Error is handled by the mutation hook
    }
  }, [query, filters, category, createSavedSearch, setSavedSearches, toast]);

  const getResultsForCategory = () => {
    switch (category) {
      case "candidates":
        return {
          results: candidateResults || [],
          isLoading: candidatesLoading,
        };
      case "jobs":
        return { results: jobResults || [], isLoading: jobsLoading };
      case "all":
      default:
        return {
          results: [...candidateResults, ...jobResults],
          isLoading: candidatesLoading || jobsLoading,
        };
    }
  };

  const { results, isLoading } = getResultsForCategory();

  // Save search history when a meaningful search is performed
  useEffect(() => {
    const saveSearchHistory = async () => {
      if (!user || !query || query.length < 3) return;
      
      try {
        await supabase.from("search_history").insert({
          user_id: user.id,
          query,
          filters,
          category,
          result_count: results.length,
        });
      } catch (error) {
        console.error("Error saving search history:", error);
      }
    };

    // Debounce to avoid saving every keystroke
    const timeoutId = setTimeout(() => {
      if (query && query.length >= 3) {
        saveSearchHistory();
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [query, user, filters, category, results.length]);

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col space-y-2 sm:space-y-4">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
          Universal Search
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Search across candidates, jobs, and more using our powerful search
          engine.
        </p>
      </div>

      <div className="flex gap-2 sm:gap-4">
        <SavedSearches
          searches={savedSearches}
          onSelect={(search) => {
            setQuery(search.query);
            setFilters(search.filters);
          }}
          onDelete={(id) => {
            // Optimistic update: remove from UI immediately
            if (setSavedSearches) {
              setSavedSearches((prev) =>
                prev.filter((search) => search.id !== id),
              );
            }
            // Then perform the actual delete
            deleteSavedSearch.mutate(id);
          }}
        />
      </div>

      <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1 min-w-0">
          <SearchIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search candidates, jobs, skills, locations..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-9 pr-20 sm:pr-24"
          />
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-12 sm:right-14 inset-y-0 my-auto h-8 w-8"
            type="button"
            onClick={handleAISearch}
            disabled={isGeneratingAISearch}
            title="Generate AI Search"
          >
            {isGeneratingAISearch ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="h-4 w-4" />
            )}
          </Button>
          <HoverCard>
            <HoverCardTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 inset-y-0 my-auto h-8 w-8"
                type="button"
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </HoverCardTrigger>
            <HoverCardContent className="w-72 sm:w-80">
              <div className="space-y-2">
                <h4 className="font-medium">Search Tips</h4>
                <div className="text-sm">
                  <ul className="space-y-1 list-disc list-inside">
                    <li>Search names, roles, locations, and skills</li>
                    <li>Use advanced filters for precise results</li>
                    <li>Save frequently used searches</li>
                    <li>Switch between tabs to focus your search</li>
                  </ul>
                </div>
              </div>
            </HoverCardContent>
          </HoverCard>
        </div>
        <div className="flex gap-2 shrink-0">
          <Button
            variant="outline"
            size="icon"
            type="button"
            onClick={handleSaveSearch}
            title="Save Search"
            className="shrink-0"
          >
            <BookmarkPlus className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="shrink-0"
          >
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </form>

      {showAdvanced && (
        <AdvancedSearch filters={filters} onChange={setFilters} />
      )}

      <Tabs defaultValue="all" className="w-full" onValueChange={setCategory}>
        <TabsList className="grid w-full grid-cols-3 sm:w-auto sm:grid-cols-none sm:flex">
          <TabsTrigger value="all" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">All </span>(
            {allResults.total || 0})
          </TabsTrigger>
          <TabsTrigger value="candidates" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Candidates </span>(
            {candidateResults.length || 0})
          </TabsTrigger>
          <TabsTrigger value="jobs" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Jobs </span>(
            {jobResults.length || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <SearchResults
            results={results}
            isLoading={isLoading}
            category="all"
            query={query}
          />
        </TabsContent>
        <TabsContent value="candidates">
          <SearchResults
            results={candidateResults}
            isLoading={candidatesLoading}
            category="candidates"
            query={query}
          />
        </TabsContent>
        <TabsContent value="jobs">
          <SearchResults
            results={jobResults}
            isLoading={jobsLoading}
            category="jobs"
            query={query}
          />
        </TabsContent>
      </Tabs>

      {!query && <SearchSuggestions onSelect={setQuery} />}
    </div>
  );
}
