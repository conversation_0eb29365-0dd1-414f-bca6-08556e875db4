import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import {
  MapPin,
  Briefcase,
  Mail,
  Phone,
  DollarSign,
  Calendar,
  Users,
  Star,
  Clock,
  MessageSquare,
} from "lucide-react";
import { CandidateType } from "@/types/candidate";
import { Job } from "@/hooks/useJobs";
import { highlightSearchResults, HighlightedText } from "@/utils/search";
import { JobDetailsDialog } from "@/components/job/JobDetailsDialog";

interface SearchResultsProps {
  results: (CandidateType | Job)[];
  isLoading: boolean;
  category: string;
  query?: string;
}

function isCandidateType(item: CandidateType | Job): item is CandidateType {
  return "name" in item && "role" in item;
}

function isJobType(item: CandidateType | Job): item is Job {
  return "title" in item && "department" in item;
}

export function SearchResults({
  results,
  isLoading,
  category,
  query = "",
}: SearchResultsProps) {
  const navigate = useNavigate();
  const [selectedJobDetails, setSelectedJobDetails] = useState<Job | null>(null);

  // Mock applicant counts - in a real app, this would come from the backend
  const applicantCounts = new Map<string, number>();

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Searching...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (results.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          {query
            ? "No results found. Try adjusting your search terms or filters."
            : "Enter a search query to see results."}
        </CardContent>
      </Card>
    );
  }

  const handleItemClick = (item: CandidateType | Job) => {
    if (isCandidateType(item)) {
      navigate(`/candidates/${item.id}`);
    } else if (isJobType(item)) {
      // For jobs, show the job details dialog instead of navigating
      const jobDetails = {
        ...item,
        company: "Your Company",
        match: 85,
        requirements: item.requirements || [
          `${item.experience_required || "No specific"} experience`,
          "Strong communication skills",
          "Team collaboration",
          "Problem solving abilities",
        ],
        benefits: item.benefits || [
          "Health insurance",
          "Flexible work hours",
          "Professional development",
          "Competitive salary",
        ],
        postedDate: new Date(item.created_at).toLocaleDateString(),
        salary: item.salary_range || "Competitive salary",
        applicants: item.applicant_count,
      };
      setSelectedJobDetails(jobDetails);
    }
  };

  const renderHighlightedText = (text: string, query: string) => {
    const highlightedSegments = highlightSearchResults(text, query);
    
    return (
      <>
        {highlightedSegments.map((segment, index) => (
          <span
            key={index}
            className={segment.isHighlighted ? "bg-yellow-200 dark:bg-yellow-800" : ""}
          >
            {segment.text}
          </span>
        ))}
      </>
    );
  };

  return (
    <div>
      <div className="space-y-4">
        {results.map((item) => (
          <Card
            key={item.id}
            className="hover:bg-accent/50 transition-colors cursor-pointer"
            onClick={() => handleItemClick(item)}
          >
            <CardContent className="p-6 relative">
              {isCandidateType(item) ? (
                <div className="flex flex-col gap-4">
                  <div className="flex items-start space-x-3 sm:space-x-4 flex-1">
                    <Avatar className="h-12 w-12 sm:h-16 sm:w-16 shrink-0">
                      <AvatarImage src={item.avatar} />
                      <AvatarFallback>{item.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <Link
                        to={`/candidates/${item.id}`}
                        className="font-medium text-base sm:text-lg hover:underline block"
                      >
                        {renderHighlightedText(item.name, query)}
                      </Link>
                      <p className="text-sm sm:text-base text-muted-foreground">
                        {renderHighlightedText(item.role, query)}
                      </p>
                      <div className="flex flex-wrap items-center gap-2 sm:gap-4 mt-2 text-xs sm:text-sm text-muted-foreground">
                        <div className="flex items-center gap-1.5">
                          <MapPin className="w-3 h-3 sm:w-4 sm:w-4" />
                          <span className="truncate">
                            {renderHighlightedText(item.location, query)}
                          </span>
                        </div>
                        {item.experience && (
                          <div className="flex items-center gap-1.5">
                            <Briefcase className="w-3 h-3 sm:w-4 sm:w-4" />
                            <span className="truncate">{item.experience}</span>
                          </div>
                        )}
                        {item.email && (
                          <div className="flex items-center gap-1.5 hidden sm:flex">
                            <Mail className="w-4 h-4" />
                            <span className="truncate">{item.email}</span>
                          </div>
                        )}
                        {item.phone && (
                          <div className="flex items-center gap-1.5 hidden sm:flex">
                            <Phone className="w-4 h-4" />
                            <span className="truncate">{item.phone}</span>
                          </div>
                        )}
                      </div>
                      {/* Tags */}
                      {item.normalized_tags && item.normalized_tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 sm:gap-2 mt-3">
                          {item.normalized_tags.slice(0, 3).map((tag, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-xs px-2 py-1"
                            >
                              {tag.name}
                            </Badge>
                          ))}
                          {item.normalized_tags.length > 3 && (
                            <Badge
                              variant="outline"
                              className="text-xs px-2 py-1"
                            >
                              +{item.normalized_tags.length - 3} more
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : isJobType(item) ? (
                <div className="flex flex-col gap-4">
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-base sm:text-lg">
                      {renderHighlightedText(item.title, query)}
                    </div>
                    <p className="text-sm sm:text-base text-muted-foreground">
                      {renderHighlightedText(item.department, query)}
                    </p>
                    <div className="flex flex-wrap items-center gap-2 sm:gap-4 mt-2 text-xs sm:text-sm text-muted-foreground">
                      <div className="flex items-center gap-1.5">
                        <MapPin className="w-3 h-3 sm:w-4 sm:w-4" />
                        <span className="truncate">
                          {renderHighlightedText(item.location, query)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Briefcase className="w-3 h-3 sm:w-4 sm:w-4" />
                        <span className="truncate">{item.job_type}</span>
                      </div>
                      {item.salary_range && (
                        <div className="flex items-center gap-1.5 hidden sm:flex">
                          <DollarSign className="w-4 h-4" />
                          <span className="truncate">{item.salary_range}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1.5">
                        <Calendar className="w-3 h-3 sm:w-4 sm:w-4" />
                        <span className="truncate">
                          Posted{" "}
                          {new Date(item.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Users className="w-3 h-3 sm:w-4 sm:w-4" />
                        <span className="truncate">
                          {applicantCounts?.get(item.id) || 0} applicants
                        </span>
                      </div>
                    </div>
                    <div className="mt-3 flex flex-wrap gap-1 sm:gap-2">
                      {item.is_urgent && (
                        <Badge className="bg-red-100 text-red-800">
                          Urgent
                        </Badge>
                      )}
                      {item.is_active ? (
                        <Badge className="bg-green-100 text-green-800">
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="outline">Inactive</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ) : null}
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedJobDetails && (
        <JobDetailsDialog
          job={selectedJobDetails}
          onClose={() => setSelectedJobDetails(null)}
        />
      )}
    </div>
  );
}
