import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { X, Plus, Check, ChevronDown, Wand2, Loader2 } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { generateText } from "@/utils/gemini";

interface SearchFilters {
  location?: string;
  experience?: string;
  skills?: Array<{
    name: string;
    proficiency?: "beginner" | "intermediate" | "expert" | "any";
  }>;
  tags?: string[];
  visa_status?: string;
  remote_preference?: string;
}

interface AdvancedSearchProps {
  filters: SearchFilters;
  onChange: (filters: SearchFilters) => void;
}

interface Skill {
  id: string;
  name: string;
  category: string;
}

export function AdvancedSearch({ filters, onChange }: AdvancedSearchProps) {
  const [skillSearchValue, setSkillSearchValue] = useState("");
  const [skillSearchOpen, setSkillSearchOpen] = useState(false);
  const [availableSkills, setAvailableSkills] = useState<Skill[]>([]);
  const [isLoadingSkills, setIsLoadingSkills] = useState(false);
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);
  const commandListRef = useRef<HTMLDivElement>(null);

  const fetchSkills = async () => {
    setIsLoadingSkills(true);
    try {
      const response = await fetch("/api/skills");
      if (response.ok) {
        const skills = await response.json();
        setAvailableSkills(skills);
      }
    } catch (error) {
      console.error("Failed to fetch skills:", error);
    } finally {
      setIsLoadingSkills(false);
    }
  };

  useEffect(() => {
    fetchSkills();
  }, []);

  const generateAISuggestions = async () => {
    setIsGeneratingSuggestions(true);
    try {
      const prompt = `Based on the current search filters: ${JSON.stringify(
        filters,
      )}, suggest 3-5 additional search criteria that would help find better candidates. Return only the suggestions as a JSON array of strings.`;

      const suggestions = await generateText(prompt);
      const parsedSuggestions = JSON.parse(suggestions);

      // Apply the first suggestion as an example
      if (parsedSuggestions.length > 0) {
        const firstSuggestion = parsedSuggestions[0];
        // You can implement logic here to apply the suggestion
        console.log("AI Suggestion:", firstSuggestion);
      }
    } catch (error) {
      console.error("Failed to generate suggestions:", error);
    } finally {
      setIsGeneratingSuggestions(false);
    }
  };

  const handleAddSkill = (skillName: string, skillId?: string) => {
    if (
      !filters.skills?.some((s) => s.name === skillName) &&
      skillName.trim()
    ) {
      const newSkill = {
        name: skillName,
        proficiency: "any" as const,
      };
      onChange({
        ...filters,
        skills: [...(filters.skills || []), newSkill],
      });
      setSkillSearchValue("");
    }
  };

  const handleRemoveSkill = (skillName: string) => {
    onChange({
      ...filters,
      skills: filters.skills?.filter((s) => s.name !== skillName) || [],
    });
  };

  const handleSkillProficiencyChange = (
    skillName: string,
    proficiency: "beginner" | "intermediate" | "expert" | "any",
  ) => {
    onChange({
      ...filters,
      skills:
        filters.skills?.map((s) =>
          s.name === skillName ? { ...s, proficiency } : s,
        ) || [],
    });
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (commandListRef.current) {
      commandListRef.current.scrollTop += e.deltaY;
    }
  };

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-background">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Advanced Search</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={generateAISuggestions}
          disabled={isGeneratingSuggestions}
        >
          {isGeneratingSuggestions ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Wand2 className="h-4 w-4 mr-2" />
          )}
          AI Suggestions
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="location">Location</Label>
          <Input
            id="location"
            placeholder="Enter location..."
            value={filters.location || ""}
            onChange={(e) =>
              onChange({ ...filters, location: e.target.value })
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="experience">Experience Level</Label>
          <Select
            value={filters.experience || ""}
            onValueChange={(value) =>
              onChange({ ...filters, experience: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select experience level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="entry">Entry Level (0-2 years)</SelectItem>
              <SelectItem value="mid">Mid Level (3-5 years)</SelectItem>
              <SelectItem value="senior">Senior Level (6+ years)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Skills</Label>
        <Popover open={skillSearchOpen} onOpenChange={setSkillSearchOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={skillSearchOpen}
              className="w-full justify-between"
            >
              Search skills...
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <Command>
              <CommandInput
                placeholder="Search skills..."
                value={skillSearchValue}
                onValueChange={setSkillSearchValue}
                onKeyDown={(e) => {
                  if (
                    e.key === "Enter" &&
                    skillSearchValue &&
                    !availableSkills.some(
                      (s) => s.name === skillSearchValue,
                    )
                  ) {
                    e.preventDefault();
                    handleAddSkill(skillSearchValue);
                  }
                }}
              />
              <CommandList 
                ref={commandListRef}
                className="max-h-[280px] overflow-y-auto overflow-x-hidden px-2 pb-2"
                onWheel={handleWheel}
              >
                <CommandEmpty>
                  {skillSearchValue ? (
                    <div className="p-4 text-sm">
                      No skill found. Press Enter to add "{skillSearchValue}
                      " as a custom skill.
                    </div>
                  ) : (
                    "No skills found."
                  )}
                </CommandEmpty>
                {isLoadingSkills ? (
                  <div className="p-4 text-sm text-center">
                    <Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
                    Loading skills...
                  </div>
                ) : (
                  Object.entries(
                    availableSkills.reduce(
                      (acc, skill) => {
                        if (!acc[skill.category]) {
                          acc[skill.category] = [];
                        }
                        if (
                          !skillSearchValue ||
                          skill.name
                            .toLowerCase()
                            .includes(skillSearchValue.toLowerCase())
                        ) {
                          acc[skill.category].push(skill);
                        }
                        return acc;
                      },
                      {} as Record<string, Skill[]>,
                    ),
                  ).map(
                    ([category, skills]) =>
                      skills.length > 0 && (
                        <CommandGroup key={category} heading={category}>
                          {skills.map((skill) => (
                            <CommandItem
                              key={skill.id}
                              value={skill.name}
                              onSelect={() => {
                                handleAddSkill(skill.name, skill.id);
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  filters.skills?.some(
                                    (s) => s.name === skill.name,
                                  )
                                    ? "opacity-100"
                                    : "opacity-0",
                                )}
                              />
                              {skill.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      ),
                  )
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <div className="flex gap-2">
          {skillSearchValue &&
            !availableSkills.some((s) => s.name === skillSearchValue) && (
              <Button
                onClick={() => handleAddSkill(skillSearchValue)}
                size="sm"
                variant="secondary"
                className="shrink-0"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add "{skillSearchValue}"
              </Button>
            )}
        </div>
      </div>
      <div className="flex flex-wrap gap-2 mt-2">
        {filters.skills?.map((skill) => (
          <div
            key={skill.name}
            className="flex flex-col sm:flex-row items-start sm:items-center gap-2 bg-secondary p-2 rounded-md min-w-0"
          >
            <span className="truncate text-sm">{skill.name}</span>
            <Select
              value={skill.proficiency || "any"}
              onValueChange={(value: any) =>
                handleSkillProficiencyChange(skill.name, value)
              }
            >
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any Level</SelectItem>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="expert">Expert</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveSkill(skill.name)}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="visa">Visa Status</Label>
          <Select
            value={filters.visa_status || ""}
            onValueChange={(value) =>
              onChange({ ...filters, visa_status: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select visa status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="us_citizen">US Citizen</SelectItem>
              <SelectItem value="green_card">Green Card Holder</SelectItem>
              <SelectItem value="h1b">H1B Visa</SelectItem>
              <SelectItem value="opt">OPT/CPT</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="remote">Remote Preference</Label>
          <Select
            value={filters.remote_preference || ""}
            onValueChange={(value) =>
              onChange({ ...filters, remote_preference: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select remote preference" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="onsite">On-site Only</SelectItem>
              <SelectItem value="hybrid">Hybrid</SelectItem>
              <SelectItem value="remote">Remote Only</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Tags</Label>
        <div className="flex flex-wrap gap-2">
          {filters.tags?.map((tag) => (
            <Badge
              key={tag}
              variant="secondary"
              className="flex items-center gap-1"
            >
              {tag}
              <button
                type="button"
                onClick={() =>
                  onChange({
                    ...filters,
                    tags: filters.tags?.filter((t) => t !== tag) || [],
                  })
                }
                className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}
