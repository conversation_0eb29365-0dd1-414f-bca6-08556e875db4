import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";

interface CandidateFiltersProps {
  filters: any;
  onFiltersChange: (filters: any) => void;
}

export function CandidateFilters({
  filters,
  onFiltersChange,
}: CandidateFiltersProps) {
  const [localFilters, setLocalFilters] = useState({
    experienceLevel: "",
    locationPreference: "",
    skills: [] as string[],
    relationshipScore: [70],
    ...filters,
  });

  const handleSkillToggle = (skill: string, checked: boolean) => {
    const updatedSkills = checked
      ? [...localFilters.skills, skill]
      : localFilters.skills.filter((s) => s !== skill);

    setLocalFilters((prev) => ({ ...prev, skills: updatedSkills }));
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      experienceLevel: "",
      locationPreference: "",
      skills: [],
      relationshipScore: [70],
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="space-y-6 pb-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Experience Level</CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={localFilters.experienceLevel}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({ ...prev, experienceLevel: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select experience level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="junior">Junior (0-2 years)</SelectItem>
                <SelectItem value="mid">Mid-level (3-5 years)</SelectItem>
                <SelectItem value="senior">Senior (6-10 years)</SelectItem>
                <SelectItem value="lead">Lead (10+ years)</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Location Preference</CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={localFilters.locationPreference}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  locationPreference: value,
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select location preference" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="remote">Remote</SelectItem>
                <SelectItem value="hybrid">Hybrid</SelectItem>
                <SelectItem value="onsite">On-site</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Skills</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {["React", "TypeScript", "Node.js", "Python", "Java", "AWS"].map(
              (skill) => (
                <div key={skill} className="flex items-center space-x-2">
                  <Checkbox
                    id={skill}
                    checked={localFilters.skills.includes(skill)}
                    onCheckedChange={(checked) =>
                      handleSkillToggle(skill, checked as boolean)
                    }
                  />
                  <Label htmlFor={skill}>{skill}</Label>
                </div>
              ),
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Relationship Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Slider
                value={localFilters.relationshipScore}
                onValueChange={(value) =>
                  setLocalFilters((prev) => ({
                    ...prev,
                    relationshipScore: value,
                  }))
                }
                max={100}
                step={1}
              />
              <div className="text-sm text-muted-foreground">
                Minimum score: {localFilters.relationshipScore[0]}%
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={handleClearFilters}
          >
            Clear Filters
          </Button>
          <Button className="flex-1" onClick={handleApplyFilters}>
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
}
