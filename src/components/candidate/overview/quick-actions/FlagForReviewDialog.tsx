import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Flag } from "lucide-react";

export function FlagForReviewDialog() {
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [flagReason, setFlagReason] = useState("");

  const handleFlagForReview = () => {
    if (flagReason.trim()) {
      toast({
        title: "Candidate Flagged",
        description: "The candidate has been flagged for review.",
      });
      setOpen(false);
      setFlagReason("");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button
        variant="outline"
        size="sm"
        className="hover:bg-gradient-to-r from-[#7E69AB] to-[#403E43] hover:text-white transition-all duration-300"
        onClick={() => setOpen(true)}
      >
        <Flag className="w-4 h-4 mr-2" />
        Flag for Review
      </Button>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Flag for Review</DialogTitle>
          <DialogDescription>
            Flag this candidate for review by the team
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Reason</label>
            <Select onValueChange={setFlagReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="skills">Skills Verification</SelectItem>
                <SelectItem value="experience">
                  Experience Verification
                </SelectItem>
                <SelectItem value="background">Background Check</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleFlagForReview}>Flag</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
