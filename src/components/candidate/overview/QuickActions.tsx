import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Star, Mail, Phone, Clock } from "lucide-react";
import { AddNoteDialog } from "./quick-actions/AddNoteDialog";
import { ScheduleMeetingDialog } from "./quick-actions/ScheduleMeetingDialog";
import { FlagForReviewDialog } from "./quick-actions/FlagForReviewDialog";
import { SetAlertDialog } from "./quick-actions/SetAlertDialog";

interface QuickActionsProps {
  candidateId: string;
}

export function QuickActions({ candidateId }: QuickActionsProps) {
  const { toast } = useToast();

  return (
    <div className="grid grid-cols-2 gap-1">
      <AddNoteDialog candidateId={candidateId} />
      <ScheduleMeetingDialog candidateId={candidateId} />
      <FlagForReviewDialog />
      <SetAlertDialog />

      <Button
        variant="outline"
        size="sm"
        className="hover:bg-gradient-to-r from-[#7E69AB] to-[#403E43] hover:text-white transition-all duration-300"
        onClick={() =>
          toast({
            title: "Candidate Shortlisted",
            description: "The candidate has been added to your shortlist.",
          })
        }
      >
        <Star className="w-4 h-4 mr-2" />
        Shortlist
      </Button>

      <Button
        variant="outline"
        size="sm"
        className="hover:bg-gradient-to-r from-[#0EA5E9] to-[#221F26] hover:text-white transition-all duration-300"
        onClick={() =>
          toast({
            title: "Email Composer",
            description: "Opening email composer...",
          })
        }
      >
        <Mail className="w-4 h-4 mr-2" />
        Send Email
      </Button>

      <Button
        variant="outline"
        size="sm"
        className="hover:bg-gradient-to-r from-[#6E59A5] to-[#1A1F2C] hover:text-white transition-all duration-300"
        onClick={() =>
          toast({
            title: "Call Scheduler",
            description: "Opening call scheduler...",
          })
        }
      >
        <Phone className="w-4 h-4 mr-2" />
        Schedule Call
      </Button>

      <Button
        variant="outline"
        size="sm"
        className="hover:bg-gradient-to-r from-[#8E9196] to-[#403E43] hover:text-white transition-all duration-300"
        onClick={() =>
          toast({
            title: "History",
            description: "Opening candidate history...",
          })
        }
      >
        <Clock className="w-4 h-4 mr-2" />
        View History
      </Button>
    </div>
  );
}
