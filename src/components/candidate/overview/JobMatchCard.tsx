import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  MapPin,
  Building,
  Clock,
  CheckCircle2,
  <PERSON>rkles,
  Loader2,
} from "lucide-react";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useState, useEffect } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";

interface JobMatchCardProps {
  job: {
    id: string;
    title: string;
    company: string;
    location: string;
    location_name?: string;
    match: number;
    status: string;
    requirements?: string[];
    description?: string;
  };
  candidate?: {
    skills?: string[];
    experience?: string;
    preferences?: string[];
  };
  onClick: () => void;
}

export function JobMatchCard({ job, candidate, onClick }: JobMatchCardProps) {
  const { toast } = useToast();
  const [aiMatchScore, setAiMatchScore] = useState<number | null>(null);
  const [aiInsights, setAiInsights] = useState<string>("");
  const [isCalculatingMatch, setIsCalculatingMatch] = useState(false);
  const getMatchColor = (match: number) => {
    if (match >= 90) return "text-green-500";
    if (match >= 70) return "text-yellow-500";
    return "text-orange-500";
  };

  const calculateAIMatchScore = async () => {
    if (!candidate || isCalculatingMatch) return;

    setIsCalculatingMatch(true);
    try {
      const prompt = `
Analyze the compatibility between this candidate and job opportunity:

Job Details:
- Title: ${job.title}
- Company: ${job.company}
- Location: ${job.location_name || job.location}
- Requirements: ${job.requirements?.join(", ") || "Not specified"}
- Description: ${job.description || "Not provided"}

Candidate Profile:
- Skills: ${candidate.skills?.join(", ") || "Not specified"}
- Experience: ${candidate.experience || "Not specified"}
- Preferences: ${candidate.preferences?.join(", ") || "Not specified"}

Provide:
1. A compatibility score from 0-100
2. Brief analysis of strengths and potential concerns
3. Key matching factors

Return in this JSON format:
{
  "score": 85,
  "analysis": "Strong technical match with relevant experience. Location preference aligns well. Minor concern about specific framework experience."
}
`;

      const systemPrompt =
        "You are an expert recruitment consultant specializing in candidate-job matching. Provide accurate, objective assessments based on skills, experience, and job requirements.";

      const response = await generateText(prompt, systemPrompt);
      const cleanedResponse = response
        .replace(/```json\s*/, "")
        .replace(/```\s*$/, "")
        .trim();
      const result = JSON.parse(cleanedResponse);

      if (result.score && result.analysis) {
        setAiMatchScore(result.score);
        setAiInsights(result.analysis);

        toast({
          title: "AI Match Analysis Complete",
          description: `AI calculated a ${result.score}% compatibility score.`,
        });
      }
    } catch (error) {
      console.error("Error calculating AI match score:", error);
      toast({
        title: "Analysis Unavailable",
        description: "Unable to calculate AI match score at the moment.",
        variant: "destructive",
      });
    } finally {
      setIsCalculatingMatch(false);
    }
  };

  return (
    <div
      onClick={onClick}
      className="group flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 cursor-pointer transition-all"
    >
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <h4 className="font-medium group-hover:text-primary transition-colors">
            {job.title}
          </h4>
          {job.match >= 90 && (
            <Tooltip>
              <TooltipTrigger>
                <CheckCircle2 className="w-4 h-4 text-green-500" />
              </TooltipTrigger>
              <TooltipContent>Perfect Match</TooltipContent>
            </Tooltip>
          )}
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <MapPin className="w-4 h-4" />
            {job.location_name || job.location}
          </div>
          <div className="flex items-center gap-1">
            <Building className="w-4 h-4" />
            {job.company}
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            {job.status}
          </div>
        </div>
      </div>
      <div className="text-right space-y-2">
        <div
          className={`text-lg font-semibold ${getMatchColor(aiMatchScore || job.match)}`}
        >
          {aiMatchScore !== null ? `${aiMatchScore}%` : `${job.match}%`}
        </div>
        <Progress
          value={aiMatchScore || job.match}
          className="w-20 h-1.5 mt-1"
        />

        {candidate && (
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              calculateAIMatchScore();
            }}
            disabled={isCalculatingMatch}
            className="text-xs"
          >
            {isCalculatingMatch ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                AI Analysis
              </>
            ) : (
              <>
                <Sparkles className="h-3 w-3 mr-1" />
                AI Match
              </>
            )}
          </Button>
        )}

        {aiInsights && (
          <Tooltip>
            <TooltipTrigger>
              <div className="text-xs text-blue-600 cursor-help">
                AI Insights
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm">
              <p className="text-sm">{aiInsights}</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  );
}
