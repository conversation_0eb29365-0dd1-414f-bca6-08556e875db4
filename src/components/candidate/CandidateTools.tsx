import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ResumeParser } from "@/components/candidate/ResumeParser";
import { InterviewAssistant } from "@/components/ai/InterviewAssistant";
import { Button } from "@/components/ui/button";
import { Calendar, Mail, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCandidates } from "@/hooks/useCandidates";
import { generateCSV } from "@/utils/reportGenerators";

import { BulkEmailDialog } from "@/components/candidate/BulkEmailDialog";
import { BulkInterviewDialog } from "@/components/candidate/BulkInterviewDialog";

export function CandidateTools() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("resume-parser");
  const [isBulkEmail<PERSON><PERSON>, setIsBulkEmailOpen] = useState(false);
  const [isBulkInterviewOpen, setIsBulkInterviewOpen] = useState(false);
  const { data: candidates = [] } = useCandidates();

  const handleExportData = async () => {
    try {
      if (candidates.length === 0) {
        toast({
          title: "No Data to Export",
          description: "There are no candidates to export.",
          variant: "destructive",
        });
        return;
      }

      // Prepare data for export
      const exportData = {
        title: "Candidates Export",
        headers: [
          "Name",
          "Role",
          "Email",
          "Phone",
          "Location",
          "Experience",
          "Relationship Score",
          "Skills",
          "Tags",
        ],
        rows: candidates.map((candidate) => [
          candidate.name,
          candidate.role,
          candidate.email,
          candidate.phone || "N/A",
          candidate.location || "N/A",
          candidate.experience || "N/A",
          `${candidate.relationshipScore}%`,
          candidate.skills
            ?.map((s) => (typeof s === "string" ? s : s.name))
            .join(", ") || "N/A",
          candidate.normalized_tags 
            ? candidate.normalized_tags.map(tag => tag.name).join(", ")
            : candidate.tags?.join(", ") || "N/A",
        ]),
      };

      // Generate CSV
      const csvBlob = generateCSV(exportData);
      const csvUrl = window.URL.createObjectURL(csvBlob);
      const csvLink = document.createElement("a");
      csvLink.href = csvUrl;
      csvLink.download = `candidates-export-${new Date().toISOString().split("T")[0]}.csv`;
      csvLink.click();
      window.URL.revokeObjectURL(csvUrl);

      toast({
        title: "Export Complete",
        description: `Successfully exported ${candidates.length} candidates to CSV.`,
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error exporting the data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleBulkEmail = () => {
    if (candidates.length === 0) {
      toast({
        title: "No Candidates",
        description: "There are no candidates to send emails to.",
        variant: "destructive",
      });
      return;
    }
    setIsBulkEmailOpen(true);
  };

  const handleBulkInterview = () => {
    if (candidates.length === 0) {
      toast({
        title: "No Candidates",
        description: "There are no candidates to schedule interviews for.",
        variant: "destructive",
      });
      return;
    }
    setIsBulkInterviewOpen(true);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Candidate Management Tools</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="resume-parser">Resume Parser</TabsTrigger>
              <TabsTrigger value="interview-assistant">
                Interview Assistant
              </TabsTrigger>
              <TabsTrigger value="bulk-actions">Bulk Actions</TabsTrigger>
            </TabsList>

            <TabsContent value="resume-parser">
              <ResumeParser />
            </TabsContent>

            <TabsContent value="interview-assistant">
              <InterviewAssistant />
            </TabsContent>

            <TabsContent value="bulk-actions">
              <Card>
                <CardHeader>
                  <CardTitle>Bulk Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    Perform actions on multiple candidates at once to save time
                    and streamline your workflow.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button
                      variant="outline"
                      className="flex flex-col h-auto py-4 px-6 items-center justify-center gap-2"
                      onClick={handleBulkEmail}
                    >
                      <Mail className="h-8 w-8" />
                      <span className="font-medium">Bulk Email</span>
                      <span className="text-xs text-muted-foreground text-center">
                        Send emails to multiple candidates at once
                      </span>
                    </Button>

                    <Button
                      variant="outline"
                      className="flex flex-col h-auto py-4 px-6 items-center justify-center gap-2"
                      onClick={handleBulkInterview}
                    >
                      <Calendar className="h-8 w-8" />
                      <span className="font-medium">Bulk Interview</span>
                      <span className="text-xs text-muted-foreground text-center">
                        Schedule interviews for multiple candidates
                      </span>
                    </Button>

                    <Button
                      variant="outline"
                      className="flex flex-col h-auto py-4 px-6 items-center justify-center gap-2"
                      onClick={handleExportData}
                    >
                      <Download className="h-8 w-8" />
                      <span className="font-medium">Export Data</span>
                      <span className="text-xs text-muted-foreground text-center">
                        Export candidate data to CSV or Excel
                      </span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Bulk Action Dialogs */}
      <BulkEmailDialog
        open={isBulkEmailOpen}
        onOpenChange={setIsBulkEmailOpen}
        candidates={candidates}
      />

      <BulkInterviewDialog
        open={isBulkInterviewOpen}
        onOpenChange={setIsBulkInterviewOpen}
        candidates={candidates}
      />
    </>
  );
}
