import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { MessageSquare, Briefcase, UserCircle, Star, Edit } from "lucide-react";
import { CandidateType } from "@/types/candidate";

interface CandidateHeaderProps {
  candidate: CandidateType;
  onMessageClick: () => void;
  onAddToJobClick: () => void;
  onEditClick: () => void;
}

export function CandidateHeader({
  candidate,
  onMessageClick,
  onAddToJobClick,
  onEditClick,
}: CandidateHeaderProps) {
  return (
    <div className="flex items-start justify-between mb-8">
      <div className="flex items-center gap-4">
        <Avatar className="h-20 w-20">
          <AvatarImage src={candidate.avatar} alt={candidate.name} />
          <AvatarFallback>
            {candidate.name
              .split(" ")
              .map((n) => n[0])
              .join("")}
          </AvatarFallback>
        </Avatar>
        <div>
          <h1 className="text-3xl font-bold">{candidate.name}</h1>
          <p className="text-gray-600">{candidate.role}</p>
          {candidate.recruiter && candidate.recruiter.name && (
            <div className="flex items-center gap-1 sm:gap-2">
              <Star className="h-4 w-4" />
              <span className="text-sm text-gray-600">
                Engaged by {candidate.recruiter.name}
              </span>
            </div>
          )}
        </div>
      </div>
      <div className="flex gap-3">
        <Button variant="outline" onClick={onEditClick}>
          <Edit className="w-4 h-4 mr-2" />
          Edit
        </Button>
        <Button variant="outline" onClick={onMessageClick}>
          <MessageSquare className="w-4 h-4 mr-2" />
          Message
        </Button>
        <Button onClick={onAddToJobClick}>
          <Briefcase className="w-4 h-4 mr-2" />
          Add to Job
        </Button>
      </div>
    </div>
  );
}
