import { CandidateType } from "@/types/candidate";
import { MatchedJobsList } from "./overview/MatchedJobsList";

interface CandidateJobsProps {
  candidate: CandidateType;
}

export function CandidateJobs({ candidate }: CandidateJobsProps) {
  // Convert candidate's matchedJobs to the format expected by MatchedJobsList
  const convertedJobs = candidate.matchedJobs?.map((job) => ({
    id: job.id,
    title: job.title,
    company: job.company,
    location: job.location,
    match: job.match,
    description: job.description || "", // Now this property exists in the type
    department: job.department,
    job_type: "Full-time", // Default value
    status: job.status,
    salary_range: job.salary,
    requirements: job.requirements,
    benefits: job.benefits,
  }));

  return (
    <div className="space-y-6">
      <MatchedJobsList jobs={convertedJobs} />
    </div>
  );
}
