import { useCreateCandidate } from "@/hooks/useCreateCandidate";
import { useToast } from "@/hooks/use-toast";
import { useAddCandidateSkills } from "@/hooks/useSkills";
import { CandidateForm, CandidateFormData } from "./CandidateForm";
import { Tag } from "@/components/TagPicker";

interface AddCandidateFormProps {
  onSuccess?: () => void;
}

export function AddCandidateForm({ onSuccess }: AddCandidateFormProps) {
  const { toast } = useToast();
  const createCandidate = useCreateCandidate();
  const addCandidateSkills = useAddCandidateSkills();

  const handleSubmit = async (
    data: CandidateFormData,
    tags: Tag[],
    skills: { id: string; name: string; level: string; years: number }[],
  ) => {
    try {
      console.log("🚀 Form submission started with data:", data);

      // Clean and validate required fields
      if (!data.name?.trim() || !data.email?.trim() || !data.role?.trim()) {
        throw new Error("Name, email, and role are required fields");
      }

      // Ensure required fields are present and properly typed
      const candidateData = {
        name: data.name.trim(),
        email: data.email.trim(),
        role: data.role.trim(),
        phone: data.phone?.trim() || undefined,
        experience: data.experience?.trim() || undefined,
        industry: data.industry?.trim() || undefined,
        remotePreference: data.remotePreference?.trim() || undefined,
        visaStatus: data.visaStatus?.trim() || undefined,
        socialLinks: {
          linkedin: data.linkedinUrl?.trim() || undefined,
          github: data.githubUrl?.trim() || undefined,
        },
        aiSummary: data.aiSummary?.trim() || undefined,
        tags: tags.map((tag) => tag.name), // Keep string array for backward compatibility
        // Don't include skills in the candidate data anymore - they'll be added separately
      };

      console.log("🔄 Processed candidate data for submission:", candidateData);

      const result = await createCandidate.mutateAsync(candidateData);
      console.log("✅ Candidate creation mutation completed:", result);

      // Add skills to the candidate using the normalized approach
      if (skills.length > 0 && result.id) {
        console.log("🔄 Adding normalized skills to candidate:", skills);
        try {
          await addCandidateSkills.mutateAsync({
            candidateId: result.id,
            skills: skills.map((s) => ({
              skill_id: s.id,
              proficiency_level: s.level as
                | "beginner"
                | "intermediate"
                | "advanced"
                | "expert",
              years_experience: s.years,
            })),
          });
          console.log("✅ Skills added successfully");
        } catch (skillError) {
          console.error("❌ Failed to add skills:", skillError);
          // Don't throw here - candidate was created successfully
          toast({
            title: "Warning",
            description:
              "Candidate created but some skills couldn't be added. Please add them manually.",
            variant: "destructive",
          });
        }
      }

      // Call success callback
      onSuccess?.();

      toast({
        title: "Success",
        description: "Candidate has been added successfully!",
      });
    } catch (error) {
      console.error("❌ Form submission error:", error);
      toast({
        title: "Error",
        description: `Failed to create candidate: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
      throw error; // Re-throw to prevent form reset
    }
  };

  return (
    <CandidateForm
      mode="add"
      onSubmit={handleSubmit}
      isPending={createCandidate.isPending}
    />
  );
}
