import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X, Plus } from "lucide-react";
import { SkillPicker } from "./SkillPicker";
import { useSkills } from "@/hooks/useSkills";
import { TagPicker, Tag } from "@/components/TagPicker";
import { useTags, useCreateTag } from "@/hooks/useTags";
import { CandidateType } from "@/types/candidate";

const candidateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  role: z.string().min(2, "Role is required"),
  phone: z.string().optional(),
  experience: z.string().optional(),
  industry: z.string().optional(),
  remotePreference: z.string().optional(),
  visaStatus: z.string().optional(),
  linkedinUrl: z
    .string()
    .url("Invalid LinkedIn URL")
    .optional()
    .or(z.literal("")),
  githubUrl: z.string().url("Invalid GitHub URL").optional().or(z.literal("")),
  twitterUrl: z
    .string()
    .url("Invalid Twitter URL")
    .optional()
    .or(z.literal("")),
  aiSummary: z.string().optional(),
});

export type CandidateFormData = z.infer<typeof candidateSchema>;

export interface CandidateFormProps {
  mode: "add" | "edit";
  defaultValues?: CandidateType;
  onSubmit: (
    data: CandidateFormData,
    tags: Tag[],
    skills: { id: string; name: string; level: string; years: number }[],
  ) => Promise<void>;
  submitLabel?: string;
  isPending?: boolean;
}

export function CandidateForm({
  mode,
  defaultValues,
  onSubmit,
  submitLabel,
  isPending = false,
}: CandidateFormProps) {
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [skills, setSkills] = useState<
    { id: string; name: string; level: string; years: number }[]
  >([]);
  const [newSkillId, setNewSkillId] = useState<string>("");
  const [newSkillLevel, setNewSkillLevel] = useState("beginner");
  const [newSkillYears, setNewSkillYears] = useState(0);

  const { data: allSkills } = useSkills();
  const { data: availableTags = [] } = useTags();
  const createTag = useCreateTag();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<CandidateFormData>({
    resolver: zodResolver(candidateSchema),
    defaultValues:
      mode === "edit" && defaultValues
        ? {
            name: defaultValues.name,
            email: defaultValues.email,
            role: defaultValues.role,
            phone: defaultValues.phone || "",
            experience: defaultValues.experience || "",
            industry: defaultValues.industry || "",
            remotePreference: defaultValues.remotePreference || "",
            visaStatus: defaultValues.visaStatus || "",
            linkedinUrl: defaultValues.socialLinks?.linkedin || "",
            githubUrl: defaultValues.socialLinks?.github || "",
            twitterUrl: defaultValues.socialLinks?.twitter || "",
            aiSummary: defaultValues.aiSummary || "",
          }
        : undefined,
  });

  // Initialize tags and skills for edit mode
  useEffect(() => {
    if (mode === "edit" && defaultValues) {
      // Set tags
      const candidateTags: Tag[] =
        defaultValues.normalized_tags?.map((tag) => ({
          id: tag.id,
          name: tag.name,
          color: tag.color || "gray",
        })) || [];
      setSelectedTags(candidateTags);

      // Set skills
      const candidateSkills =
        defaultValues.skills?.map((skill) => ({
          id: skill.name, // This might need adjustment based on your data structure
          name: skill.name,
          level: skill.level,
          years: skill.years,
        })) || [];
      setSkills(candidateSkills);
    }
  }, [mode, defaultValues]);

  const handleCreateTag = async (name: string, color: string): Promise<Tag> => {
    const newTag = await createTag.mutateAsync({ name, color });
    return newTag;
  };

  const addSkill = () => {
    if (newSkillId && newSkillYears >= 0) {
      const selectedSkill = allSkills?.find((s) => s.id === newSkillId);
      if (selectedSkill && !skills.find((s) => s.id === newSkillId)) {
        setSkills([
          ...skills,
          {
            id: selectedSkill.id,
            name: selectedSkill.name,
            level: newSkillLevel,
            years: newSkillYears,
          },
        ]);
        setNewSkillId("");
        setNewSkillLevel("beginner");
        setNewSkillYears(0);
      }
    }
  };

  const removeSkill = (index: number) => {
    setSkills(skills.filter((_, i) => i !== index));
  };

  const handleFormSubmit = async (data: CandidateFormData) => {
    await onSubmit(data, selectedTags, skills);

    // Reset form state only for add mode
    if (mode === "add") {
      reset();
      setSelectedTags([]);
      setSkills([]);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            {...register("name")}
            placeholder="John Doe"
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && (
            <p className="text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email *</Label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="<EMAIL>"
            className={errors.email ? "border-red-500" : ""}
          />
          {errors.email && (
            <p className="text-sm text-red-500">{errors.email.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="role">Role *</Label>
          <Input
            id="role"
            {...register("role")}
            placeholder="Senior Frontend Developer"
            className={errors.role ? "border-red-500" : ""}
          />
          {errors.role && (
            <p className="text-sm text-red-500">{errors.role.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            {...register("phone")}
            placeholder="+****************"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="experience">Years of Experience</Label>
          <Input
            id="experience"
            {...register("experience")}
            placeholder="5 years"
          />
        </div>
      </div>

      {/* Professional Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="industry">Industry</Label>
          <Select
            onValueChange={(value) => setValue("industry", value)}
            defaultValue={mode === "edit" ? defaultValues?.industry : undefined}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select industry" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="technology">Technology</SelectItem>
              <SelectItem value="finance">Finance</SelectItem>
              <SelectItem value="healthcare">Healthcare</SelectItem>
              <SelectItem value="education">Education</SelectItem>
              <SelectItem value="retail">Retail</SelectItem>
              <SelectItem value="manufacturing">Manufacturing</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="remotePreference">Remote Work Preference</Label>
          <Select
            onValueChange={(value) => setValue("remotePreference", value)}
            defaultValue={
              mode === "edit" ? defaultValues?.remotePreference : undefined
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select preference" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="remote">Fully Remote</SelectItem>
              <SelectItem value="hybrid">Hybrid</SelectItem>
              <SelectItem value="onsite">On-site Only</SelectItem>
              <SelectItem value="flexible">Flexible</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="visaStatus">Visa Status</Label>
          <Select
            onValueChange={(value) => setValue("visaStatus", value)}
            defaultValue={
              mode === "edit" ? defaultValues?.visaStatus : undefined
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="citizen">US Citizen</SelectItem>
              <SelectItem value="permanent_resident">
                Permanent Resident
              </SelectItem>
              <SelectItem value="h1b">H1B</SelectItem>
              <SelectItem value="opt">OPT</SelectItem>
              <SelectItem value="requires_sponsorship">
                Requires Sponsorship
              </SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Social Links */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
          <Input
            id="linkedinUrl"
            {...register("linkedinUrl")}
            placeholder="https://linkedin.com/in/johndoe"
            className={errors.linkedinUrl ? "border-red-500" : ""}
          />
          {errors.linkedinUrl && (
            <p className="text-sm text-red-500">{errors.linkedinUrl.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="githubUrl">GitHub URL</Label>
          <Input
            id="githubUrl"
            {...register("githubUrl")}
            placeholder="https://github.com/johndoe"
            className={errors.githubUrl ? "border-red-500" : ""}
          />
          {errors.githubUrl && (
            <p className="text-sm text-red-500">{errors.githubUrl.message}</p>
          )}
        </div>

        {mode === "edit" && (
          <div className="space-y-2">
            <Label htmlFor="twitterUrl">Twitter URL</Label>
            <Input
              id="twitterUrl"
              {...register("twitterUrl")}
              placeholder="https://twitter.com/johndoe"
              className={errors.twitterUrl ? "border-red-500" : ""}
            />
            {errors.twitterUrl && (
              <p className="text-sm text-red-500">
                {errors.twitterUrl.message}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Tags */}
      <div className="space-y-2">
        <Label>Tags</Label>
        <TagPicker
          tags={availableTags}
          selectedTags={selectedTags}
          onTagsChange={setSelectedTags}
          onCreateTag={handleCreateTag}
          placeholder="Select or create tags..."
        />
      </div>

      {/* Skills */}
      <div className="space-y-2">
        <Label>Skills</Label>
        <div className="space-y-2 mb-4">
          {skills.map((skill, index) => (
            <div
              key={skill.id}
              className="flex items-center gap-2 p-2 border rounded"
            >
              <span className="font-medium">{skill.name}</span>
              <Badge variant="outline">{skill.level}</Badge>
              <span className="text-sm text-muted-foreground">
                {skill.years} years
              </span>
              <X
                className="h-4 w-4 cursor-pointer ml-auto"
                onClick={() => removeSkill(index)}
              />
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
          <SkillPicker
            value={newSkillId}
            onValueChange={setNewSkillId}
            placeholder="Select skill..."
          />
          <Select value={newSkillLevel} onValueChange={setNewSkillLevel}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="beginner">Beginner</SelectItem>
              <SelectItem value="intermediate">Intermediate</SelectItem>
              <SelectItem value="advanced">Advanced</SelectItem>
              <SelectItem value="expert">Expert</SelectItem>
            </SelectContent>
          </Select>
          <Input
            type="number"
            min="0"
            value={newSkillYears}
            onChange={(e) => setNewSkillYears(parseInt(e.target.value) || 0)}
            placeholder="Years"
          />
          <Button type="button" variant="outline" size="sm" onClick={addSkill}>
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* AI Summary */}
      <div className="space-y-2">
        <Label htmlFor="aiSummary">Professional Summary</Label>
        <Textarea
          id="aiSummary"
          {...register("aiSummary")}
          placeholder="Brief professional summary or notes about the candidate..."
          rows={3}
        />
      </div>

      <Button type="submit" className="w-full" disabled={isPending}>
        {isPending
          ? mode === "add"
            ? "Adding Candidate..."
            : "Updating Candidate..."
          : submitLabel ||
            (mode === "add" ? "Add Candidate" : "Update Candidate")}
      </Button>
    </form>
  );
}
