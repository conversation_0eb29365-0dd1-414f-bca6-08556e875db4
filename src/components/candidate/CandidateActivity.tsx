import { CandidateType } from "@/types/candidate";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { RecentActivity } from "./activity/RecentActivity";
import { Communications } from "./activity/Communications";
import { Meetings } from "./activity/Meetings";
import { Documents } from "./activity/Documents";

interface CandidateActivityProps {
  candidate: CandidateType;
}

export function CandidateActivity({ candidate }: CandidateActivityProps) {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Activity</TabsTrigger>
          <TabsTrigger value="communications">Communications</TabsTrigger>
          <TabsTrigger value="meetings">Meetings</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <RecentActivity candidateId={candidate.id} showAll={true} />
        </TabsContent>

        <TabsContent value="communications">
          <Communications candidateId={candidate.id} />
        </TabsContent>

        <TabsContent value="meetings">
          <Meetings candidateId={candidate.id} />
        </TabsContent>

        <TabsContent value="documents">
          <Documents candidateId={candidate.id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
