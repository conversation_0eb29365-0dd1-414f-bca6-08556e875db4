import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { EditCandidateDialog } from "../EditCandidateDialog";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import React from "react";

// Mock the EditCandidateForm component
vi.mock("../EditCandidateForm", () => ({
  EditCandidateForm: ({ onSuccess }: { onSuccess: () => void }) =>
    React.createElement(
      "form",
      { "data-testid": "edit-candidate-form" },
      React.createElement(
        "button",
        { type: "button", onClick: onSuccess },
        "Mock Save",
      ),
    ),
}));

describe("EditCandidateDialog - Accessibility Tests", () => {
  const mockCandidate = {
    id: "test-123",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Software Engineer",
    createdAt: new Date().toISOString(),
  };

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  const wrapper = ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);

  const defaultProps = {
    candidate: mockCandidate,
    open: true,
    onOpenChange: vi.fn(),
    onSuccess: vi.fn(),
  };

  it("should have proper ARIA attributes on dialog", () => {
    render(React.createElement(EditCandidateDialog, defaultProps), { wrapper });

    const dialog = screen.getByRole("dialog");
    expect(dialog).toBeInTheDocument();
    expect(dialog).toHaveAttribute("aria-modal", "true");
  });

  it("should have accessible dialog title", () => {
    render(React.createElement(EditCandidateDialog, defaultProps), { wrapper });

    const heading = screen.getByRole("heading", {
      name: /Edit Candidate: John Doe/i,
    });
    expect(heading).toBeInTheDocument();
  });

  it("should close dialog when Escape key is pressed", async () => {
    const onOpenChange = vi.fn();
    render(
      React.createElement(EditCandidateDialog, {
        ...defaultProps,
        onOpenChange,
      }),
      { wrapper },
    );

    const dialog = screen.getByRole("dialog");
    fireEvent.keyDown(dialog, { key: "Escape" });

    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  it("should call onSuccess and close dialog when form is submitted", async () => {
    const onOpenChange = vi.fn();
    const onSuccess = vi.fn();

    render(
      React.createElement(EditCandidateDialog, {
        ...defaultProps,
        onOpenChange,
        onSuccess,
      }),
      { wrapper },
    );

    const saveButton = screen.getByText("Mock Save");
    await userEvent.click(saveButton);

    expect(onOpenChange).toHaveBeenCalledWith(false);
    expect(onSuccess).toHaveBeenCalled();
  });

  it("should not render dialog when open is false", () => {
    render(
      React.createElement(EditCandidateDialog, {
        ...defaultProps,
        open: false,
      }),
      { wrapper },
    );

    expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
  });

  it("should have proper content structure", () => {
    render(React.createElement(EditCandidateDialog, defaultProps), { wrapper });

    // Check for dialog header
    const dialogHeader = screen.getByRole("heading", { level: 2 });
    expect(dialogHeader).toHaveTextContent("Edit Candidate: John Doe");

    // Check for form
    const form = screen.getByTestId("edit-candidate-form");
    expect(form).toBeInTheDocument();
  });

  it("should handle click outside to close", async () => {
    const onOpenChange = vi.fn();
    const { container } = render(
      React.createElement(
        "div",
        {},
        React.createElement(
          "div",
          { "data-testid": "outside" },
          "Outside Element",
        ),
        React.createElement(EditCandidateDialog, {
          ...defaultProps,
          onOpenChange,
        }),
      ),
      { wrapper },
    );

    // Click on the overlay/backdrop
    const overlay = container.querySelector("[data-radix-dialog-overlay]");
    if (overlay) {
      await userEvent.click(overlay);
      expect(onOpenChange).toHaveBeenCalledWith(false);
    }
  });

  it("should have max height and scroll for long content", () => {
    render(React.createElement(EditCandidateDialog, defaultProps), { wrapper });

    const dialogContent = screen.getByRole("dialog").firstElementChild;
    expect(dialogContent).toHaveClass("max-h-[90vh]", "overflow-y-auto");
  });

  it("should have appropriate max width", () => {
    render(React.createElement(EditCandidateDialog, defaultProps), { wrapper });

    const dialogContent = screen.getByRole("dialog").firstElementChild;
    expect(dialogContent).toHaveClass("max-w-4xl");
  });
});
