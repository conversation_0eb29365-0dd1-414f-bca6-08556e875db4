import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Mail, Send, Loader2, Sparkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCreateMessage } from "@/hooks/useCreateMessage";
import { CandidateType } from "@/types/candidate";
import { generateText } from "@/utils/gemini";

interface BulkEmailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  candidates: CandidateType[];
}

export function BulkEmailDialog({
  open,
  onO<PERSON><PERSON><PERSON>e,
  candidates,
}: BulkEmailDialogProps) {
  const [selectedCandidates, setSelectedCandidates] = useState<Set<string>>(
    new Set(),
  );
  const [subject, setSubject] = useState("");
  const [content, setContent] = useState("");
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [isSending, setIsSending] = useState(false);

  const { toast } = useToast();
  const createMessage = useCreateMessage();

  const handleCandidateToggle = (candidateId: string, checked: boolean) => {
    const newSelected = new Set(selectedCandidates);
    if (checked) {
      newSelected.add(candidateId);
    } else {
      newSelected.delete(candidateId);
    }
    setSelectedCandidates(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCandidates(new Set(candidates.map((c) => c.id)));
    } else {
      setSelectedCandidates(new Set());
    }
  };

  const generateAIContent = async () => {
    if (selectedCandidates.size === 0) {
      toast({
        title: "No Candidates Selected",
        description: "Please select candidates to generate AI content for.",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingAI(true);
    try {
      const selectedCandidatesList = candidates.filter((c) =>
        selectedCandidates.has(c.id),
      );
      const roles = [...new Set(selectedCandidatesList.map((c) => c.role))];
      const skills = [
        ...new Set(
          selectedCandidatesList.flatMap(
            (c) =>
              c.skills?.map((s) => (typeof s === "string" ? s : s.name)) || [],
          ),
        ),
      ];

      const prompt = `
        Write a professional bulk recruitment email for ${selectedCandidates.size} candidates.
        
        Context:
        - Number of candidates: ${selectedCandidates.size}
        - Common roles: ${roles.join(", ")}
        - Common skills: ${skills.slice(0, 10).join(", ")}
        
        Write a personalized but scalable email that:
        1. Shows genuine interest in their backgrounds
        2. Mentions the variety of roles we're hiring for
        3. Invites them for next steps
        4. Maintains a professional but friendly tone
        5. Uses placeholders like [Candidate Name] for personalization
        
        Format the response as:
        Subject: [Email subject line]
        
        [Email body content with [Candidate Name] placeholder]
      `;

      const response = await generateText(
        prompt,
        "You are a professional recruiter. Write engaging bulk recruitment emails that can be personalized for multiple candidates.",
      );

      const lines = response.split("\n");
      const subjectLine = lines
        .find((line) => line.startsWith("Subject:"))
        ?.replace("Subject:", "")
        .trim();
      const bodyStart =
        lines.findIndex((line) => line.startsWith("Subject:")) + 1;
      const bodyContent = lines
        .slice(bodyStart + 1)
        .join("\n")
        .trim();

      if (subjectLine) setSubject(subjectLine);
      if (bodyContent) setContent(bodyContent);

      toast({
        title: "AI Content Generated",
        description: `Generated bulk email content for ${selectedCandidates.size} candidates.`,
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate AI content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const handleSend = async () => {
    if (selectedCandidates.size === 0) {
      toast({
        title: "No Recipients",
        description: "Please select candidates to send emails to.",
        variant: "destructive",
      });
      return;
    }

    if (!subject || !content) {
      toast({
        title: "Missing Content",
        description: "Please provide both subject and email content.",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      const selectedCandidatesList = candidates.filter((c) =>
        selectedCandidates.has(c.id),
      );

      for (const candidate of selectedCandidatesList) {
        try {
          // Personalize content by replacing placeholders
          const personalizedContent = content
            .replace(/\[Candidate Name\]/g, candidate.name)
            .replace(/\[Name\]/g, candidate.name)
            .replace(/\[Role\]/g, candidate.role);

          await createMessage.mutateAsync({
            sender_name: "Hiring Team",
            sender_email: "<EMAIL>",
            content: `Subject: ${subject}\n\nTo: ${candidate.email}\n\n${personalizedContent}`,
            status: "read",
          });

          successCount++;
        } catch (error) {
          console.error(`Failed to send email to ${candidate.name}:`, error);
          errorCount++;
        }
      }

      if (successCount > 0) {
        toast({
          title: "Bulk Email Sent",
          description: `Successfully sent ${successCount} emails${errorCount > 0 ? ` (${errorCount} failed)` : ""}.`,
        });
      }

      if (errorCount === selectedCandidatesList.length) {
        toast({
          title: "All Emails Failed",
          description: "Failed to send any emails. Please try again.",
          variant: "destructive",
        });
      }

      // Reset form on success
      if (successCount > 0) {
        setSelectedCandidates(new Set());
        setSubject("");
        setContent("");
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send bulk emails. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  const selectedCount = selectedCandidates.size;
  const allSelected = selectedCount === candidates.length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Email Candidates</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Candidate Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Select Recipients</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={allSelected}
                  onCheckedChange={handleSelectAll}
                />
                <Label htmlFor="select-all" className="text-sm">
                  Select All ({candidates.length})
                </Label>
              </div>
            </div>

            <ScrollArea className="h-48 border rounded-md p-4">
              <div className="space-y-2">
                {candidates.map((candidate) => (
                  <div
                    key={candidate.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={candidate.id}
                      checked={selectedCandidates.has(candidate.id)}
                      onCheckedChange={(checked) =>
                        handleCandidateToggle(candidate.id, checked as boolean)
                      }
                    />
                    <Label
                      htmlFor={candidate.id}
                      className="flex-1 cursor-pointer"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium">{candidate.name}</span>
                          <span className="text-muted-foreground ml-2">
                            ({candidate.role})
                          </span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {candidate.email}
                        </span>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>

            {selectedCount > 0 && (
              <Badge variant="secondary">
                {selectedCount} candidate{selectedCount > 1 ? "s" : ""} selected
              </Badge>
            )}
          </div>

          {/* Email Content */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Email Content</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={generateAIContent}
                disabled={isGeneratingAI || selectedCount === 0}
              >
                {isGeneratingAI ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate AI Content
                  </>
                )}
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="subject">Subject</Label>
                <Input
                  id="subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder="Enter email subject..."
                />
              </div>

              <div>
                <Label htmlFor="content">Message</Label>
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Enter your message... Use [Candidate Name] for personalization."
                  rows={8}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Tip: Use [Candidate Name] to personalize emails for each
                  recipient.
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSend}
              disabled={
                isSending || selectedCount === 0 || !subject || !content
              }
              className="flex-1"
            >
              {isSending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending to {selectedCount} candidates...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send to {selectedCount} candidate
                  {selectedCount > 1 ? "s" : ""}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
