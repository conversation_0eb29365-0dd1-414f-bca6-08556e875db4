import { useUpdateCandidate } from "@/hooks/useUpdateCandidate";
import { useToast } from "@/hooks/use-toast";
import { CandidateForm, CandidateFormData } from "./CandidateForm";
import { CandidateType } from "@/types/candidate";
import { Tag } from "@/components/TagPicker";
import { CandidatesService } from "@/services/CandidatesService";

interface EditCandidateFormProps {
  candidate: CandidateType;
  onSuccess?: () => void;
}

export function EditCandidateForm({
  candidate,
  onSuccess,
}: EditCandidateFormProps) {
  const { toast } = useToast();
  const updateCandidate = useUpdateCandidate();

  const handleSubmit = async (
    data: CandidateFormData,
    tags: Tag[],
    skills: { id: string; name: string; level: string; years: number }[],
  ) => {
    try {
      // Update candidate basic info
      await updateCandidate.mutateAsync({
        id: candidate.id,
        data: {
          name: data.name.trim(),
          email: data.email.trim(),
          role: data.role.trim(),
          phone: data.phone?.trim() || undefined,
          location: data.location?.trim() || undefined,
          experience: data.experience?.trim() || undefined,
          industry: data.industry?.trim() || undefined,
          remotePreference: data.remotePreference?.trim() || undefined,
          visaStatus: data.visaStatus?.trim() || undefined,
          socialLinks: {
            linkedin: data.linkedinUrl?.trim() || undefined,
            github: data.githubUrl?.trim() || undefined,
            twitter: data.twitterUrl?.trim() || undefined,
          },
          aiSummary: data.aiSummary?.trim() || undefined,
          tags: tags.map((tag) => tag.name),
        },
      });

      // Handle skills updates using upsertCandidateSkills
      if (candidate.id && skills) {
        // Compare skills to determine if an update is needed
        const originalSkills = candidate.skills || [];
        const skillsChanged =
          skills.length !== originalSkills.length ||
          skills.some((skill, index) => {
            const original = originalSkills[index];
            return (
              !original ||
              skill.name !== original.name ||
              skill.level !== original.level ||
              skill.years !== original.years
            );
          });

        if (skillsChanged) {
          // Prepare skills data for the RPC call
          const skillsData = skills.map((skill) => ({
            name: skill.name,
            level: skill.level || "intermediate",
            years: skill.years || 0,
          }));

          await CandidatesService.upsertCandidateSkills(
            candidate.id,
            skillsData,
          );
        }
      }

      onSuccess?.();

      toast({
        title: "Success",
        description: "Candidate has been updated successfully!",
      });
    } catch (error) {
      console.error("Form submission error:", error);
      toast({
        title: "Error",
        description: `Failed to update candidate: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
      throw error; // Re-throw to prevent form reset
    }
  };

  return (
    <CandidateForm
      mode="edit"
      defaultValues={candidate}
      onSubmit={handleSubmit}
      isPending={updateCandidate.isPending}
    />
  );
}
