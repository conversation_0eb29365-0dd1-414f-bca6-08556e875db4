import { Badge } from "@/components/ui/badge";
import { CandidateType } from "@/types/candidate";

interface CandidateSkillsDisplayProps {
  candidate: CandidateType;
}

export const CandidateSkillsDisplay = ({
  candidate,
}: CandidateSkillsDisplayProps) => {
  // Use skill_names if available, otherwise fall back to skills array
  const skillNames =
    candidate.skill_names || candidate.skills.map((skill) => skill.name);

  if (!skillNames || skillNames.length === 0) {
    return <p className="text-sm text-gray-500">No skills listed</p>;
  }

  return (
    <div className="flex flex-wrap gap-2">
      {skillNames.map((skillName, index) => (
        <Badge key={index} variant="secondary">
          {skillName}
        </Badge>
      ))}
    </div>
  );
};

// Example usage in a parent component:
// <CandidateSkillsDisplay candidate={candidate} />
