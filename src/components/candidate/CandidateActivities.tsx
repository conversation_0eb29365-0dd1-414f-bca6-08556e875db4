import React, { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useCreateActivityEntry } from "@/hooks/useCreateActivityEntry";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { CandidateType } from "@/types/candidate";

interface CandidateActivitiesProps {
  candidate: CandidateType;
}

interface ActivityEvent {
  id: string;
  activity_type: string;
  title: string;
  description: string;
  metadata?: any;
  created_at: string;
}

export function CandidateActivities({ candidate }: CandidateActivitiesProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const createActivityEntry = useCreateActivityEntry();
  const [isAddingEntry, setIsAddingEntry] = useState(false);
  const [newEntry, setNewEntry] = useState({
    title: "",
    description: "",
    activity_type: "general",
    event_date: new Date().toISOString().split("T")[0],
    status: "completed",
  });

  const fetchActivities = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from("candidate_activities")
      .select("*")
      .eq("candidate_id", candidate.id)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const { records: activities = [], isLoading } = useRealtimeCollection(
    "candidate_activities",
    fetchActivities,
    "public",
    `candidate_id=eq.${candidate.id}`,
  );

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "interview":
        return "🎯";
      case "document":
        return "📄";
      case "screening":
        return "✅";
      case "application":
        return "📝";
      case "status_change":
        return "🔄";
      case "note":
        return "📝";
      case "email":
        return "📧";
      default:
        return "•";
    }
  };

  const handleAddEntry = async () => {
    if (!newEntry.title.trim()) {
      toast({
        title: "Error",
        description: "Please enter a title for the activity.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createActivityEntry.mutateAsync({
        candidate_id: candidate.id,
        activity_type: newEntry.activity_type,
        title: newEntry.title,
        description: newEntry.description,
        metadata: {
          event_date: newEntry.event_date,
          status: newEntry.status,
        },
      });

      setNewEntry({
        title: "",
        description: "",
        activity_type: "general",
        event_date: new Date().toISOString().split("T")[0],
        status: "completed",
      });
      setIsAddingEntry(false);

      toast({
        title: "Activity Added",
        description: "Activity has been added successfully.",
      });
    } catch (error) {
      console.error("Error adding activity:", error);
      toast({
        title: "Error",
        description: "Failed to add activity. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>Activities</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsAddingEntry(!isAddingEntry)}
        >
          {isAddingEntry ? "Cancel" : "Add Activity"}
        </Button>
      </CardHeader>
      <CardContent>
        {isAddingEntry && (
          <div className="mb-6 p-4 border rounded-lg bg-gray-50">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Activity Type</label>
                <Select
                  value={newEntry.activity_type}
                  onValueChange={(value) =>
                    setNewEntry({ ...newEntry, activity_type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="interview">Interview</SelectItem>
                    <SelectItem value="document">Document</SelectItem>
                    <SelectItem value="screening">Screening</SelectItem>
                    <SelectItem value="application">Application</SelectItem>
                    <SelectItem value="status_change">Status Change</SelectItem>
                    <SelectItem value="note">Note</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={newEntry.title}
                  onChange={(e) =>
                    setNewEntry({ ...newEntry, title: e.target.value })
                  }
                  placeholder="Enter title"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={newEntry.description}
                  onChange={(e) =>
                    setNewEntry({ ...newEntry, description: e.target.value })
                  }
                  placeholder="Enter description"
                  rows={3}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Date</label>
                <Input
                  type="date"
                  value={newEntry.event_date}
                  onChange={(e) =>
                    setNewEntry({ ...newEntry, event_date: e.target.value })
                  }
                />
              </div>
              <div>
                <label className="text-sm font-medium">Status</label>
                <Select
                  value={newEntry.status}
                  onValueChange={(value) =>
                    setNewEntry({ ...newEntry, status: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="upcoming">Upcoming</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2">
                <Button onClick={handleAddEntry} disabled={createActivityEntry.isPending}>
                  {createActivityEntry.isPending ? "Adding..." : "Add Activity"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsAddingEntry(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {activities.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No activities yet.</p>
              <p className="text-sm">Add activities to track candidate progress.</p>
            </div>
          ) : (
            activities.map((event: ActivityEvent) => (
              <div
                key={event.id}
                className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="text-2xl">{getActivityIcon(event.activity_type)}</div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm">{event.title}</h4>
                    <span className="text-xs text-gray-500">
                      {formatDate(event.created_at)}
                    </span>
                  </div>
                  {event.description && (
                    <p className="text-sm text-gray-600 mt-1">
                      {event.description}
                    </p>
                  )}
                  {event.metadata?.status && (
                    <span className={`inline-block px-2 py-1 text-xs rounded-full mt-2 ${
                      event.metadata.status === "completed"
                        ? "bg-green-100 text-green-800"
                        : event.metadata.status === "upcoming"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-red-100 text-red-800"
                    }`}>
                      {event.metadata.status}
                    </span>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
} 