/**
 * Example usage of ApplicantList component
 * This demonstrates how to use the ApplicantList component in pages with minimal code
 */

import { useState } from "react";
import { ApplicantList } from "./ApplicantList";
import { PipelineCandidateData } from "@/services/PipelineService";

// Example 1: Basic usage in a job applicants page
export function JobApplicantsPageExample() {
  // In a real page, this would come from a hook like usePipeline or useJobApplicants
  const applicants: PipelineCandidateData[] = [
    {
      id: "1",
      user_id: "user123",
      candidate_id: "cand1",
      job_id: "job1",
      candidate_name: "<PERSON>",
      role: "Senior Frontend Developer",
      stage: "Interview",
      rating: 4,
      last_activity: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    // ... more applicants
  ];

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Job Applicants</h1>
      <ApplicantList
        applicants={applicants}
        onApplicantClick={(applicant) => {
          // Navigate to applicant details or open a modal
          console.log("Selected applicant:", applicant);
        }}
      />
    </div>
  );
}

// Example 2: Usage with selection state
export function PipelineViewExample() {
  const [selectedApplicantId, setSelectedApplicantId] = useState<string>();
  const applicants: PipelineCandidateData[] = []; // Would come from hooks

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h2 className="text-lg font-semibold mb-4">Candidates</h2>
        <ApplicantList
          applicants={applicants}
          selectedApplicantId={selectedApplicantId}
          onApplicantClick={(applicant) => {
            setSelectedApplicantId(applicant.id);
          }}
        />
      </div>
      <div>
        {selectedApplicantId && (
          <div>
            {/* Show applicant details */}
            <h2 className="text-lg font-semibold mb-4">Candidate Details</h2>
            {/* Details component here */}
          </div>
        )}
      </div>
    </div>
  );
}

// Example 3: Usage with loading and empty states
export function MinimalPageExample() {
  const { data: applicants, isLoading } = useJobApplicants(); // Custom hook

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return <ApplicantList applicants={applicants || []} />;
}

// Example custom hook (simplified)
function useJobApplicants() {
  // This would be implemented with proper data fetching
  return {
    data: [] as PipelineCandidateData[],
    isLoading: false,
  };
}
