import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { CandidateType } from "@/types/candidate";
import { AddCandidateForm } from "./AddCandidateForm";
import { EditCandidateForm } from "./EditCandidateForm";

interface CandidateFormDialogProps {
  mode: "add" | "edit";
  candidate?: CandidateType;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CandidateFormDialog({
  mode,
  candidate,
  open,
  onOpenChange,
  onSuccess,
}: CandidateFormDialogProps) {
  const handleSuccess = () => {
    onOpenChange(false);
    onSuccess?.();
  };

  const getTitle = () => {
    if (mode === "add") return "Add New Candidate";
    return `Edit Candidate: ${candidate?.name || ""}`;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
        </DialogHeader>

        {mode === "add" ? (
          <AddCandidateForm onSuccess={handleSuccess} />
        ) : (
          <EditCandidateForm 
            candidate={candidate!} 
            onSuccess={handleSuccess} 
          />
        )}
      </DialogContent>
    </Dialog>
  );
} 