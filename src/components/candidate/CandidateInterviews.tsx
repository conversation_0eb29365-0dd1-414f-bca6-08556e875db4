import { CandidateType } from "@/types/candidate";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Calendar as CalendarIcon,
  Clock,
  Users,
  Video,
  MapPin,
  Plus,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { ScheduleMeetingDialog } from "./overview/quick-actions/ScheduleMeetingDialog";
import { InterviewDetailsDialog } from "./interview/InterviewDetailsDialog";
import { useState } from "react";

interface CandidateInterviewsProps {
  candidate: CandidateType;
}

interface Interview {
  id: string;
  interview_type: string;
  scheduled_date: string;
  duration_minutes: number;
  status: string;
  interviewers: string[];
  location: string;
  meeting_platform: string;
  meeting_link: string;
  feedback: string;
}

export function CandidateInterviews({ candidate }: CandidateInterviewsProps) {
  const { user } = useAuth();
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(
    null,
  );
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  // Real-time interviews subscription
  const { records: interviews = [], isLoading } = useRealtimeCollection(
    "candidate_interviews",
    async () => {
      if (!user) return [];

      try {
        const { data, error } = await supabase
          .from("candidate_interviews")
          .select("*")
          .eq("candidate_id", candidate.id)
          .eq("user_id", user.id)
          .order("scheduled_date", { ascending: true });

        if (error) throw error;
        return data || [];
      } catch (error) {
        console.error("Error fetching interviews:", error);
        return [];
      }
    },
    "public",
    `candidate_id=eq.${candidate.id}`,
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-500";
      case "completed":
        return "bg-green-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const handleViewDetails = (interview: Interview) => {
    setSelectedInterview(interview);
    setShowDetailsDialog(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Interviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="animate-pulse space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-24 bg-muted rounded" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Interviews</CardTitle>
          <Button onClick={() => setShowScheduleDialog(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Schedule Interview
          </Button>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px] pr-4">
            {interviews.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="text-4xl mb-4">🎯</div>
                <p className="text-muted-foreground">
                  No interviews scheduled yet. Click "Schedule Interview" to add
                  the first interview for this candidate.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {interviews.map((interview) => (
                  <div
                    key={interview.id}
                    className="p-4 border rounded-lg hover:bg-accent/50"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">
                          {interview.interview_type}
                        </h3>
                        <Badge
                          variant="secondary"
                          className={`${getStatusColor(interview.status)} text-white`}
                        >
                          {interview.status}
                        </Badge>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(interview)}
                      >
                        View Details
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <CalendarIcon className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">
                          {new Date(
                            interview.scheduled_date,
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">
                          {new Date(
                            interview.scheduled_date,
                          ).toLocaleTimeString()}{" "}
                          ({interview.duration_minutes} min)
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm">
                          {interview.interviewers?.join(", ") || "Not assigned"}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {interview.meeting_platform === "In Person" ||
                        !interview.meeting_platform ? (
                          <MapPin className="w-4 h-4 text-muted-foreground" />
                        ) : (
                          <Video className="w-4 h-4 text-muted-foreground" />
                        )}
                        <span className="text-sm">
                          {interview.location ||
                            interview.meeting_platform ||
                            "Virtual"}
                        </span>
                      </div>
                    </div>
                    {interview.feedback && (
                      <div className="mt-4 p-3 bg-muted rounded-md">
                        <p className="text-sm">{interview.feedback}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      <ScheduleMeetingDialog
        candidateId={candidate.id}
        open={showScheduleDialog}
        onOpenChange={setShowScheduleDialog}
      />

      <InterviewDetailsDialog
        interview={selectedInterview}
        open={showDetailsDialog}
        onOpenChange={setShowDetailsDialog}
      />
    </div>
  );
}
