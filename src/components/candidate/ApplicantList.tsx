import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Clock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { PipelineCandidateData } from "@/services/PipelineService";
import { getStageColor } from "@/utils/stageUtils";

interface ApplicantListProps {
  applicants: PipelineCandidateData[];
  onApplicantClick?: (applicant: PipelineCandidateData) => void;
  selectedApplicantId?: string;
  className?: string;
}

export function ApplicantList({
  applicants,
  onApplicantClick,
  selectedApplicantId,
  className = "",
}: ApplicantListProps) {
  if (applicants.length === 0) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">
          No applicants found in this pipeline.
        </p>
      </div>
    );
  }


  const renderRating = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[...Array(5)].map((_, index) => (
          <Star
            key={index}
            className={`w-4 h-4 ${
              index < rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {applicants.map((applicant) => (
        <Card
          key={applicant.id}
          className={`cursor-pointer transition-all hover:shadow-md ${
            selectedApplicantId === applicant.id
              ? "ring-2 ring-primary shadow-md"
              : ""
          }`}
          onClick={() => onApplicantClick?.(applicant)}
        >
          <CardContent className="p-4">
            <div className="flex items-start gap-4">
              <Avatar className="h-10 w-10 flex-shrink-0">
                <AvatarImage
                  src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${applicant.candidate_name}`}
                  alt={applicant.candidate_name}
                />
                <AvatarFallback>
                  {getInitials(applicant.candidate_name)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="min-w-0 flex-1">
                    <h4 className="font-semibold text-base truncate">
                      {applicant.candidate_name}
                    </h4>
                    <p className="text-sm text-muted-foreground truncate">
                      {applicant.role}
                    </p>
                  </div>
                  <Badge
                    variant="secondary"
                    className={`${getStageColor(applicant.stage)} flex-shrink-0`}
                  >
                    {applicant.stage}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center gap-4">
                    {renderRating(applicant.rating)}
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      <span>
                        {formatDistanceToNow(new Date(applicant.last_activity), {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
