import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { parseResume } from "@/utils/resumeParser";
import { parseResumeText } from "@/utils/gemini";
import { useCreateCandidate } from "@/hooks/useCreateCandidate";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Upload, FileText, Check } from "lucide-react";
import { Progress } from "@/components/ui/progress";

export function ResumeParser() {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isParsing, setIsParsing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [parsedData, setParsedData] = useState<any>(null);
  const createCandidate = useCreateCandidate();
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a resume file to upload",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    setProgress(0);

    // Simulate upload progress
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 95) {
          clearInterval(interval);
          return 95;
        }
        return prev + 5;
      });
    }, 100);

    try {
      // Read the file content
      const fileText = await file.text();

      // Use Gemini to parse the resume
      clearInterval(interval);
      setProgress(100);

      // Start parsing
      setIsParsing(true);
      const result = await parseResumeText(fileText);

      if (result) {
        setParsedData(result);
        toast({
          title: "Resume Parsed Successfully",
          description:
            "The resume has been analyzed and information extracted.",
        });
      }
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: "There was an error uploading the resume.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      setIsParsing(false);
    }
  };

  const handleCreateCandidate = async () => {
    if (!parsedData) return;

    try {
      // Convert parsed data to candidate format
      const candidateData = {
        name: parsedData.name || "",
        email: parsedData.email || "",
        role: parsedData.experience?.[0]?.title || "",
        phone: parsedData.phone || "",
        skills: parsedData.skills.map((skill) => ({
          name: skill,
          level: "Intermediate",
          years: 2,
        })),
        experience: parsedData.experience?.[0]?.duration || "",
        ai_summary: parsedData.summary || "",
        tags: parsedData.skills.slice(0, 3),
      };

      await createCandidate.mutateAsync(candidateData);

      // Reset state after successful creation
      setFile(null);
      setParsedData(null);
      setProgress(0);

      toast({
        title: "Candidate Created",
        description: "A new candidate has been created from the resume.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create candidate from resume.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          AI Resume Parser
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="resume">Upload Resume</Label>
          <div className="flex gap-2">
            <Input
              id="resume"
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={handleFileChange}
              disabled={isUploading || isParsing}
            />
            <Button
              onClick={handleUpload}
              disabled={!file || isUploading || isParsing}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : isParsing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Parsing...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload
                </>
              )}
            </Button>
          </div>

          {(isUploading || isParsing) && (
            <div className="space-y-1 mt-2">
              <div className="flex justify-between text-xs">
                <span>{isUploading ? "Uploading..." : "Parsing..."}</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-1" />
            </div>
          )}
        </div>

        {parsedData && (
          <div className="space-y-4 border rounded-md p-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium text-lg">Parsed Resume Data</h3>
                <p className="text-sm text-muted-foreground">
                  The AI has extracted the following information from the
                  resume.
                </p>
              </div>
              <Button
                onClick={handleCreateCandidate}
                disabled={createCandidate.isPending}
              >
                {createCandidate.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Create Candidate
                  </>
                )}
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Basic Information</h4>
                <div className="space-y-1">
                  <p className="text-sm">
                    <span className="font-medium">Name:</span> {parsedData.name}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Email:</span>{" "}
                    {parsedData.email}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Phone:</span>{" "}
                    {parsedData.phone}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Skills</h4>
                <div className="flex flex-wrap gap-1">
                  {parsedData.skills.map((skill: string, index: number) => (
                    <span
                      key={index}
                      className="text-xs bg-secondary px-2 py-1 rounded-full"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Experience</h4>
              <div className="space-y-3">
                {parsedData.experience.map((exp: any, index: number) => (
                  <div key={index} className="text-sm">
                    <div className="font-medium">
                      {exp.title} at {exp.company}
                    </div>
                    <div className="text-muted-foreground">{exp.duration}</div>
                    <div>{exp.description}</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Education</h4>
              <div className="space-y-3">
                {parsedData.education.map((edu: any, index: number) => (
                  <div key={index} className="text-sm">
                    <div className="font-medium">{edu.degree}</div>
                    <div className="text-muted-foreground">
                      {edu.institution}, {edu.year}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {parsedData.summary && (
              <div className="space-y-2">
                <h4 className="font-medium">Summary</h4>
                <p className="text-sm">{parsedData.summary}</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
