import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useCreateActivityEntry } from "@/hooks/useCreateActivityEntry";
import { useToast } from "@/hooks/use-toast";

export const useDocumentManagement = () => {
  const { toast } = useToast();
  const createActivityEntry = useCreateActivityEntry();

  const deleteDocument = useMutation({
    mutationFn: async ({ documentId, candidateId }: { documentId: string; candidateId: string }) => {
      const { error } = await supabase
        .from("candidate_documents")
        .delete()
        .eq("id", documentId);

      if (error) throw error;
      return { documentId, candidateId };
    },
    onSuccess: async ({ candidateId }) => {
      // Create activity entry for document deletion
      try {
        await createActivityEntry.mutateAsync({
          candidate_id: candidateId,
          activity_type: "document",
          title: "Document Deleted",
          description: "A document has been removed from the candidate's profile",
          metadata: {
            action: "deleted",
            status: "completed",
          },
        });
      } catch (activityError) {
        console.warn("Failed to create activity entry for document deletion:", activityError);
      }

      toast({
        title: "Document Deleted",
        description: "Document has been successfully removed.",
      });
    },
    onError: (error) => {
      console.error("Error deleting document:", error);
      toast({
        title: "Error",
        description: "Failed to delete document. Please try again.",
        variant: "destructive",
      });
    },
  });

  return { deleteDocument };
};
