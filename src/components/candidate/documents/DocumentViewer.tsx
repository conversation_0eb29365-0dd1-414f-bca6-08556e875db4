import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Download,
  Eye,
  FileText,
  Image,
  Film,
  Music,
  Archive,
  X,
  ExternalLink,
  Share2,
  Trash2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface DocumentViewerProps {
  document: {
    id: string;
    name: string;
    type: string;
    size: number;
    file_url: string;
    uploaded_at: string;
  } | null;
  isOpen: boolean;
  onClose: () => void;
  onDelete?: (id: string) => void;
}

export function DocumentViewer({
  document: doc,
  isOpen,
  onClose,
  onDelete,
}: DocumentViewerProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  if (!doc) return null;

  const getFileIcon = (type: string) => {
    if (type.startsWith("image/")) return Image;
    if (type.startsWith("video/")) return Film;
    if (type.startsWith("audio/")) return Music;
    if (type.includes("pdf") || type.includes("document")) return FileText;
    if (type.includes("zip") || type.includes("rar")) return Archive;
    return FileText;
  };

  const formatFileSize = (bytes: number) => {
    if (!bytes) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDownload = () => {
    // Create a temporary link element and trigger download
    const link = window.document.createElement("a");
    link.href = doc.file_url;
    link.download = doc.name;
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);

    toast({
      title: "Download Started",
      description: `Downloading ${doc.name}...`,
    });
  };

  const handleShare = async () => {
    try {
      await navigator.clipboard.writeText(doc.file_url);
      toast({
        title: "Link Copied",
        description: "Document link has been copied to clipboard.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy link to clipboard.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;

    setIsDeleting(true);
    try {
      await onDelete(doc.id);
      onClose();
      toast({
        title: "Document Deleted",
        description: "The document has been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete document.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleOpenExternal = () => {
    window.open(doc.file_url, "_blank");
  };

  const isImage = doc.type.startsWith("image/");
  const isPdf = doc.type.includes("pdf");
  const isVideo = doc.type.startsWith("video/");
  const isAudio = doc.type.startsWith("audio/");

  const FileIcon = getFileIcon(doc.type);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileIcon className="h-6 w-6 text-primary" />
              <div>
                <DialogTitle className="text-lg">{doc.name}</DialogTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline">{doc.type}</Badge>
                  <span className="text-sm text-muted-foreground">
                    {formatFileSize(doc.size)}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(doc.uploaded_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm" onClick={handleOpenExternal}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open
              </Button>
              {onDelete && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {isDeleting ? "Deleting..." : "Delete"}
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 min-h-0">
          <ScrollArea className="h-full">
            <div className="p-4">
              {isImage && (
                <div className="flex justify-center">
                  <img
                    src={doc.file_url}
                    alt={doc.name}
                    className="max-w-full max-h-[60vh] object-contain rounded-lg shadow-lg"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = "none";
                    }}
                  />
                </div>
              )}

              {isPdf && (
                <div className="w-full h-[60vh] border rounded-lg">
                  <iframe
                    src={`${doc.file_url}#toolbar=1`}
                    width="100%"
                    height="100%"
                    className="rounded-lg"
                    title={doc.name}
                  />
                </div>
              )}

              {isVideo && (
                <div className="flex justify-center">
                  <video
                    controls
                    className="max-w-full max-h-[60vh] rounded-lg shadow-lg"
                    src={doc.file_url}
                  >
                    Your browser does not support the video tag.
                  </video>
                </div>
              )}

              {isAudio && (
                <div className="flex justify-center p-8">
                  <div className="w-full max-w-md">
                    <div className="flex items-center gap-4 mb-4">
                      <Music className="h-12 w-12 text-primary" />
                      <div>
                        <h3 className="font-medium">{doc.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          Audio File
                        </p>
                      </div>
                    </div>
                    <audio controls className="w-full" src={doc.file_url}>
                      Your browser does not support the audio tag.
                    </audio>
                  </div>
                </div>
              )}

              {!isImage && !isPdf && !isVideo && !isAudio && (
                <div className="flex flex-col items-center justify-center py-12">
                  <FileIcon className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    Preview Not Available
                  </h3>
                  <p className="text-muted-foreground text-center mb-6 max-w-md">
                    This file type cannot be previewed. You can download it to
                    view the contents.
                  </p>
                  <div className="space-y-2">
                    <Button onClick={handleDownload} className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Download File
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleOpenExternal}
                      className="w-full"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open in New Tab
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}
