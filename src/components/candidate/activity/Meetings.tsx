import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Users, Video, MapPin } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

interface MeetingsProps {
  candidateId: string;
}

export function Meetings({ candidateId }: MeetingsProps) {
  const { user } = useAuth();

  const fetchInterviews = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from("candidate_interviews")
      .select("*")
      .eq("candidate_id", candidateId)
      .eq("user_id", user.id)
      .order("scheduled_date", { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const { records: interviews = [], isLoading } = useRealtimeCollection(
    "candidate_interviews",
    fetchInterviews,
    "public",
    `candidate_id=eq.${candidateId}`,
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-500";
      case "completed":
        return "bg-green-500";
      case "cancelled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Meetings & Interviews</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-muted rounded" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Meetings & Interviews</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {interviews.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="text-4xl mb-4">🎯</div>
              <p className="text-muted-foreground">
                No meetings or interviews scheduled. They will appear here once
                you schedule them.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {interviews.map((interview) => (
                <div
                  key={interview.id}
                  className="p-4 border rounded-lg hover:bg-accent/50"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium">
                        {interview.interview_type}
                      </h3>
                      <Badge
                        variant="secondary"
                        className={`${getStatusColor(interview.status)} text-white`}
                      >
                        {interview.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">
                        {new Date(
                          interview.scheduled_date,
                        ).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">
                        {new Date(
                          interview.scheduled_date,
                        ).toLocaleTimeString()}{" "}
                        ({interview.duration_minutes} min)
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">
                        {interview.interviewers?.join(", ") || "Not assigned"}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {interview.meeting_platform === "In Person" ||
                      !interview.meeting_platform ? (
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                      ) : (
                        <Video className="w-4 h-4 text-muted-foreground" />
                      )}
                      <span className="text-sm">
                        {interview.location ||
                          interview.meeting_platform ||
                          "Virtual"}
                      </span>
                    </div>
                  </div>
                  {interview.feedback && (
                    <div className="mt-4 p-3 bg-muted rounded-md">
                      <p className="text-sm">{interview.feedback}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
