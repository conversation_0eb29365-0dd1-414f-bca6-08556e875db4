import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { FileText, Download, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { useToast } from "@/hooks/use-toast";

interface DocumentsProps {
  candidateId: string;
}

export function Documents({ candidateId }: DocumentsProps) {
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchDocuments = async () => {
    if (!user) return [];

    const { data, error } = await supabase
      .from("candidate_documents")
      .select("*")
      .eq("candidate_id", candidateId)
      .order("uploaded_at", { ascending: false });

    if (error) throw error;
    return data || [];
  };

  const { records: documents = [], isLoading } = useRealtimeCollection(
    "candidate_documents",
    fetchDocuments,
    "public",
    `candidate_id=eq.${candidateId}`,
  );

  const handleViewDocument = (document: any) => {
    if (document.file_url) {
      window.open(document.file_url, "_blank");
    } else {
      toast({
        title: "Document Preview",
        description: `Opening ${document.name}`,
      });
    }
  };

  const handleDownloadDocument = (document: any) => {
    if (document.file_url) {
      const link = document.createElement("a");
      link.href = document.file_url;
      link.download = document.name;
      link.click();
    } else {
      toast({
        title: "Download",
        description: `Downloading ${document.name}`,
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Document Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-muted rounded" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Document Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {documents.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="text-4xl mb-4">📄</div>
              <p className="text-muted-foreground">
                No documents uploaded yet. Documents will appear here once
                uploaded.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50"
                >
                  <div className="flex items-center space-x-4">
                    <FileText className="w-4 h-4" />
                    <div>
                      <p className="font-medium">{doc.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {doc.type || "Document"} •{" "}
                        {doc.size
                          ? `${Math.round(doc.size / 1024)} KB`
                          : "Unknown size"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDocument(doc)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadDocument(doc)}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      Download
                    </Button>
                    <div className="text-right">
                      <Badge variant="outline">uploaded</Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(doc.uploaded_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
