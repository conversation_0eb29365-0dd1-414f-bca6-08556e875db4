import { useState } from "react";
import {
  CandidateType,
  ScreeningQuestion,
  Requirements,
} from "@/types/candidate";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CandidateRequirements } from "./screening/CandidateRequirements";
import { CandidateEvaluations } from "./screening/CandidateEvaluations";
import { AIInterviewQuestions } from "./screening/AIInterviewQuestions";
import { useMutation } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useCreateActivityEntry } from "@/hooks/useCreateActivityEntry";

interface CandidateScreeningProps {
  candidate: CandidateType;
}

export function CandidateScreening({ candidate }: CandidateScreeningProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [screening, setScreening] = useState(candidate.screening || {});
  const { toast } = useToast();
  const { user } = useAuth();
  const createActivityEntry = useCreateActivityEntry();

  const updateScreeningMutation = useMutation({
    mutationFn: async (screeningData: any) => {
      if (!user) throw new Error("User not authenticated");

      const { data, error } = await supabase
        .from("candidates")
        .update({
          screening: screeningData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", candidate.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data) => {
      // Create activity entry for screening update
      try {
        await createActivityEntry.mutateAsync({
          candidate_id: candidate.id,
          activity_type: "screening",
          title: "Screening Updated",
          description: "Candidate screening information has been updated",
          metadata: {
            status: "completed",
          },
        });
      } catch (activityError) {
        console.warn("Failed to create activity entry for screening update:", activityError);
      }

      toast({
        title: "Screening Updated",
        description: "Candidate screening information has been saved successfully.",
      });
      setIsEditing(false);
    },
    onError: (error) => {
      console.error("Error updating screening:", error);
      toast({
        title: "Error",
        description: "Failed to update screening information. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    updateScreeningMutation.mutate(screening);
  };

  // Extract requirements and evaluations from screening data with proper typing
  const requirements = (screening as any)?.requirements || {
    workAuthorization: "pending" as const,
    backgroundCheck: "pending" as const,
    drugScreening: "not_required" as const,
    references: "pending" as const,
  };

  const evaluations = (screening as any)?.evaluations || [
    {
      category: "Technical Skills",
      score: 4,
      notes: "Strong frontend development skills",
    },
    {
      category: "Communication",
      score: 5,
      notes: "Excellent verbal and written communication",
    },
    {
      category: "Problem Solving",
      score: 4,
      notes: "Good analytical thinking",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Screening</h2>
          <p className="text-muted-foreground">
            Evaluate candidate skills, experience, and fit
          </p>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={() => setIsEditing(false)}
                disabled={updateScreeningMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={updateScreeningMutation.isPending}
              >
                {updateScreeningMutation.isPending ? (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </>
                )}
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Edit Screening
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CandidateRequirements requirements={requirements} />
        <CandidateEvaluations 
          evaluations={evaluations}
          candidate={{
            name: candidate.name,
            skills: candidate.skills?.map(s => typeof s === 'string' ? s : s.name) || [],
            experience: candidate.experience,
            role: candidate.role,
          }}
        />
      </div>

      <AIInterviewQuestions candidate={candidate} />
    </div>
  );
}
