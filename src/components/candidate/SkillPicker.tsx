import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, ChevronsUpDown, Plus } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSkills } from "@/hooks/useSkills";
import { useCreateSkill } from "@/hooks/useSkills";

interface SkillPickerProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
}

export function SkillPicker({
  value,
  onValueChange,
  placeholder = "Select skill...",
}: SkillPickerProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const commandListRef = useRef<HTMLDivElement>(null);

  const { data: skills = [], isLoading } = useSkills();
  const createSkill = useCreateSkill();

  const selectedSkillName = skills.find((skill) => skill.id === value)?.name;

  const filteredSkills = skills.filter((skill) =>
    skill.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleCreate = async () => {
    if (searchValue.trim()) {
      setIsCreating(true);
      try {
        const newSkill = await createSkill.mutateAsync({
          name: searchValue.trim(),
          category: "Other",
        });
        onValueChange(newSkill.id);
        setSearchValue("");
        setOpen(false);
      } catch (error) {
        console.error("Failed to create skill:", error);
      } finally {
        setIsCreating(false);
      }
    }
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (commandListRef.current) {
      commandListRef.current.scrollTop += e.deltaY;
    }
  };

  if (isLoading) {
    return (
      <div className="text-sm text-muted-foreground">Loading skills...</div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedSkillName || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search skills..."
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList
            ref={commandListRef}
            className="max-h-[280px] overflow-y-auto overflow-x-hidden px-2 pb-2"
            onWheel={handleWheel}
          >
            <CommandEmpty>
              <div className="flex flex-col items-center gap-2 p-2">
                <span>No skills found.</span>
                {searchValue.trim() && (
                  <Button
                    size="sm"
                    onClick={handleCreate}
                    disabled={isCreating}
                  >
                    {isCreating ? (
                      "Creating..."
                    ) : (
                      <>
                        <Plus className="mr-1 h-3 w-3" />
                        Create "{searchValue}"
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CommandEmpty>
            <CommandGroup>
              {filteredSkills.map((skill) => (
                <CommandItem
                  key={skill.id}
                  value={skill.name}
                  onSelect={() => {
                    onValueChange(skill.id);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={`mr-2 h-4 w-4 ${
                      value === skill.id ? "opacity-100" : "opacity-0"
                    }`}
                  />
                  {skill.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
