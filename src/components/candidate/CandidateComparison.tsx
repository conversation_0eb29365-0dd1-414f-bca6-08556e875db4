import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CandidateType } from "@/types/candidate";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { UserPlus2, X, Loader2, Spa<PERSON>les, Brain } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { generateText } from "@/utils/gemini";

interface CandidateComparisonProps {
  mainCandidate: CandidateType;
}

export function CandidateComparison({
  mainCandidate,
}: CandidateComparisonProps) {
  const [comparedCandidates, setComparedCandidates] = useState<CandidateType[]>(
    [],
  );
  const [isSelectingCandidate, setIsSelectingCandidate] = useState(false);
  const [isGeneratingComparison, setIsGeneratingComparison] = useState(false);
  const [comparisonAnalysis, setComparisonAnalysis] = useState<string | null>(
    null,
  );
  const [availableCandidates, setAvailableCandidates] = useState<
    CandidateType[]
  >([]);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchCandidates = async () => {
      if (!user) return;

      try {
        const { data: candidates, error } = await supabase
          .from("candidates")
          .select("*")
          .eq("user_id", user.id)
          .neq("id", mainCandidate.id);

        if (error) {
          console.error("Error fetching candidates:", error);
          return;
        }

        if (candidates) {
          const normalizedCandidates: CandidateType[] = candidates.map(
            (candidate) => ({
              id: candidate.id,
              name: candidate.name,
              role: candidate.role,
              email: candidate.email,
              phone: candidate.phone || "",
              location: candidate.location || "",
              avatar: candidate.avatar || "/placeholder.svg",
              recruiter: {
                id: candidate.recruiter_id || "",
                name: candidate.recruiter_name || "",
                avatar: candidate.recruiter_avatar || "/placeholder.svg",
              },
              tags: candidate.normalized_tags
                ? candidate.normalized_tags.map(tag => tag.name)
                : candidate.tags || [],
              socialLinks: {
                github: candidate.github_url || "",
                linkedin: candidate.linkedin_url || "",
                twitter: candidate.twitter_url || "",
              },
              relationshipScore: candidate.relationship_score || 0,
              experience: candidate.experience || "",
              industry: candidate.industry || "",
              remotePreference: candidate.remote_preference || "",
              visaStatus: candidate.visa_status || "",
              skills: Array.isArray(candidate.normalized_skills)
                ? (candidate.normalized_skills as {
                    name: string;
                    level: string;
                    years: number;
                    category?: string;
                  }[])
                : [],
              aiSummary: candidate.ai_summary || "",
              matchedJobs: [],
              createdAt: candidate.created_at,
              updatedAt: candidate.updated_at,
            }),
          );
          setAvailableCandidates(normalizedCandidates);
        }
      } catch (error) {
        console.error("Error in fetchCandidates:", error);
      }
    };

    fetchCandidates();
  }, [user, mainCandidate.id]);

  const addCandidateForComparison = (candidate: CandidateType) => {
    if (
      comparedCandidates.length < 8 &&
      !comparedCandidates.find((c) => c.id === candidate.id)
    ) {
      setComparedCandidates([...comparedCandidates, candidate]);
    }
    setIsSelectingCandidate(false);
  };

  const removeCandidateFromComparison = (candidateId: string) => {
    setComparedCandidates(
      comparedCandidates.filter((c) => c.id !== candidateId),
    );
  };

  const generateComparisonAnalysis = async () => {
    if (comparedCandidates.length === 0) {
      toast({
        title: "No candidates to compare",
        description:
          "Please add at least one candidate to compare with the main candidate.",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingComparison(true);
    try {
      // Create a prompt for Gemini
      const prompt = `
Compare the following candidates for a ${mainCandidate.role} position:

MAIN CANDIDATE:
Name: ${mainCandidate.name}
Role: ${mainCandidate.role}
Experience: ${mainCandidate.experience}
Skills: ${mainCandidate.skills.map((s) => (typeof s === "string" ? s : s.name)).join(", ")}
Location: ${mainCandidate.location}

COMPARISON CANDIDATES:
${comparedCandidates
  .map(
    (c) => `
Name: ${c.name}
Role: ${c.role}
Experience: ${c.experience}
Skills: ${c.skills.map((s) => (typeof s === "string" ? s : s.name)).join(", ")}
Location: ${c.location}
`,
  )
  .join("\n")}

Provide a detailed comparison analysis that:
1. Highlights the strengths and weaknesses of each candidate
2. Compares their skills and experience relative to the role
3. Recommends which candidate(s) might be the best fit and why

Format your response as a concise, professional analysis that would be helpful for a hiring manager.
`;

      const systemPrompt =
        "You are an expert technical recruiter with deep knowledge of candidate evaluation. Provide objective, insightful comparisons between candidates to help hiring managers make informed decisions.";

      // Call Gemini API
      const response = await generateText(prompt, systemPrompt);
      setComparisonAnalysis(response);

      toast({
        title: "Comparison Generated",
        description: "AI analysis of the candidates has been generated.",
      });
    } catch (error) {
      console.error("Error generating comparison:", error);
      toast({
        title: "Generation Failed",
        description:
          "Failed to generate comparison analysis. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingComparison(false);
    }
  };

  const renderComparisonCard = (
    candidate: CandidateType,
    isRemovable: boolean = false,
  ) => (
    <Card
      key={candidate.id}
      className="flex-1 min-w-[300px] max-w-[350px] shrink-0"
    >
      <CardHeader className="relative">
        {isRemovable && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4"
            onClick={() => removeCandidateFromComparison(candidate.id)}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
        <div className="flex items-center gap-4">
          <Avatar className="h-12 w-12">
            <AvatarImage src={candidate.avatar} alt={candidate.name} />
            <AvatarFallback>{candidate.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <CardTitle className="text-lg">{candidate.name}</CardTitle>
            <p className="text-sm text-muted-foreground">{candidate.role}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium mb-2">Skills</h4>
          <div className="flex flex-wrap gap-2">
            {candidate.skills &&
              Array.isArray(candidate.skills) &&
              candidate.skills.map((skill, index) => {
                const skillName =
                  typeof skill === "string" ? skill : skill?.name || "";
                const skillLevel =
                  typeof skill === "object" && skill?.level ? skill.level : "";
                const displayText =
                  typeof skill === "string"
                    ? skill
                    : `${skillName}${skillLevel ? ` (${skillLevel})` : ""}`;

                return (
                  <div
                    key={`${candidate.id}-${skillName || index}`}
                    className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm"
                  >
                    {displayText}
                  </div>
                );
              })}
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">Experience</h4>
          <p>{candidate.experience || "Not specified"}</p>
        </div>
        <div>
          <h4 className="font-medium mb-2">Location</h4>
          <p>{candidate.location || "Not specified"}</p>
        </div>
        <div>
          <h4 className="font-medium mb-2">Industry</h4>
          <p>{candidate.industry || "Not specified"}</p>
        </div>
      </CardContent>
    </Card>
  );

  const filteredAvailableCandidates = availableCandidates.filter(
    (candidate) => !comparedCandidates.find((c) => c.id === candidate.id),
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center flex-wrap gap-2">
        <h2 className="text-2xl font-bold">Compare Candidates</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={generateComparisonAnalysis}
            disabled={isGeneratingComparison || comparedCandidates.length === 0}
          >
            {isGeneratingComparison ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Generate AI Analysis
              </>
            )}
          </Button>
          <Sheet
            open={isSelectingCandidate}
            onOpenChange={setIsSelectingCandidate}
          >
            <SheetTrigger asChild>
              <Button
                onClick={() => setIsSelectingCandidate(true)}
                disabled={
                  comparedCandidates.length >= 8 ||
                  filteredAvailableCandidates.length === 0
                }
              >
                <UserPlus2 className="mr-2 h-4 w-4" />
                Add Candidate
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Select Candidate</SheetTitle>
                <SheetDescription>
                  Choose a candidate to compare with {mainCandidate.name}
                </SheetDescription>
              </SheetHeader>
              <ScrollArea className="h-[calc(100vh-200px)] mt-6">
                <div className="space-y-4 pr-4">
                  {filteredAvailableCandidates.length === 0 ? (
                    <p className="text-center text-muted-foreground py-8">
                      No other candidates available for comparison
                    </p>
                  ) : (
                    filteredAvailableCandidates.map((candidate) => (
                      <Card
                        key={candidate.id}
                        className="cursor-pointer hover:bg-accent transition-colors"
                        onClick={() => addCandidateForComparison(candidate)}
                      >
                        <CardHeader>
                          <div className="flex items-center gap-4">
                            <Avatar className="h-10 w-10">
                              <AvatarImage
                                src={candidate.avatar}
                                alt={candidate.name}
                              />
                              <AvatarFallback>
                                {candidate.name.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h3 className="font-medium">{candidate.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {candidate.role}
                              </p>
                            </div>
                          </div>
                        </CardHeader>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {comparisonAnalysis && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Comparison Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none">
              <div className="whitespace-pre-line">{comparisonAnalysis}</div>
            </div>
          </CardContent>
        </Card>
      )}

      <ScrollArea className="w-full" orientation="horizontal">
        <div className="flex gap-6 pb-6 min-w-max">
          {renderComparisonCard(mainCandidate)}
          {comparedCandidates.map((candidate) =>
            renderComparisonCard(candidate, true),
          )}
          {comparedCandidates.length < 8 &&
            filteredAvailableCandidates.length > 0 && (
              <div
                key="add-candidate"
                className="flex-1 min-w-[300px] max-w-[350px] shrink-0 border-2 border-dashed rounded-lg flex items-center justify-center p-6"
              >
                <Button
                  variant="outline"
                  onClick={() => setIsSelectingCandidate(true)}
                >
                  <UserPlus2 className="mr-2 h-4 w-4" />
                  Add Candidate
                </Button>
              </div>
            )}
        </div>
      </ScrollArea>
    </div>
  );
}
