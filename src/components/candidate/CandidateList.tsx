import { useEffect, useState, useMemo } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { MapPin, Mail, Phone } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { useCandidates } from "@/hooks/useCandidates";
import { CandidateType } from "@/types/candidate";
import { useAuth } from "@/contexts/AuthContext";
import { Skeleton } from "@/components/ui/skeleton";
import { searchCandidatesOptimized } from "@/utils/optimizedSearch";

interface CandidateListProps {
  searchQuery?: string;
  filters?: any;
}

export function CandidateList({
  searchQuery = "",
  filters = {},
}: CandidateListProps) {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const { data: allCandidates, isLoading: loading, error } = useCandidates();

  // Filter candidates based on search query and filters
  const candidates = useMemo(() => {
    if (!allCandidates) return [];

    // Transform UI filters to SearchFilters format
    const searchFilters = {
      experience: filters.experienceLevel || undefined,
      remoteOnly: filters.locationPreference === "remote",
      skills:
        filters.skills?.map((skill: string) => ({
          name: skill,
          required: false,
        })) || [],
    };

    // Apply filters even without search query
    let filteredCandidates = allCandidates;

    // Apply relationship score filter
    if (filters.relationshipScore && filters.relationshipScore[0] > 0) {
      filteredCandidates = filteredCandidates.filter(
        (candidate) =>
          candidate.relationshipScore >= filters.relationshipScore[0],
      );
    }

    // Apply experience level filter
    if (filters.experienceLevel) {
      filteredCandidates = filteredCandidates.filter((candidate) => {
        const experience = candidate.experience?.toLowerCase() || "";
        switch (filters.experienceLevel) {
          case "junior":
            return (
              experience.includes("junior") ||
              experience.includes("0-2") ||
              experience.includes("entry")
            );
          case "mid":
            return (
              experience.includes("mid") ||
              experience.includes("3-5") ||
              experience.includes("intermediate")
            );
          case "senior":
            return (
              experience.includes("senior") ||
              experience.includes("6-10") ||
              experience.includes("lead")
            );
          case "lead":
            return (
              experience.includes("lead") ||
              experience.includes("10+") ||
              experience.includes("principal")
            );
          default:
            return true;
        }
      });
    }

    // Apply location preference filter
    if (filters.locationPreference) {
      filteredCandidates = filteredCandidates.filter((candidate) => {
        const remotePreference =
          candidate.remotePreference?.toLowerCase() || "";
        const location = candidate.location?.toLowerCase() || "";

        switch (filters.locationPreference) {
          case "remote":
            return (
              remotePreference.includes("remote") || location.includes("remote")
            );
          case "hybrid":
            return (
              remotePreference.includes("hybrid") || location.includes("hybrid")
            );
          case "onsite":
            return (
              remotePreference.includes("onsite") ||
              remotePreference.includes("on-site") ||
              (!remotePreference.includes("remote") &&
                !remotePreference.includes("hybrid"))
            );
          default:
            return true;
        }
      });
    }

    // Apply skills filter
    if (filters.skills && filters.skills.length > 0) {
      filteredCandidates = filteredCandidates.filter((candidate) => {
        // Use normalized_skills if available, fallback to skills
        const skillsToCheck =
          candidate.normalized_skills || candidate.skills || [];
        return filters.skills.some(
          (skill: string) =>
            skillsToCheck.some((candidateSkill) =>
              (typeof candidateSkill === "string"
                ? candidateSkill
                : candidateSkill.name
              )
                .toLowerCase()
                .includes(skill.toLowerCase()),
            ) ||
            candidate.tags.some((tag) =>
              tag.toLowerCase().includes(skill.toLowerCase()),
            ),
        );
      });
    }

    // Apply tags filter
    if (filters.tags && filters.tags.length > 0) {
      filteredCandidates = filteredCandidates.filter((candidate) => {
        // Check both normalized tags and legacy tags
        const normalizedTags = candidate.normalized_tags || [];
        const legacyTags = candidate.tags || [];

        return filters.tags.some((filterTag: string) => {
          // Check normalized tags first
          if (normalizedTags.length > 0) {
            return normalizedTags.some((tag: any) =>
              tag.name.toLowerCase().includes(filterTag.toLowerCase()),
            );
          }
          // Fallback to legacy tags
          return legacyTags.some((tag) =>
            tag.toLowerCase().includes(filterTag.toLowerCase()),
          );
        });
      });
    }

    // If there's a search query, use optimized search on the filtered results
    if (searchQuery && searchQuery.length >= 3) {
      return searchCandidatesOptimized(
        filteredCandidates,
        searchQuery,
        searchFilters,
      );
    }

    return filteredCandidates;
  }, [allCandidates, searchQuery, filters]);

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">Loading...</p>
      </div>
    );
  }

  // Show authentication required message
  if (!user) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">
          Please sign in to view candidates
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-5 w-48" />
                  <Skeleton className="h-4 w-32" />
                  <div className="flex gap-4">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-28" />
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Skeleton className="h-4 w-16" />
                  <div className="flex gap-1">
                    <Skeleton className="h-6 w-12" />
                    <Skeleton className="h-6 w-12" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">
          Error loading candidates
        </p>
      </div>
    );
  }

  if (!candidates || candidates.length === 0) {
    const message =
      searchQuery && searchQuery.length >= 3
        ? `No candidates found matching "${searchQuery}". Try adjusting your search terms.`
        : "No candidates found. Create your first candidate to get started!";

    return (
      <div className="flex items-center justify-center h-32">
        <p className="text-lg text-muted-foreground">{message}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {candidates.map((candidate) => (
        <Card
          key={candidate.id}
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => navigate(`/candidates/${candidate.id}`)}
        >
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <Avatar className="h-12 w-12 sm:h-12 sm:w-12 self-start sm:self-center flex-shrink-0">
                <AvatarImage src={candidate.avatar} alt={candidate.name} />
                <AvatarFallback>
                  {candidate.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg truncate">
                  {candidate.name}
                </h3>
                <p className="text-muted-foreground truncate">
                  {candidate.role}
                </p>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mt-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1 min-w-0">
                    <MapPin className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">
                      {candidate.location || "Location not specified"}
                    </span>
                  </div>
                  <div className="flex items-center gap-1 min-w-0">
                    <Mail className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">{candidate.email}</span>
                  </div>
                  <div className="flex items-center gap-1 min-w-0">
                    <Phone className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">
                      {candidate.phone || "Phone not provided"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex flex-row sm:flex-col justify-between sm:items-end gap-2 sm:gap-2">
                <div className="text-sm font-medium">
                  Score: {candidate.relationshipScore}%
                </div>
                <div className="flex flex-wrap gap-1 justify-end">
                  {(() => {
                    // Handle both normalized tags and legacy string tags
                    const normalizedTags = candidate.normalized_tags || [];
                    const legacyTags = candidate.tags || [];

                    if (normalizedTags.length > 0) {
                      return normalizedTags.slice(0, 3).map((tag: any) => (
                        <Badge
                          key={tag.id || tag.name}
                          variant="secondary"
                          className="text-xs"
                          style={{
                            backgroundColor: tag.color
                              ? `${tag.color}20`
                              : undefined,
                            borderColor: tag.color || undefined,
                            color: tag.color || undefined,
                          }}
                        >
                          {tag.name}
                        </Badge>
                      ));
                    } else {
                      return legacyTags.slice(0, 3).map((tag: string) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ));
                    }
                  })()}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
