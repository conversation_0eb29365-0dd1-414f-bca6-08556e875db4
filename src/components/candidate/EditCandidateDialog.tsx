import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { CandidateType } from "@/types/candidate";
import { EditCandidateForm } from "./EditCandidateForm";

interface EditCandidateDialogProps {
  candidate: CandidateType;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditCandidateDialog({
  candidate,
  open,
  onOpenChange,
  onSuccess,
}: EditCandidateDialogProps) {
  const handleSuccess = () => {
    onOpenChange(false);
    onSuccess?.();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Candidate: {candidate.name}</DialogTitle>
        </DialogHeader>

        <EditCandidateForm candidate={candidate} onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  );
}
