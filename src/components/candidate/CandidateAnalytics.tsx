import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import {
  useCandidateMonthlyChartData,
  useCandidateSkillsChartData,
  useInitializeCandidateAnalytics,
} from "@/hooks/useCandidateAnalytics";
import { useCandidateTagsChartData, useInitializeCandidateTagsAnalytics } from "@/hooks/useCandidateTagsAnalytics";
import { usePipelineTrends } from "@/hooks/usePipelineTrends";
import { useAuth } from "@/contexts/AuthContext";

export function CandidateAnalytics() {
  const { user } = useAuth();
  const userId = user?.id;

  const {
    data: monthlyData = [],
    isLoading: monthlyLoading,
    error: monthlyError,
  } = useCandidateMonthlyChartData(userId || "");

  const {
    data: skillsData = [],
    isLoading: skillsLoading,
    error: skillsError,
  } = useCandidateSkillsChartData(userId || "");

  const {
    data: tagsData = [],
    isLoading: tagsLoading,
    error: tagsError,
  } = useCandidateTagsChartData(userId || "");

  const {
    data: pipelineTrendsData = [],
    isLoading: pipelineLoading,
    error: pipelineError,
  } = usePipelineTrends(userId || "");

  const { initializeAllData, isInitializing } =
    useInitializeCandidateAnalytics();
  const { mutateAsync: initializeTagsData, isPending: isInitializingTags } =
    useInitializeCandidateTagsAnalytics();

  // Initialize data if empty
  useEffect(() => {
    if (
      userId &&
      monthlyData.length === 0 &&
      skillsData.length === 0 &&
      tagsData.length === 0 &&
      !monthlyLoading &&
      !skillsLoading &&
      !tagsLoading &&
      !monthlyError &&
      !skillsError &&
      !tagsError
    ) {
      initializeAllData(userId);
      initializeTagsData(userId);
    }
  }, [
    userId,
    monthlyData.length,
    skillsData.length,
    tagsData.length,
    monthlyLoading,
    skillsLoading,
    tagsLoading,
    monthlyError,
    skillsError,
    tagsError,
    initializeAllData,
    initializeTagsData,
  ]);

  const isLoading = monthlyLoading || skillsLoading || tagsLoading || pipelineLoading || isInitializing || isInitializingTags;
  const hasError = monthlyError || skillsError || tagsError || pipelineError;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Candidates & Placements</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Skills Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tags Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Candidate Pipeline Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-[400px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (hasError) {
    return (
      <Alert className="mb-4">
        <AlertDescription>
          Failed to load candidate analytics:{" "}
          {(monthlyError || skillsError || pipelineError)?.message}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Monthly Candidates & Placements</CardTitle>
          </CardHeader>
          <CardContent>
            {monthlyData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="candidates" fill="#8884d8" name="Candidates" />
                  <Bar dataKey="placements" fill="#82ca9d" name="Placements" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px]">
                <p className="text-muted-foreground">No monthly data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Skills Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {skillsData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={skillsData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {skillsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px]">
                <p className="text-muted-foreground">No skills data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tags Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {tagsData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={tagsData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {tagsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[300px]">
                <p className="text-muted-foreground">No tags data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Candidate Pipeline Trends</CardTitle>
        </CardHeader>
        <CardContent>
          {pipelineTrendsData.length > 0 ? (
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={pipelineTrendsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="applied"
                  stroke="#8884d8"
                  strokeWidth={2}
                  name="Applied"
                />
                <Line
                  type="monotone"
                  dataKey="phone_screen"
                  stroke="#82ca9d"
                  strokeWidth={2}
                  name="Phone Screen"
                />
                <Line
                  type="monotone"
                  dataKey="interview"
                  stroke="#ffc658"
                  strokeWidth={2}
                  name="Interview"
                />
                <Line
                  type="monotone"
                  dataKey="final_round"
                  stroke="#ff7300"
                  strokeWidth={2}
                  name="Final Round"
                />
                <Line
                  type="monotone"
                  dataKey="offer"
                  stroke="#8dd1e1"
                  strokeWidth={2}
                  name="Offer"
                />
                <Line
                  type="monotone"
                  dataKey="hired"
                  stroke="#00c49f"
                  strokeWidth={2}
                  name="Hired"
                />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-[400px]">
              <p className="text-muted-foreground">No pipeline trends data available</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
