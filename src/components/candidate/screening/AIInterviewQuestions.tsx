import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { CandidateType } from "@/types/candidate";
import { generateInterviewQuestions } from "@/utils/gemini";

interface AIInterviewQuestionsProps {
  candidate: CandidateType;
}

interface Question {
  id: string;
  question: string;
  category: "technical" | "behavioral" | "cultural" | "experience";
  difficulty: "easy" | "medium" | "hard";
  rationale: string;
}

export function AIInterviewQuestions({ candidate }: AIInterviewQuestionsProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const { toast } = useToast();

  const generateQuestions = async () => {
    setIsGenerating(true);
    try {
      // Create a context string from candidate information
      const candidateContext = `
Role: ${candidate.role}
Experience: ${candidate.experience}
Skills: ${candidate.skills.map((s) => (typeof s === "string" ? s : s.name)).join(", ")}
Remote Preference: ${candidate.remotePreference || "Not specified"}
Industry: ${candidate.industry || "Not specified"}
      `;

      // Generate personalized interview questions using Gemini
      const generatedQuestions = await generateInterviewQuestions(
        candidate.role,
        candidateContext,
      );

      setQuestions(generatedQuestions);
      toast({
        title: "Questions Generated",
        description:
          "New interview questions have been generated based on the candidate's profile.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          "Failed to generate interview questions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const getDifficultyColor = (difficulty: Question["difficulty"]) => {
    switch (difficulty) {
      case "easy":
        return "bg-success text-success-foreground";
      case "medium":
        return "bg-warning text-warning-foreground";
      case "hard":
        return "bg-destructive text-destructive-foreground";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          AI Interview Questions
        </CardTitle>
        <Button onClick={generateQuestions} disabled={isGenerating}>
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-4 w-4" />
              Generate Questions
            </>
          )}
        </Button>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {questions.map((q) => (
              <Card key={q.id} className="p-4">
                <div className="flex gap-2 mb-2">
                  <Badge>{q.category}</Badge>
                  <Badge className={getDifficultyColor(q.difficulty)}>
                    {q.difficulty}
                  </Badge>
                </div>
                <p className="font-medium mb-2">{q.question}</p>
                <p className="text-sm text-muted-foreground">
                  Rationale: {q.rationale}
                </p>
              </Card>
            ))}
            {questions.length === 0 && !isGenerating && (
              <div className="text-center text-muted-foreground">
                Click "Generate Questions" to create AI-powered interview
                questions based on the candidate's profile.
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
