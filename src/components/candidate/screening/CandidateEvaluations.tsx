import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { useState } from "react";
import { generateText } from "@/utils/gemini";
import { useToast } from "@/hooks/use-toast";

interface EvaluationType {
  category: string;
  score: number;
  notes: string;
}

interface CandidateEvaluationsProps {
  evaluations: EvaluationType[];
  candidate?: {
    name?: string;
    skills?: string[];
    experience?: string;
    education?: string;
    role?: string;
  };
}

export function CandidateEvaluations({
  evaluations,
  candidate,
}: CandidateEvaluationsProps) {
  const { toast } = useToast();
  const [aiSuggestions, setAiSuggestions] = useState<string>("");
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);

  const generateAISuggestions = async () => {
    if (!candidate || isGeneratingSuggestions) return;

    setIsGeneratingSuggestions(true);
    try {
      const evaluationData = evaluations
        .map(
          (evaluation) =>
            `${evaluation.category}: ${evaluation.score}/5 - ${evaluation.notes}`,
        )
        .join("\n");

      const prompt = `
Analyze the following candidate evaluation data and provide strategic insights:

Candidate Profile:
- Name: ${candidate.name || "Not specified"}
- Role: ${candidate.role || "Not specified"}
- Skills: ${candidate.skills?.join(", ") || "Not specified"}
- Experience: ${candidate.experience || "Not specified"}
- Education: ${candidate.education || "Not specified"}

Current Evaluations:
${evaluationData}

Provide analysis including:
1. Overall assessment and strengths/weaknesses
2. Areas for improvement and development
3. Recommendations for interview focus areas
4. Suggestions for additional evaluation criteria
5. Fit assessment for the role

Keep the response actionable and focused on recruitment decision-making.
`;

      const systemPrompt =
        "You are an expert talent assessment consultant specializing in candidate evaluation. Provide strategic insights that help recruitment teams make informed hiring decisions.";

      const suggestions = await generateText(prompt, systemPrompt);
      setAiSuggestions(suggestions);

      toast({
        title: "AI Suggestions Generated",
        description: "Evaluation insights and recommendations are ready.",
      });
    } catch (error) {
      console.error("Error generating AI suggestions:", error);
      toast({
        title: "Suggestions Unavailable",
        description: "Unable to generate AI suggestions at the moment.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingSuggestions(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 4) return "text-green-600";
    if (score >= 3) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadge = (score: number) => {
    if (score >= 4) return "bg-green-100 text-green-800";
    if (score >= 3) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const averageScore =
    evaluations.length > 0
      ? evaluations.reduce((sum, evaluation) => sum + evaluation.score, 0) /
        evaluations.length
      : 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Evaluation Scores
            {averageScore > 0 && (
              <Badge className={getScoreBadge(averageScore)}>
                Avg: {averageScore.toFixed(1)}/5
              </Badge>
            )}
          </CardTitle>
          {candidate && (
            <Button
              variant="outline"
              size="sm"
              onClick={generateAISuggestions}
              disabled={isGeneratingSuggestions}
            >
              {isGeneratingSuggestions ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  AI Insights
                </>
              )}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {evaluations.map((evaluation, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">
                  {evaluation.category}
                </span>
                <div className="flex items-center gap-2">
                  <span
                    className={`text-sm font-medium ${getScoreColor(evaluation.score)}`}
                  >
                    {evaluation.score}/5
                  </span>
                  {evaluation.score >= 4 && (
                    <TrendingUp className="h-3 w-3 text-green-600" />
                  )}
                  {evaluation.score <= 2 && (
                    <AlertTriangle className="h-3 w-3 text-red-600" />
                  )}
                </div>
              </div>
              <div className="h-2 bg-secondary rounded-full overflow-hidden">
                <div
                  className={`h-full ${
                    evaluation.score >= 4
                      ? "bg-green-500"
                      : evaluation.score >= 3
                        ? "bg-yellow-500"
                        : "bg-red-500"
                  }`}
                  style={{ width: `${(evaluation.score / 5) * 100}%` }}
                />
              </div>
              <p className="text-sm text-muted-foreground">
                {evaluation.notes}
              </p>
            </div>
          ))}

          {aiSuggestions && (
            <div className="mt-6 pt-4 border-t">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Sparkles className="h-4 w-4 text-blue-600" />
                  <h3 className="font-medium text-blue-900">
                    AI Evaluation Insights
                  </h3>
                </div>
                <div className="text-sm text-gray-700 whitespace-pre-wrap">
                  {aiSuggestions}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
