import { supabase } from "./supabase/client";
import { Database } from "./supabase/types";

type UserConnection = Database["public"]["Tables"]["user_connections"]["Row"];
type UsageQuota = Database["public"]["Tables"]["usage_quotas"]["Row"];

export interface EmailConfig {
  to: string | string[];
  subject: string;
  body: string;
  html?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export class EmailIntegration {
  private async getActiveConnection(
    userId: string,
  ): Promise<UserConnection | null> {
    const { data } = await supabase
      .from("user_connections")
      .select("*")
      .eq("user_id", userId)
      .in("provider", ["gmail", "outlook"])
      .eq("is_active", true)
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    return data;
  }

  private async checkQuota(userId: string, provider: string): Promise<boolean> {
    const { data: quota } = await supabase
      .from("usage_quotas")
      .select("*")
      .eq("user_id", userId)
      .eq("provider", provider)
      .eq("quota_type", "daily")
      .single();

    if (!quota) return true; // No quota set

    // Reset quota if needed
    if (new Date() > new Date(quota.reset_at)) {
      await supabase
        .from("usage_quotas")
        .update({
          quota_used: 0,
          reset_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        })
        .eq("id", quota.id);
      return true;
    }

    return quota.quota_used < quota.quota_limit;
  }

  private async incrementQuota(
    userId: string,
    provider: string,
  ): Promise<void> {
    await supabase
      .rpc("increment", {
        table_name: "usage_quotas",
        row_id: userId,
        column_name: "quota_used",
        x: 1,
      })
      .throwOnError();
  }

  private async sendViaGmail(
    connection: UserConnection,
    config: EmailConfig,
  ): Promise<void> {
    // In production, this would use Google's Gmail API
    // For now, we'll simulate the call
    console.log(
      `[Gmail] Sending email via ${connection.provider_account_email}`,
    );
    console.log(
      `To: ${Array.isArray(config.to) ? config.to.join(", ") : config.to}`,
    );
    console.log(`Subject: ${config.subject}`);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 100));
  }

  private async sendViaOutlook(
    connection: UserConnection,
    config: EmailConfig,
  ): Promise<void> {
    // In production, this would use Microsoft Graph API
    // For now, we'll simulate the call
    console.log(
      `[Outlook] Sending email via ${connection.provider_account_email}`,
    );
    console.log(
      `To: ${Array.isArray(config.to) ? config.to.join(", ") : config.to}`,
    );
    console.log(`Subject: ${config.subject}`);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 100));
  }

  async sendEmail(
    userId: string,
    config: EmailConfig,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Get active email connection
      const connection = await this.getActiveConnection(userId);

      if (!connection) {
        throw new Error(
          "No active email integration found. Please connect Gmail or Outlook.",
        );
      }

      // Check quota
      const hasQuota = await this.checkQuota(userId, connection.provider);
      if (!hasQuota) {
        throw new Error(
          `Daily email quota exceeded for ${connection.provider}`,
        );
      }

      // Check if token is expired
      if (
        connection.token_expires_at &&
        new Date() > new Date(connection.token_expires_at)
      ) {
        // In production, we would refresh the token here
        throw new Error("Email integration token expired. Please reconnect.");
      }

      // Send email based on provider
      switch (connection.provider) {
        case "gmail":
          await this.sendViaGmail(connection, config);
          break;
        case "outlook":
          await this.sendViaOutlook(connection, config);
          break;
        default:
          throw new Error(`Unsupported email provider: ${connection.provider}`);
      }

      // Increment quota
      await this.incrementQuota(userId, connection.provider);

      return { success: true };
    } catch (error) {
      console.error("Email send error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to send email",
      };
    }
  }

  // Mock email sender for fallback
  async sendMockEmail(config: EmailConfig): Promise<{ success: boolean }> {
    console.log("[Mock] Sending email");
    console.log(
      `To: ${Array.isArray(config.to) ? config.to.join(", ") : config.to}`,
    );
    console.log(`Subject: ${config.subject}`);
    console.log(`Body: ${config.body}`);

    return { success: true };
  }
}

export default new EmailIntegration();
