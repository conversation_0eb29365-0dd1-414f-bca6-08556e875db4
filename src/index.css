@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* Sidebar variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* Sidebar variables for dark mode */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }
}

/* Ensure sidebar background is properly applied */
[data-sidebar="sidebar"] {
  background-color: hsl(var(--sidebar-background)) !important;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* React Day Picker Calendar Fixes */
.rdp {
  --rdp-accent-color: hsl(var(--primary));
  --rdp-background-color: hsl(var(--background));
  --rdp-accent-color-dark: hsl(var(--primary));
  --rdp-background-color-dark: hsl(var(--background));
  --rdp-outline: 2px solid hsl(var(--ring));
  --rdp-outline-selected: 2px solid hsl(var(--ring));
  --rdp-selected-color: hsl(var(--primary-foreground));
}

.rdp-month_grid {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.rdp-weekdays {
  display: table-header-group;
}

.rdp-weekday {
  display: table-cell;
  width: 14.285714%;
  text-align: center;
  vertical-align: middle;
  padding: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.rdp-week {
  display: table-row;
}

.rdp-day {
  display: table-cell;
  width: 14.285714%;
  text-align: center;
  vertical-align: middle;
  padding: 0;
  position: relative;
}

.rdp-day_button {
  width: 2.25rem;
  height: 2.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 400;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  color: hsl(var(--foreground));
}

.rdp-day_button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rdp-day_button:focus {
  outline: var(--rdp-outline);
  outline-offset: 2px;
}

.rdp-selected .rdp-day_button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-selected .rdp-day_button:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-today .rdp-day_button {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  font-weight: 500;
}

.rdp-outside .rdp-day_button {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
}

.rdp-disabled .rdp-day_button {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-disabled .rdp-day_button:hover {
  background: transparent;
}

.rdp-range_start .rdp-day_button,
.rdp-range_end .rdp-day_button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-range_middle .rdp-day_button {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Dark mode adjustments */
.dark .rdp-day_button {
  color: hsl(var(--foreground));
}

.dark .rdp-weekday {
  color: hsl(var(--muted-foreground));
}
