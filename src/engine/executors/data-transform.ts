/**
 * Data Transform Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";

export class DataTransformExecutor extends BaseExecutor {
  id = "data-transform";
  name = "Data Transform";
  description = "Transform data between steps";
  category = "transformation" as const;

  configSchema = {
    transformType: {
      type: "string",
      label: "Transform Type",
      required: true,
      options: ["extract", "format", "merge", "split", "custom"],
    },
    extractPath: {
      type: "string",
      label: "Extract Path",
      required: false,
      placeholder: "e.g., lastResult.candidateId",
    },
    formatTemplate: {
      type: "text",
      label: "Format Template",
      required: false,
      placeholder: "e.g., Candidate {{name}} - {{status}}",
    },
    mergeStrategy: {
      type: "string",
      label: "Merge Strategy",
      required: false,
      options: ["concat", "override", "deep"],
    },
    customTransform: {
      type: "text",
      label: "Custom Transform (JavaScript)",
      required: false,
      placeholder: "// return transformed data",
    },
  };

  protected async executeInternal(
    nodeData: Record<string, unknown>,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const transformType =
        (config as Record<string, unknown>).transformType || "extract";
      let transformedData: unknown = null;

      switch (transformType) {
        case "extract":
          // Extract data from context using path
          transformedData = this.extractFromPath(
            context,
            ((config as Record<string, unknown>).extractPath as string) ||
              "lastResult",
          );
          break;

        case "format":
          // Format data using template
          transformedData = this.formatData(
            context,
            ((config as Record<string, unknown>).formatTemplate as string) ||
              "",
          );
          break;

        case "merge":
          // Merge multiple data sources
          transformedData = this.mergeData(
            context,
            ((config as Record<string, unknown>).mergeStrategy as string) ||
              "concat",
          );
          break;

        case "split":
          // Split data into array
          transformedData = this.splitData(context);
          break;

        case "custom":
          // Execute custom transform (safely)
          transformedData = this.customTransform(
            context,
            ((config as Record<string, unknown>).customTransform as string) ||
              "",
          );
          break;
      }

      return {
        success: true,
        data: {
          transformType,
          transformedData,
          originalContext: context,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  private extractFromPath(context: ExecutionContext, path: string): unknown {
    const parts = path.split(".");
    let current: unknown = context;

    for (const part of parts) {
      if (
        current &&
        typeof current === "object" &&
        current !== null &&
        part in current
      ) {
        current = (current as Record<string, unknown>)[part];
      } else {
        return null;
      }
    }

    return current;
  }

  private formatData(context: ExecutionContext, template: string): string {
    let formatted = template;

    // Replace placeholders with actual values
    const regex = /\{\{(\w+)\}\}/g;
    formatted = formatted.replace(regex, (match, key) => {
      const value = this.extractFromPath(context, key);
      return value !== null ? String(value) : match;
    });

    return formatted;
  }

  private mergeData(context: ExecutionContext, strategy: string): unknown {
    const results: unknown[] = [];

    // Collect all results from context
    Object.keys(context).forEach((key) => {
      if (key.endsWith("Result") || key.startsWith("node_")) {
        results.push(context[key]);
      }
    });

    switch (strategy) {
      case "concat":
        return results;

      case "override":
        return Object.assign({}, ...results);

      case "deep":
        return this.deepMerge(...results);

      default:
        return results;
    }
  }

  private deepMerge(...objects: unknown[]): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const obj of objects) {
      if (obj && typeof obj === "object" && obj !== null) {
        const objRecord = obj as Record<string, unknown>;
        for (const key in objRecord) {
          if (Object.prototype.hasOwnProperty.call(objRecord, key)) {
            if (
              typeof objRecord[key] === "object" &&
              !Array.isArray(objRecord[key]) &&
              objRecord[key] !== null
            ) {
              result[key] = this.deepMerge(result[key] || {}, objRecord[key]);
            } else {
              result[key] = objRecord[key];
            }
          }
        }
      }
    }

    return result;
  }

  private splitData(context: ExecutionContext): unknown[] {
    const lastResult = context.lastResult;

    if (Array.isArray(lastResult)) {
      return lastResult;
    } else if (typeof lastResult === "string") {
      return lastResult.split(",").map((s) => s.trim());
    } else if (lastResult && typeof lastResult === "object") {
      return Object.values(lastResult);
    }

    return [lastResult];
  }

  private customTransform(
    context: ExecutionContext,
    customCode: string,
  ): unknown {
    try {
      // Create a safe function that only has access to the context
      const transformFunction = new Function(
        "context",
        `
        const { lastResult, candidateId, jobId } = context;
        ${customCode}
      `,
      );

      return transformFunction(context);
    } catch (error) {
      console.error("Error in custom transform:", error);
      return context.lastResult;
    }
  }
}
