/**
 * Education Check Condition Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";
import { supabase } from "@/integrations/supabase/client";
import { generateText } from "@/utils/gemini";

export class EducationCheckExecutor extends BaseExecutor {
  id = "education-check";
  name = "Education Check";
  description = "Verify education requirements";
  category = "condition" as const;

  configSchema = {
    minDegree: {
      type: "string",
      label: "Minimum Degree Required",
      required: true,
      options: ["high-school", "bachelors", "masters", "phd"],
      default: "bachelors",
    },
    useAIAnalysis: {
      type: "boolean",
      label: "Use AI-Powered Education Analysis",
      required: false,
      default: false,
      description:
        "Enable intelligent education evaluation that considers equivalencies, relevance, and quality of education",
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId =
        context.candidateId || (context.lastResult as any)?.candidateId;
      const minDegree = config.minDegree || "bachelors";
      const useAIAnalysis = config.useAIAnalysis || false;

      if (!candidateId && !context.lastResult) {
        return {
          success: true,
          data: { result: false, reason: "No candidate data available" },
        };
      }

      const degreeHierarchy = {
        "high-school": 1,
        bachelors: 2,
        masters: 3,
        phd: 4,
      };

      const minDegreeLevel = degreeHierarchy[minDegree] || 2;
      let candidateDegreeLevel = 0;
      let candidateDegree = "unknown";

      if (candidateId) {
        // Fetch from database
        const { data: candidate, error } = await supabase
          .from("candidates")
          .select("education, ai_summary")
          .eq("id", candidateId)
          .single();

        if (error) {
          throw new Error(`Failed to fetch candidate: ${error.message}`);
        }

        let aiAnalysis: string | undefined;

        if (useAIAnalysis) {
          // Use AI-powered education analysis
          try {
            const educationText = candidate?.education || "";
            const aiSummary = candidate?.ai_summary || "";

            const prompt = `
Analyze the following candidate education data and determine if they meet the minimum education requirement:

Required Education Level: ${minDegree}
Candidate Education: ${educationText}
AI Summary: ${aiSummary}

Consider:
1. Degree level and equivalencies
2. Relevance of field of study
3. Institution quality and accreditation
4. Professional certifications and continuing education
5. International degree equivalencies

Degree hierarchy for reference:
- high-school: High school diploma or equivalent
- bachelors: Bachelor's degree or equivalent
- masters: Master's degree, MBA, or equivalent
- phd: PhD, Doctorate, or equivalent

Provide a detailed analysis and determine if the candidate meets the ${minDegree} requirement.

Return in JSON format:
{
  "meetsRequirement": true/false,
  "degreeLevel": "detected degree level",
  "analysis": "Detailed explanation of education assessment"
}
`;

            const systemPrompt =
              "You are an expert education analyst specializing in academic credential evaluation. Provide accurate, objective assessments of candidate education based on comprehensive analysis.";

            const response = await generateText(prompt, systemPrompt);
            const cleanedResponse = response
              .replace(/```json\s*/, "")
              .replace(/```\s*$/, "")
              .trim();
            const aiResult = JSON.parse(cleanedResponse);

            candidateDegree = aiResult.degreeLevel || "unknown";
            candidateDegreeLevel = degreeHierarchy[candidateDegree] || 0;
            aiAnalysis = aiResult.analysis;

            // Use AI determination if available
            if (aiResult.meetsRequirement !== undefined) {
              const result = aiResult.meetsRequirement;
              return {
                success: true,
                data: {
                  result,
                  candidateDegree,
                  candidateDegreeLevel,
                  minDegree,
                  minDegreeLevel,
                  aiAnalysis,
                  useAIAnalysis: true,
                },
              };
            }
          } catch (error) {
            console.error(
              "AI education analysis failed, falling back to basic parsing:",
              error,
            );
            // Fall back to basic parsing
          }
        }

        // Basic education parsing (fallback or when AI is disabled)
        const educationText = (
          candidate?.education ||
          candidate?.ai_summary ||
          ""
        ).toLowerCase();

        if (
          educationText.includes("phd") ||
          educationText.includes("doctorate")
        ) {
          candidateDegreeLevel = 4;
          candidateDegree = "phd";
        } else if (
          educationText.includes("master") ||
          educationText.includes("mba") ||
          educationText.includes("m.s") ||
          educationText.includes("m.a")
        ) {
          candidateDegreeLevel = 3;
          candidateDegree = "masters";
        } else if (
          educationText.includes("bachelor") ||
          educationText.includes("b.s") ||
          educationText.includes("b.a") ||
          educationText.includes("undergraduate")
        ) {
          candidateDegreeLevel = 2;
          candidateDegree = "bachelors";
        } else if (
          educationText.includes("high school") ||
          educationText.includes("diploma")
        ) {
          candidateDegreeLevel = 1;
          candidateDegree = "high-school";
        }
      }

      const result = candidateDegreeLevel >= minDegreeLevel;

      return {
        success: true,
        data: {
          result,
          candidateDegree,
          candidateDegreeLevel,
          minDegree,
          minDegreeLevel,
          threshold: minDegree,
          useAIAnalysis: false,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }
}
