/**
 * AI Screen Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";
import { supabase } from "@/integrations/supabase/client";
import { screenCandidate } from "@/utils/gemini";
import { CandidatesService, ActivityService } from "@/services";

export class AIScreenExecutor extends BaseExecutor {
  id = "ai-screen";
  name = "AI Screen";
  description = "Screen candidate using AI";
  category = "action" as const;

  configSchema = {
    criteria: {
      type: "string",
      label: "Screening Criteria",
      required: true,
      options: [
        "comprehensive",
        "technical",
        "cultural_fit",
        "document_verification",
      ],
    },
    minScore: {
      type: "number",
      label: "Minimum Score (%)",
      required: true,
      default: 75,
      min: 0,
      max: 100,
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId =
        (context.candidateId as string) || ((context.lastResult as any)?.candidateId as string);
      const jobId = (context.jobId as string) || ((context.lastResult as any)?.jobId as string);

      if (!candidateId) {
        throw new Error("No candidate ID found in context");
      }

      // Get candidate details
      const candidate = await CandidatesService.getCandidate(candidateId);

      if (!candidate) {
        throw new Error("Candidate not found");
      }

      // Get job details if available
      let jobDescription = "";
      if (jobId) {
        const { data: job, error: jobError } = await supabase
          .from("jobs")
          .select("*")
          .eq("id", jobId)
          .single();

        if (!jobError && job) {
          jobDescription = job.description;
        }
      }

      // Perform AI screening
      const screeningResult = await screenCandidate(
        candidate,
        jobDescription || `${candidate.role} position`,
      );

      // Create activity entry
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user) {
        await ActivityService.createActivityEntry({
          candidate_id: candidateId,
          user_id: user.id,
          activity_type: "ai_screening",
          title: "AI Screening Completed",
          description: `AI screening completed with score: ${screeningResult.score}%. Criteria: ${config.criteria || "comprehensive"}`,
          metadata: {
            score: screeningResult.score,
            criteria: config.criteria,
            min_score: config.minScore,
            passed: screeningResult.score >= parseInt(config.minScore || "75", 10),
            status: "completed",
          },
        });
      }

      const passed =
        screeningResult.score >= parseInt(config.minScore || "75", 10);

      return {
        success: true,
        data: {
          score: screeningResult.score,
          passed,
          candidateId,
          candidateName: candidate.name,
          criteria: config.criteria,
          minScore: config.minScore,
          jobId,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!((context.candidateId as string) || ((context.lastResult as any)?.candidateId as string));
  }
}
