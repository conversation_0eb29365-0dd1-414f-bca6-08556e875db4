/**
 * Scheduled Trigger Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";

export class ScheduledTriggerExecutor extends BaseExecutor {
  id = "scheduled-trigger";
  name = "Schedule";
  description = "Triggers at scheduled times";
  category = "trigger" as const;

  configSchema = {
    scheduleType: {
      type: "string",
      label: "Schedule Type",
      required: true,
      options: ["interval", "cron", "daily", "weekly", "monthly"],
    },
    interval: {
      type: "number",
      label: "Interval (minutes)",
      required: false,
      min: 1,
      placeholder: "For interval type",
    },
    cronExpression: {
      type: "string",
      label: "Cron Expression",
      required: false,
      placeholder: "For cron type (e.g., 0 9 * * MON-FRI)",
    },
    timeOfDay: {
      type: "string",
      label: "Time of Day",
      required: false,
      placeholder: "For daily type (e.g., 09:00)",
    },
    dayOfWeek: {
      type: "string",
      label: "Day of Week",
      required: false,
      options: [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
      ],
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};

      // In a real implementation, this would be triggered by a scheduler
      // For now, it validates the configuration and returns success
      return {
        success: true,
        data: {
          triggered: true,
          type: "scheduled-trigger",
          timestamp: new Date().toISOString(),
          scheduleType: config.scheduleType,
          scheduleConfig: {
            interval: config.interval,
            cronExpression: config.cronExpression,
            timeOfDay: config.timeOfDay,
            dayOfWeek: config.dayOfWeek,
          },
          context: { ...context },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  validate(nodeData: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!nodeData) {
      errors.push("Node data is required");
    }

    const config = nodeData?.config || {};

    if (!config.scheduleType) {
      errors.push("Schedule type is required");
    } else {
      // Validate based on schedule type
      switch (config.scheduleType) {
        case "interval":
          if (!config.interval || config.interval < 1) {
            errors.push("Interval must be at least 1 minute");
          }
          break;
        case "cron":
          if (!config.cronExpression) {
            errors.push("Cron expression is required for cron schedule type");
          }
          break;
        case "daily":
          if (!config.timeOfDay) {
            errors.push("Time of day is required for daily schedule type");
          }
          break;
        case "weekly":
          if (!config.dayOfWeek || !config.timeOfDay) {
            errors.push(
              "Day of week and time are required for weekly schedule type",
            );
          }
          break;
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  canExecute(context: ExecutionContext): boolean {
    // Triggers can always execute
    return true;
  }
}
