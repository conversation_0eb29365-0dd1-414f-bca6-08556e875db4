/**
 * New Application Trigger Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";

export class NewApplicationTriggerExecutor extends BaseExecutor {
  id = "new-application";
  name = "New Application";
  description = "Triggers when a new candidate applies";
  category = "trigger" as const;

  configSchema = {
    jobId: {
      type: "string",
      label: "Job ID (optional)",
      required: false,
      placeholder: "Leave empty for any job",
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};

      // In a real implementation, this would be triggered by an event
      // For now, it just validates and returns success
      return {
        success: true,
        data: {
          triggered: true,
          type: "new-application",
          timestamp: new Date().toISOString(),
          jobId: config.jobId || context.jobId,
          candidateId: context.candidateId,
          context: { ...context },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  validate(nodeData: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!nodeData) {
      errors.push("Node data is required");
    }

    // Triggers are always valid as entry points
    return {
      valid: errors.length === 0,
      errors,
    };
  }

  canExecute(context: ExecutionContext): boolean {
    // Triggers can always execute
    return true;
  }
}
