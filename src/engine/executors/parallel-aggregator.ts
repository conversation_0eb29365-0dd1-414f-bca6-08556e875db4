/**
 * Parallel Aggregator Executor - Demonstrates wait aggregation pattern
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";

export class ParallelAggregatorExecutor extends BaseExecutor {
  id = "parallel-aggregator";
  name = "Parallel Aggregator";
  description = "Wait for multiple parallel executions and aggregate results";
  category = "transformation" as const;

  configSchema = {
    aggregationType: {
      type: "string",
      label: "Aggregation Type",
      required: true,
      default: "all-success",
      options: [
        "all-success",
        "any-success",
        "majority-success",
        "collect-all",
      ],
    },
    waitTimeout: {
      type: "number",
      label: "Wait Timeout (ms)",
      required: false,
      default: 30000,
      min: 1000,
      max: 120000,
    },
    majorityThreshold: {
      type: "number",
      label: "Majority Threshold (%)",
      required: false,
      default: 50,
      min: 1,
      max: 100,
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const {
        aggregationType = "all-success",
        waitTimeout = 30000,
        majorityThreshold = 50,
      } = config;

      // Collect parallel execution results from context
      // In a real implementation, this would receive results from parallel edges
      const parallelResults = this.collectParallelResults(context);

      if (parallelResults.length === 0) {
        return {
          success: true,
          data: {
            aggregationType,
            message: "No parallel results to aggregate",
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Apply aggregation logic
      let aggregationResult: boolean;
      const aggregationData: any = {
        totalNodes: parallelResults.length,
        successfulNodes: 0,
        failedNodes: 0,
        results: [],
      };

      switch (aggregationType) {
        case "all-success":
          aggregationResult = parallelResults.every((r) => r.success);
          break;

        case "any-success":
          aggregationResult = parallelResults.some((r) => r.success);
          break;

        case "majority-success": {
          const successCount = parallelResults.filter((r) => r.success).length;
          const successPercentage =
            (successCount / parallelResults.length) * 100;
          aggregationResult = successPercentage >= majorityThreshold;
          aggregationData.successPercentage = successPercentage;
          aggregationData.threshold = majorityThreshold;
          break;
        }

        case "collect-all": {
          // Always succeed and collect all results
          aggregationResult = true;
          aggregationData.collectedResults = parallelResults;
          break;
        }

        default:
          aggregationResult = false;
      }

      // Count successes and failures
      aggregationData.successfulNodes = parallelResults.filter(
        (r) => r.success,
      ).length;
      aggregationData.failedNodes = parallelResults.filter(
        (r) => !r.success,
      ).length;
      aggregationData.results = parallelResults.map((r: any) => ({
        nodeId: r.nodeId,
        success: r.success,
        data: r.data,
        error: r.error?.message,
      }));

      return {
        success: aggregationResult,
        data: {
          aggregationType,
          aggregationPassed: aggregationResult,
          ...aggregationData,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  /**
   * Collect parallel results from context
   * In a real implementation, this would be populated by the execution engine
   */
  private collectParallelResults(context: ExecutionContext): any[] {
    // Look for parallel execution results in context
    const results = [];

    // Check for results from previous parallel nodes
    for (const key in context) {
      if (key.startsWith("parallel_") || key.includes("_result")) {
        const result = context[key];
        if (result && typeof result === "object" && "success" in result) {
          results.push({
            nodeId: key,
            success: (result as any).success,
            data: (result as any).data || result,
            error: (result as any).error,
          });
        }
      }
    }

    return results;
  }

  validate(nodeData: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = nodeData?.config || {};

    const validTypes = [
      "all-success",
      "any-success",
      "majority-success",
      "collect-all",
    ];
    if (
      config.aggregationType &&
      !validTypes.includes(config.aggregationType)
    ) {
      errors.push("Invalid aggregation type");
    }

    if (
      config.waitTimeout &&
      (config.waitTimeout < 1000 || config.waitTimeout > 120000)
    ) {
      errors.push("Wait timeout must be between 1 second and 2 minutes");
    }

    if (
      config.majorityThreshold &&
      (config.majorityThreshold < 1 || config.majorityThreshold > 100)
    ) {
      errors.push("Majority threshold must be between 1 and 100");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  canExecute(context: ExecutionContext): boolean {
    // This node can execute if there are any results in the context
    return true;
  }
}
