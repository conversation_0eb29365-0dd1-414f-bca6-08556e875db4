/**
 * Send Email Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";
import EmailIntegration from "@/integrations/email";
import {
  MessagingService,
  CandidatesService,
  ActivityService,
} from "@/services";

export class SendEmailExecutor extends BaseExecutor {
  id = "send-email";
  name = "Send Email";
  description = "Send an email to a candidate";
  category = "action" as const;

  configSchema = {
    template: {
      type: "string",
      label: "Email Template",
      required: true,
      options: ["welcome", "interview", "rejection", "offer", "custom"],
    },
    customMessage: {
      type: "text",
      label: "Custom Message",
      required: false,
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId =
        (context.candidateId as string) || ((context.lastResult as any)?.candidateId as string);

      if (!candidateId) {
        throw new Error("No candidate ID found in context");
      }

      // Get candidate details
      const candidate = await CandidatesService.getCandidate(candidateId);

      if (!candidate) {
        throw new Error("Candidate not found");
      }

      // Get email template
      let templateContent = config.customMessage || "";
      if (config.template && !config.customMessage) {
        const templateData = await MessagingService.getMessageTemplate(
          config.template,
        );
        if (templateData) {
          templateContent = templateData.content;
        }
      }

      // Send email
      const result = await EmailIntegration.sendEmail((context as any).user.id, {
        to: candidate.email,
        subject: `Job Opportunity: ${config.template}`,
        body: templateContent,
      });

      if (!result.success) {
        throw new Error(result.error || "Failed to send email");
      }

      // Create activity entry for email sent
      await ActivityService.createActivityEntry({
        candidate_id: candidateId,
        user_id: (context as any).user.id,
        activity_type: "email",
        title: "Email Sent",
        description: `Email sent using template: ${config.template}`,
        metadata: {
          template: config.template,
          status: "sent",
        },
      });

      return {
        success: true,
        data: {
          sent: true,
          candidateId,
          candidateName: candidate.name,
          candidateEmail: candidate.email,
          template: config.template,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!((context.candidateId as string) || ((context.lastResult as any)?.candidateId as string));
  }
}
