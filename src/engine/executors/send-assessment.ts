/**
 * Send Assessment Executor
 */

import { BaseExecutor } from "../BaseExecutor";
import { NodeExecutorResult, ExecutionContext } from "../types";
import { supabase } from "@/integrations/supabase/client";
import { ActivityService } from "@/services";

export class SendAssessmentExecutor extends BaseExecutor {
  id = "send-assessment";
  name = "Send Assessment";
  description = "Send skills assessment test";
  category = "action" as const;

  configSchema = {
    assessmentType: {
      type: "string",
      label: "Assessment Type",
      required: true,
      options: ["technical", "personality", "cognitive", "skills", "custom"],
    },
    timeLimit: {
      type: "number",
      label: "Time Limit (minutes)",
      required: true,
      default: 90,
      min: 15,
      max: 480,
    },
    requireWebcam: {
      type: "boolean",
      label: "Require Webcam",
      default: false,
    },
    passingScore: {
      type: "number",
      label: "Passing Score (%)",
      required: true,
      default: 70,
      min: 0,
      max: 100,
    },
    assessmentUrl: {
      type: "string",
      label: "Assessment URL",
      required: false,
      placeholder: "Link to external assessment",
    },
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext,
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId =
        (context.candidateId as string) || ((context.lastResult as any)?.candidateId as string);

      if (!candidateId) {
        throw new Error("No candidate ID found in context");
      }

      // Get current user
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }

      // Get candidate details
      const { data: candidate, error: candidateError } = await supabase
        .from("candidates")
        .select("name, email")
        .eq("id", candidateId)
        .single();

      if (candidateError) {
        throw new Error(`Failed to fetch candidate: ${candidateError.message}`);
      }

      // Calculate due date (5 days from now)
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 5);

      // Generate assessment token (in production, this would be a secure token)
      const assessmentToken = `ASMT-${candidateId}-${Date.now()}`;

      // Create assessment record in candidate_notes (as a temporary solution)
      const assessmentData = {
        type: "assessment",
        assessmentType: config.assessmentType,
        timeLimit: config.timeLimit,
        requireWebcam: config.requireWebcam,
        passingScore: config.passingScore,
        dueDate: dueDate.toISOString(),
        status: "sent",
        token: assessmentToken,
        url:
          config.assessmentUrl ||
          `https://assessments.example.com/${assessmentToken}`,
      };

      const { error: noteError } = await supabase
        .from("candidate_notes")
        .insert({
          candidate_id: candidateId,
          user_id: user.id,
          note: JSON.stringify(assessmentData),
          created_at: new Date().toISOString(),
        });

      if (noteError) {
        throw new Error(`Failed to record assessment: ${noteError.message}`);
      }

      // Create activity entry
      await ActivityService.createActivityEntry({
        candidate_id: candidateId,
        user_id: user.id,
        activity_type: "assessment",
        title: `${config.assessmentType} Assessment Sent`,
        description: `Assessment sent to candidate. Due: ${dueDate.toLocaleDateString()}. Time limit: ${config.timeLimit} minutes.`,
        metadata: {
          assessment_type: config.assessmentType,
          time_limit: config.timeLimit,
          require_webcam: config.requireWebcam,
          passing_score: config.passingScore,
          due_date: dueDate.toISOString(),
          status: "pending",
        },
      });

      // Send notification email
      const { error: messageError } = await supabase.from("messages").insert({
        sender_name: "Assessment System",
        sender_email: "<EMAIL>",
        sender_role: "System",
        content: `Assessment Notification: ${config.assessmentType} assessment has been sent to ${candidate?.name} (${candidate?.email}). Due date: ${dueDate.toLocaleDateString()}`,
        status: "unread",
        user_id: user.id,
      });

      return {
        success: true,
        data: {
          sent: true,
          candidateId,
          candidateName: candidate?.name,
          candidateEmail: candidate?.email,
          assessmentType: config.assessmentType,
          timeLimit: config.timeLimit,
          requireWebcam: config.requireWebcam,
          passingScore: config.passingScore,
          dueDate: dueDate.toISOString(),
          assessmentToken,
          assessmentUrl: assessmentData.url,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
      };
    }
  }

  canExecute(context: ExecutionContext): boolean {
    return !!((context.candidateId as string) || ((context.lastResult as any)?.candidateId as string));
  }
}
