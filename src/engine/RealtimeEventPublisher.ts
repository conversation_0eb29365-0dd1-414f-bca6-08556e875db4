/**
 * Realtime Event Publisher
 * Publishes workflow execution events to Supabase Realtime channels
 */

import { supabase } from "@/integrations/supabase/client";
import { RealtimeChannel } from "@supabase/supabase-js";
import { ExecutionEvent } from "./types";

export class RealtimeEventPublisher {
  private static instance: RealtimeEventPublisher;
  private channels: Map<string, RealtimeChannel> = new Map();
  private eventQueue: Map<string, ExecutionEvent[]> = new Map();
  private publishInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // Start batch publishing
    this.startBatchPublishing();
  }

  public static getInstance(): RealtimeEventPublisher {
    if (!RealtimeEventPublisher.instance) {
      RealtimeEventPublisher.instance = new RealtimeEventPublisher();
    }
    return RealtimeEventPublisher.instance;
  }

  /**
   * Subscribe to a channel for publishing events
   */
  public subscribeToChannel(channelName: string): RealtimeChannel {
    if (!this.channels.has(channelName)) {
      const channel = supabase.channel(channelName, {
        config: {
          broadcast: {
            self: true,
            ack: true,
          },
        },
      });

      channel.subscribe((status) => {
        console.log(`Channel ${channelName} subscription status:`, status);
      });

      this.channels.set(channelName, channel);
      this.eventQueue.set(channelName, []);
    }

    return this.channels.get(channelName)!;
  }

  /**
   * Unsubscribe from a channel
   */
  public async unsubscribeFromChannel(channelName: string): Promise<void> {
    const channel = this.channels.get(channelName);
    if (channel) {
      await channel.unsubscribe();
      this.channels.delete(channelName);
      this.eventQueue.delete(channelName);
    }
  }

  /**
   * Publish an event to a channel
   */
  public async publishEvent(
    channelName: string,
    event: ExecutionEvent,
  ): Promise<void> {
    // Ensure channel exists
    this.subscribeToChannel(channelName);

    // Add to queue for batch publishing
    const queue = this.eventQueue.get(channelName) || [];
    queue.push(event);
    this.eventQueue.set(channelName, queue);

    // For critical events, publish immediately
    if (event.type === "error" || event.type === "started") {
      await this.flushChannel(channelName);
    }
  }

  /**
   * Publish multiple events
   */
  public async publishEvents(
    channelName: string,
    events: ExecutionEvent[],
  ): Promise<void> {
    for (const event of events) {
      await this.publishEvent(channelName, event);
    }
  }

  /**
   * Start batch publishing interval
   */
  private startBatchPublishing(): void {
    // Smart batching: Publish queued events every 500ms
    this.publishInterval = setInterval(() => {
      this.flushAllChannels();
    }, 500);

    // Immediate flush for browser events
    if (typeof window !== "undefined") {
      // Flush before page unload
      window.addEventListener("beforeunload", () => {
        this.flushAllChannels();
      });

      // Flush when switching tabs to ensure events are sent
      document.addEventListener("visibilitychange", () => {
        if (document.hidden) {
          this.flushAllChannels();
        }
      });
    }
  }

  /**
   * Stop batch publishing
   */
  public stopBatchPublishing(): void {
    if (this.publishInterval) {
      clearInterval(this.publishInterval);
      this.publishInterval = null;
    }
  }

  /**
   * Flush all channels
   */
  private async flushAllChannels(): Promise<void> {
    const flushPromises = Array.from(this.channels.keys()).map((channelName) =>
      this.flushChannel(channelName),
    );
    await Promise.all(flushPromises);
  }

  /**
   * Flush events for a specific channel
   */
  private async flushChannel(channelName: string): Promise<void> {
    const channel = this.channels.get(channelName);
    const queue = this.eventQueue.get(channelName);

    if (!channel || !queue || queue.length === 0) {
      return;
    }

    // Get events to publish and clear queue
    const eventsToPublish = [...queue];
    this.eventQueue.set(channelName, []);

    // Batch publish events
    try {
      await channel.send({
        type: "broadcast",
        event: "workflow_events",
        payload: {
          events: eventsToPublish,
          timestamp: new Date().toISOString(),
        },
      });

      console.log(
        `Published ${eventsToPublish.length} events to channel ${channelName}`,
      );
    } catch (error) {
      console.error(
        `Error publishing events to channel ${channelName}:`,
        error,
      );

      // Re-queue failed events
      const currentQueue = this.eventQueue.get(channelName) || [];
      this.eventQueue.set(channelName, [...eventsToPublish, ...currentQueue]);
    }
  }

  /**
   * Get channel statistics
   */
  public getChannelStats(channelName: string): {
    subscribed: boolean;
    queueSize: number;
  } {
    return {
      subscribed: this.channels.has(channelName),
      queueSize: this.eventQueue.get(channelName)?.length || 0,
    };
  }

  /**
   * Get all channel statistics
   */
  public getAllChannelStats(): Map<
    string,
    { subscribed: boolean; queueSize: number }
  > {
    const stats = new Map<string, { subscribed: boolean; queueSize: number }>();

    for (const [channelName] of this.channels) {
      stats.set(channelName, this.getChannelStats(channelName));
    }

    return stats;
  }

  /**
   * Cleanup and unsubscribe from all channels
   */
  public async cleanup(): Promise<void> {
    this.stopBatchPublishing();

    const unsubscribePromises = Array.from(this.channels.keys()).map(
      (channelName) => this.unsubscribeFromChannel(channelName),
    );

    await Promise.all(unsubscribePromises);
  }
}

// Export singleton instance
export const realtimePublisher = RealtimeEventPublisher.getInstance();
