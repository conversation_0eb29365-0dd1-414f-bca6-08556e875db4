/// <reference types="cypress" />

describe("Edit Candidate Flow - Comprehensive Tests", () => {
  // Mock candidate data
  const testCandidate = {
    id: "test-123",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Software Engineer",
    phone: "+1234567890",
    location: "New York, NY",
    linkedinUrl: "https://linkedin.com/in/johndoe",
  };

  beforeEach(() => {
    // Visit the candidates page
    cy.visit("/candidates");

    // Wait for candidates to load
    cy.get('[data-testid="candidates-list"]', { timeout: 10000 }).should(
      "exist",
    );

    // Click on the first candidate to view details
    cy.get('[data-testid="candidate-card"]').first().click();

    // Wait for candidate details page to load
    cy.get('[data-testid="candidate-details"]', { timeout: 10000 }).should(
      "exist",
    );
  });

  describe("Happy Path - Edit Candidate Successfully", () => {
    it("should edit all candidate fields successfully", () => {
      // Click edit button
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Verify dialog opened
      cy.get('[role="dialog"]').should("be.visible");
      cy.contains("Edit Candidate").should("be.visible");

      // Update basic information
      cy.get('input[name="name"]').clear().type("Jane Smith");
      cy.get('input[name="email"]').clear().type("<EMAIL>");
      cy.get('input[name="role"]').clear().type("Senior Software Engineer");
      cy.get('input[name="phone"]').clear().type("+9876543210");
      cy.get('input[name="location"]').clear().type("San Francisco, CA");

      // Update experience level
      cy.get('select[name="experience"]').select("5-10 years");

      // Update remote preference
      cy.get('select[name="remotePreference"]').select("Remote");

      // Update social links
      cy.get('input[name="linkedinUrl"]')
        .clear()
        .type("https://linkedin.com/in/janesmith");
      cy.get('input[name="githubUrl"]')
        .clear()
        .type("https://github.com/janesmith");

      // Add skills
      cy.get('[data-testid="add-skill-button"]').click();
      cy.get('input[name="skill-name"]').type("React");
      cy.get('select[name="skill-level"]').select("Expert");
      cy.get('input[name="skill-years"]').type("5");
      cy.get('[data-testid="save-skill-button"]').click();

      // Submit form
      cy.get('button[type="submit"]').contains("Save Changes").click();

      // Verify success toast
      cy.contains("Candidate has been updated successfully!").should(
        "be.visible",
      );

      // Verify dialog closed
      cy.get('[role="dialog"]').should("not.exist");

      // Verify updated data is displayed
      cy.contains("Jane Smith").should("be.visible");
      cy.contains("<EMAIL>").should("be.visible");
      cy.contains("Senior Software Engineer").should("be.visible");
    });
  });

  describe("Validation Errors", () => {
    it("should show validation error for empty required fields", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Clear required fields
      cy.get('input[name="name"]').clear();
      cy.get('input[name="email"]').clear();
      cy.get('input[name="role"]').clear();

      // Try to submit
      cy.get('button[type="submit"]').click();

      // Check for validation errors
      cy.contains("Name is required").should("be.visible");
      cy.contains("Email is required").should("be.visible");
      cy.contains("Role is required").should("be.visible");
    });

    it("should show validation error for invalid email format", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      cy.get('input[name="email"]').clear().type("invalid-email");
      cy.get('button[type="submit"]').click();

      cy.contains("Please enter a valid email address").should("be.visible");
    });

    it("should show validation error for invalid phone format", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      cy.get('input[name="phone"]').clear().type("123");
      cy.get('button[type="submit"]').click();

      cy.contains("Please enter a valid phone number").should("be.visible");
    });

    it("should show validation error for invalid LinkedIn URL", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      cy.get('input[name="linkedinUrl"]').clear().type("not-a-url");
      cy.get('button[type="submit"]').click();

      cy.contains("Please enter a valid LinkedIn URL").should("be.visible");
    });
  });

  describe("Accessibility Tests", () => {
    it("should have proper focus management", () => {
      // Open dialog
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Check initial focus is on first input
      cy.focused().should("have.attr", "name", "name");

      // Tab through form fields
      cy.focused().tab().should("have.attr", "name", "email");
      cy.focused().tab().should("have.attr", "name", "role");

      // Escape key should close dialog
      cy.get("body").type("{esc}");
      cy.get('[role="dialog"]').should("not.exist");

      // Focus should return to edit button
      cy.focused().should("have.attr", "data-testid", "edit-candidate-button");
    });

    it("should have proper ARIA labels and roles", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Check dialog has proper ARIA attributes
      cy.get('[role="dialog"]').should("have.attr", "aria-modal", "true");
      cy.get('[role="dialog"]').should("have.attr", "aria-labelledby");

      // Check form inputs have labels
      cy.get('label[for="name"]').should("contain", "Name");
      cy.get('input[name="name"]').should("have.attr", "aria-required", "true");

      cy.get('label[for="email"]').should("contain", "Email");
      cy.get('input[name="email"]').should(
        "have.attr",
        "aria-required",
        "true",
      );

      cy.get('label[for="role"]').should("contain", "Role");
      cy.get('input[name="role"]').should("have.attr", "aria-required", "true");
    });

    it("should trap focus within dialog", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Get all focusable elements in dialog
      cy.get('[role="dialog"]').within(() => {
        // Tab to last focusable element (Cancel button)
        cy.get("button").contains("Cancel").focus();

        // Tab should wrap to first element
        cy.focused().tab();
        cy.focused().should("have.attr", "name", "name");

        // Shift+Tab from first element should wrap to last
        cy.focused().tab({ shift: true });
        cy.focused().should("contain", "Cancel");
      });
    });
  });

  describe("Edge Cases", () => {
    it("should handle network errors gracefully", () => {
      // Intercept the update request and make it fail
      cy.intercept("PUT", "/api/candidates/*", {
        statusCode: 500,
        body: { error: "Internal Server Error" },
      }).as("updateCandidate");

      cy.get('[data-testid="edit-candidate-button"]').click();
      cy.get('input[name="name"]').clear().type("Test User");
      cy.get('button[type="submit"]').click();

      cy.wait("@updateCandidate");

      // Should show error toast
      cy.contains("Failed to update candidate").should("be.visible");

      // Dialog should remain open
      cy.get('[role="dialog"]').should("be.visible");
    });

    it("should handle concurrent edits warning", () => {
      // Simulate another user editing the same candidate
      cy.intercept("PUT", "/api/candidates/*", {
        statusCode: 409,
        body: { error: "Candidate has been modified by another user" },
      }).as("updateConflict");

      cy.get('[data-testid="edit-candidate-button"]').click();
      cy.get('input[name="name"]').clear().type("Test User");
      cy.get('button[type="submit"]').click();

      cy.wait("@updateConflict");

      // Should show conflict error
      cy.contains("Candidate has been modified by another user").should(
        "be.visible",
      );
    });

    it("should preserve form data on validation error", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Enter valid data in some fields
      cy.get('input[name="name"]').clear().type("Jane Doe");
      cy.get('input[name="role"]').clear().type("Manager");

      // Enter invalid email
      cy.get('input[name="email"]').clear().type("invalid-email");

      // Submit
      cy.get('button[type="submit"]').click();

      // Check validation error appears
      cy.contains("Please enter a valid email address").should("be.visible");

      // Check other fields retained their values
      cy.get('input[name="name"]').should("have.value", "Jane Doe");
      cy.get('input[name="role"]').should("have.value", "Manager");
    });
  });

  describe("Cancel and Close Behavior", () => {
    it("should close dialog without saving on cancel", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Make changes
      cy.get('input[name="name"]').clear().type("Changed Name");

      // Click cancel
      cy.get("button").contains("Cancel").click();

      // Dialog should close
      cy.get('[role="dialog"]').should("not.exist");

      // Original data should remain
      cy.contains("Changed Name").should("not.exist");
    });

    it("should show unsaved changes warning when closing with changes", () => {
      cy.get('[data-testid="edit-candidate-button"]').click();

      // Make changes
      cy.get('input[name="name"]').clear().type("Changed Name");

      // Click X button or backdrop
      cy.get('[data-testid="dialog-close-button"]').click();

      // Should show confirmation dialog
      cy.contains("Unsaved Changes").should("be.visible");
      cy.contains("Are you sure you want to discard your changes?").should(
        "be.visible",
      );

      // Confirm discard
      cy.get("button").contains("Discard").click();

      // Dialog should close
      cy.get('[role="dialog"]').should("not.exist");
    });
  });
});
