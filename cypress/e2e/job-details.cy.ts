describe("Job Details Navigation", () => {
  beforeEach(() => {
    // Visit the dashboard
    cy.visit("/dashboard");

    // Wait for the dashboard to load
    cy.contains("Dashboard").should("be.visible");
  });

  it("should navigate to job details from Recent Jobs widget", () => {
    // Wait for the Recent Jobs widget to load
    cy.contains("Active Jobs").should("be.visible");

    // Check if there are any active jobs
    cy.get('[data-testid="recent-jobs"]').within(() => {
      // Look for job items - they should be clickable divs
      cy.get(".cursor-pointer")
        .first()
        .then(($job) => {
          // Get the job title before clicking
          const jobTitle = $job.find("h4").text();

          // Click on the first job
          cy.wrap($job).click();
        });
    });

    // Verify navigation to job details page
    cy.url().should("match", /\/jobs\/[a-zA-Z0-9-]+$/);

    // Verify job details are displayed
    cy.get("h2").should("be.visible"); // Job title
    cy.contains(/Remote|On-site|Hybrid/).should("be.visible"); // Location
    cy.contains(/Active|Closed|Draft/).should("be.visible"); // Status
  });

  it("should show 404 state for non-existent job", () => {
    // Navigate directly to a non-existent job
    cy.visit("/jobs/non-existent-job-id");

    // Should show job not found message
    cy.contains("Job not found").should("be.visible");
  });

  it("should show loading state while fetching job details", () => {
    // Intercept the job details request
    cy.intercept("GET", "**/jobs*", (req) => {
      req.reply((res) => {
        // Delay the response to see loading state
        res.delay(1000);
      });
    }).as("getJobDetails");

    // Visit a job details page directly
    cy.visit("/dashboard");

    // Click on a job if available
    cy.get(".cursor-pointer").first().click();

    // Should show loading skeleton
    cy.get('[class*="skeleton"]').should("be.visible");

    // Wait for the request to complete
    cy.wait("@getJobDetails");

    // Loading skeleton should be gone
    cy.get('[class*="skeleton"]').should("not.exist");
  });

  it("should handle error state gracefully", () => {
    // Intercept and force an error
    cy.intercept("GET", "**/jobs*", {
      statusCode: 500,
      body: { error: "Internal Server Error" },
    }).as("getJobDetailsError");

    // Visit a job details page
    cy.visit("/jobs/some-job-id");

    // Wait for the error response
    cy.wait("@getJobDetailsError");

    // Should show error message
    cy.contains("Error loading job details").should("be.visible");
  });

  it("should preserve dashboard functionality after viewing job details", () => {
    // Navigate to a job details page
    cy.get(".cursor-pointer").first().click();

    // Verify we're on the job details page
    cy.url().should("include", "/jobs/");

    // Navigate back to dashboard
    cy.contains("Dashboard").click();

    // Verify dashboard components are still functional
    cy.contains("Active Jobs").should("be.visible");
    cy.contains("Recent Candidates").should("be.visible");

    // Verify we can still interact with dashboard components
    cy.get('[data-testid="quick-actions"]').should("be.visible");
  });
});
