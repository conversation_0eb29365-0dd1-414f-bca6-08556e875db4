describe("Workflow Management E2E Tests", () => {
  beforeEach(() => {
    // Setup interceptors
    cy.interceptSupabase();

    // Login and navigate to workflows
    cy.login();
    cy.visit("/ai-workflows");

    // Wait for initial load
    cy.wait("@getWorkflows");
  });

  afterEach(() => {
    cy.logout();
  });

  describe("Workflow Creation", () => {
    it("should create a new workflow", () => {
      // Click create workflow button
      cy.get('[data-testid="create-workflow-btn"]').click();

      // Fill in workflow details
      cy.get("#workflow-name").type("Test Candidate Screening");
      cy.get("#workflow-description").type(
        "Automated workflow for screening candidates",
      );

      // Save workflow
      cy.get('[data-testid="save-workflow-btn"]').click();

      // Wait for creation
      cy.wait("@createWorkflow");

      // Verify redirect to canvas
      cy.url().should("include", "/ai-workflows/new-workflow-id");
      cy.get(".workflow-canvas").should("be.visible");
    });

    it("should add nodes to workflow canvas", () => {
      // Navigate to a workflow
      cy.get('[data-testid="workflow-card-workflow-1"]').click();

      // Add trigger node
      cy.dragNode("new-application", 200, 200);
      cy.get('[data-node-type="new-application"]').should("exist");

      // Add condition node
      cy.dragNode("skills-match", 400, 200);
      cy.get('[data-node-type="skills-match"]').should("exist");

      // Add action nodes
      cy.dragNode("send-email", 600, 100);
      cy.dragNode("send-email", 600, 300);
      cy.get('[data-node-type="send-email"]').should("have.length", 2);
    });

    it("should connect nodes with edges", () => {
      // Navigate to workflow
      cy.get('[data-testid="workflow-card-workflow-1"]').click();

      // Add nodes
      cy.dragNode("new-application", 200, 200);
      cy.dragNode("skills-match", 400, 200);

      // Wait for nodes to render
      cy.wait(500);

      // Connect nodes
      cy.get('[data-node-type="new-application"]').first().as("trigger");
      cy.get('[data-node-type="skills-match"]').first().as("condition");

      // Connect trigger to condition
      cy.get("@trigger").find(".react-flow__handle-source").click();
      cy.get("@condition").find(".react-flow__handle-target").click();

      // Verify edge was created
      cy.get(".react-flow__edge").should("exist");
    });

    it("should configure node properties", () => {
      // Navigate to workflow
      cy.get('[data-testid="workflow-card-workflow-1"]').click();

      // Add and configure skills match node
      cy.dragNode("skills-match", 400, 200);

      // Double-click to configure
      cy.get('[data-node-type="skills-match"]').first().dblclick();

      // Fill configuration
      cy.get('[name="requiredSkills"]').type("JavaScript, React, Node.js");
      cy.get('[name="minMatchPercentage"]').clear().type("80");

      // Save configuration
      cy.get('[data-testid="save-node-config"]').click();

      // Verify configuration was saved
      cy.get('[data-node-type="skills-match"]').should("contain", "80%");
    });
  });

  describe("Workflow Editing", () => {
    beforeEach(() => {
      // Create a workflow with some nodes
      cy.intercept("GET", "**/rest/v1/workflow_configurations/workflow-1", {
        statusCode: 200,
        body: {
          id: "workflow-1",
          name: "Existing Workflow",
          configuration: {
            nodes: [
              {
                id: "node-1",
                type: "new-application",
                position: { x: 200, y: 200 },
                data: {
                  type: "new-application",
                  label: "New Application Trigger",
                },
              },
              {
                id: "node-2",
                type: "send-email",
                position: { x: 400, y: 200 },
                data: {
                  type: "send-email",
                  label: "Send Welcome Email",
                  config: { template: "welcome" },
                },
              },
            ],
            edges: [
              {
                id: "edge-1",
                source: "node-1",
                target: "node-2",
              },
            ],
          },
        },
      }).as("getWorkflow");
    });

    it("should load and display existing workflow", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();
      cy.wait("@getWorkflow");

      // Verify nodes are loaded
      cy.get('[data-node-id="node-1"]').should("exist");
      cy.get('[data-node-id="node-2"]').should("exist");
      cy.get(".react-flow__edge").should("exist");
    });

    it("should delete nodes from workflow", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();
      cy.wait("@getWorkflow");

      // Select and delete node
      cy.get('[data-node-id="node-2"]').click();
      cy.get("body").type("{del}");

      // Verify node was deleted
      cy.get('[data-node-id="node-2"]').should("not.exist");
      cy.get(".react-flow__edge").should("not.exist");
    });

    it("should update workflow name and description", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();
      cy.wait("@getWorkflow");

      // Open workflow settings
      cy.get('[data-testid="workflow-settings-btn"]').click();

      // Update name and description
      cy.get("#workflow-name").clear().type("Updated Workflow Name");
      cy.get("#workflow-description").clear().type("Updated description");

      // Save changes
      cy.get('[data-testid="save-workflow-settings"]').click();

      // Verify changes were saved
      cy.get('[data-testid="workflow-title"]').should(
        "contain",
        "Updated Workflow Name",
      );
    });

    it("should validate workflow before saving", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();

      // Add a condition node without connecting it properly
      cy.dragNode("skills-match", 600, 200);

      // Try to save workflow
      cy.get('[data-testid="save-workflow-btn"]').click();

      // Should show validation error
      cy.get('[data-testid="validation-error"]').should(
        "contain",
        "All condition nodes must have both true and false paths",
      );
    });
  });

  describe("Workflow Execution", () => {
    beforeEach(() => {
      // Setup a complete workflow
      cy.intercept("GET", "**/rest/v1/workflow_configurations/workflow-1", {
        statusCode: 200,
        body: {
          id: "workflow-1",
          name: "Complete Screening Workflow",
          configuration: {
            nodes: [
              {
                id: "trigger-1",
                type: "new-application",
                position: { x: 100, y: 200 },
                data: { type: "new-application", label: "New Application" },
              },
              {
                id: "condition-1",
                type: "skills-match",
                position: { x: 300, y: 200 },
                data: {
                  type: "skills-match",
                  label: "Check Skills",
                  config: {
                    requiredSkills: "JavaScript, React",
                    minMatchPercentage: 70,
                  },
                },
              },
              {
                id: "action-accept",
                type: "send-email",
                position: { x: 500, y: 100 },
                data: {
                  type: "send-email",
                  label: "Send Welcome",
                  config: { template: "welcome" },
                },
              },
              {
                id: "action-reject",
                type: "send-email",
                position: { x: 500, y: 300 },
                data: {
                  type: "send-email",
                  label: "Send Rejection",
                  config: { template: "rejection" },
                },
              },
            ],
            edges: [
              { id: "e1", source: "trigger-1", target: "condition-1" },
              {
                id: "e2",
                source: "condition-1",
                target: "action-accept",
                data: { condition: "true" },
              },
              {
                id: "e3",
                source: "condition-1",
                target: "action-reject",
                data: { condition: "false" },
              },
            ],
          },
        },
      }).as("getCompleteWorkflow");

      // Mock execution results
      cy.intercept("POST", "**/functions/v1/workflow-executor", {
        statusCode: 200,
        body: {
          success: true,
          executionId: "exec-123",
          executionPath: ["trigger-1", "condition-1", "action-accept"],
          logs: [
            {
              nodeId: "trigger-1",
              status: "success",
              message: "Workflow triggered",
              timestamp: new Date().toISOString(),
            },
            {
              nodeId: "condition-1",
              status: "success",
              message: "Skills match: 85%",
              timestamp: new Date().toISOString(),
              data: { matchPercentage: 85, result: true },
            },
            {
              nodeId: "action-accept",
              status: "success",
              message: "Email sent successfully",
              timestamp: new Date().toISOString(),
            },
          ],
        },
      }).as("executeWorkflow");
    });

    it("should execute workflow and show progress", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();
      cy.wait("@getCompleteWorkflow");

      // Click run workflow button
      cy.get('[data-testid="run-workflow-btn"]').click();

      // Select test data or candidate
      cy.get('[data-testid="select-candidate"]').click();
      cy.get('[data-testid="candidate-option-1"]').click();

      // Start execution
      cy.get('[data-testid="start-execution-btn"]').click();

      // Wait for execution
      cy.wait("@executeWorkflow");

      // Verify execution progress
      cy.get('[data-testid="execution-progress"]').should("be.visible");
      cy.get('[data-node-id="trigger-1"]').should("have.class", "node-success");
      cy.get('[data-node-id="condition-1"]').should(
        "have.class",
        "node-success",
      );
      cy.get('[data-node-id="action-accept"]').should(
        "have.class",
        "node-success",
      );
      cy.get('[data-node-id="action-reject"]').should(
        "not.have.class",
        "node-success",
      );
    });

    it("should display execution logs", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();
      cy.wait("@getCompleteWorkflow");

      // Execute workflow
      cy.get('[data-testid="run-workflow-btn"]').click();
      cy.get('[data-testid="select-candidate"]').click();
      cy.get('[data-testid="candidate-option-1"]').click();
      cy.get('[data-testid="start-execution-btn"]').click();
      cy.wait("@executeWorkflow");

      // Open execution logs
      cy.get('[data-testid="view-logs-btn"]').click();

      // Verify logs are displayed
      cy.get('[data-testid="execution-log"]').should("have.length", 3);
      cy.get('[data-testid="execution-log"]')
        .first()
        .should("contain", "Workflow triggered");
      cy.get('[data-testid="execution-log"]')
        .eq(1)
        .should("contain", "Skills match: 85%");
      cy.get('[data-testid="execution-log"]')
        .last()
        .should("contain", "Email sent successfully");
    });

    it("should handle execution errors gracefully", () => {
      // Mock failed execution
      cy.intercept("POST", "**/functions/v1/workflow-executor", {
        statusCode: 200,
        body: {
          success: false,
          executionId: "exec-failed-123",
          error: "Email service unavailable",
          executionPath: ["trigger-1", "condition-1", "action-accept"],
          logs: [
            {
              nodeId: "trigger-1",
              status: "success",
              message: "Workflow triggered",
              timestamp: new Date().toISOString(),
            },
            {
              nodeId: "condition-1",
              status: "success",
              message: "Skills match: 85%",
              timestamp: new Date().toISOString(),
            },
            {
              nodeId: "action-accept",
              status: "error",
              message: "Failed to send email: Service unavailable",
              timestamp: new Date().toISOString(),
            },
          ],
        },
      }).as("executeWorkflowError");

      cy.get('[data-testid="workflow-card-workflow-1"]').click();
      cy.wait("@getCompleteWorkflow");

      // Execute workflow
      cy.get('[data-testid="run-workflow-btn"]').click();
      cy.get('[data-testid="select-candidate"]').click();
      cy.get('[data-testid="candidate-option-1"]').click();
      cy.get('[data-testid="start-execution-btn"]').click();
      cy.wait("@executeWorkflowError");

      // Verify error is displayed
      cy.get('[data-testid="execution-error"]').should("be.visible");
      cy.get('[data-testid="execution-error"]').should(
        "contain",
        "Email service unavailable",
      );

      // Verify node shows error state
      cy.get('[data-node-id="action-accept"]').should(
        "have.class",
        "node-error",
      );
    });
  });

  describe("Workflow Templates", () => {
    it("should create workflow from template", () => {
      // Click create from template
      cy.get('[data-testid="create-from-template-btn"]').click();

      // Select a template
      cy.get('[data-testid="template-screening"]').click();

      // Customize template
      cy.get("#workflow-name").clear().type("My Screening Workflow");
      cy.get('[data-testid="use-template-btn"]').click();

      // Wait for creation
      cy.wait("@createWorkflow");

      // Verify workflow was created with template nodes
      cy.get('[data-node-type="new-application"]').should("exist");
      cy.get('[data-node-type="ai-screen"]').should("exist");
      cy.get('[data-node-type="skills-match"]').should("exist");
      cy.get('[data-node-type="send-email"]').should("have.length.at.least", 2);
    });
  });

  describe("Workflow History", () => {
    beforeEach(() => {
      // Mock execution history
      cy.intercept("GET", "**/rest/v1/workflow_executions*", {
        statusCode: 200,
        body: [
          {
            id: "exec-1",
            workflow_id: "workflow-1",
            status: "completed",
            created_at: new Date(Date.now() - 3600000).toISOString(),
            completed_at: new Date(Date.now() - 3500000).toISOString(),
            logs: [],
          },
          {
            id: "exec-2",
            workflow_id: "workflow-1",
            status: "failed",
            created_at: new Date(Date.now() - 7200000).toISOString(),
            completed_at: new Date(Date.now() - 7100000).toISOString(),
            error: "Connection timeout",
            logs: [],
          },
        ],
      }).as("getExecutions");
    });

    it("should display workflow execution history", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();

      // Open history tab
      cy.get('[data-testid="workflow-history-tab"]').click();
      cy.wait("@getExecutions");

      // Verify executions are displayed
      cy.get('[data-testid="execution-row"]').should("have.length", 2);
      cy.get('[data-testid="execution-row"]')
        .first()
        .should("contain", "completed");
      cy.get('[data-testid="execution-row"]')
        .last()
        .should("contain", "failed");
    });

    it("should view details of past execution", () => {
      cy.get('[data-testid="workflow-card-workflow-1"]').click();
      cy.get('[data-testid="workflow-history-tab"]').click();
      cy.wait("@getExecutions");

      // Click on execution to view details
      cy.get('[data-testid="execution-row"]').first().click();

      // Verify execution details are shown
      cy.get('[data-testid="execution-detail-modal"]').should("be.visible");
      cy.get('[data-testid="execution-duration"]').should("contain", "100s");
      cy.get('[data-testid="execution-status"]').should("contain", "completed");
    });
  });
});
