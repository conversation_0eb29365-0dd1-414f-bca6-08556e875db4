/// <reference types="cypress" />

describe("Edit Candidate Flow", () => {
  beforeEach(() => {
    cy.visit("/");
    // Assuming there's a way to navigate to a specific candidate, e.g., click a candidate link or enter the candidate page directly
    // Also assumed login is handled globally for all tests or a login command is available
    // E.g., cy.login('user', 'password');
  });

  it("should successfully edit a candidate", () => {
    // Click the edit button to open EditCandidateDialog
    cy.get('[data-testid="edit-candidate-button"]').click();

    // Fill out the form fields
    cy.get('input[name="name"]').clear().type("John <PERSON> Updated");
    cy.get('input[name="email"]').clear().type("<EMAIL>");
    cy.get('input[name="role"]').clear().type("Lead Engineer");

    // Submit the form
    cy.get('button[type="submit"]').click();

    // Check for success toast
    cy.contains("Candidate has been updated successfully!").should(
      "be.visible",
    );

    // Validate if the updated candidate's information appears correctly on the candidate details page
    cy.get('[data-testid="candidate-name"]').should(
      "contain",
      "<PERSON>e Updated",
    );
    cy.get('[data-testid="candidate-email"]').should(
      "contain",
      "<EMAIL>",
    );
    cy.get('[data-testid="candidate-role"]').should("contain", "Lead Engineer");
  });

  it("should show validation errors when form inputs are invalid", () => {
    // Click the edit button to open EditCandidateDialog
    cy.get('[data-testid="edit-candidate-button"]').click();

    // Fill out invalid input
    cy.get('input[name="email"]').clear().type("invalid-email");

    // Submit the form
    cy.get('button[type="submit"]').click();

    // Check for validation error
    cy.contains("Enter a valid email").should("be.visible");
  });
});
