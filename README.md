# RMS-Refresh: Modern Recruitment Management System

A comprehensive recruitment management platform with advanced workflow automation, AI-powered candidate screening, and real-time collaboration features.

## 🏗 Architecture Overview

### System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                         Frontend (React)                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌───────────────────────┐  │
│  │   UI Layer  │  │  Workflow     │  │   State Management    │  │
│  │  Components │  │  Canvas       │  │   (Context + Hooks)   │  │
│  │  (shadcn)   │  │  (React Flow) │  │                       │  │
│  └─────────────┘  └──────────────┘  └───────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Backend (Supabase)                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌───────────────────────┐  │
│  │  Database   │  │    Edge      │  │     Real-time         │  │
│  │ (PostgreSQL)│  │  Functions   │  │    Subscriptions      │  │
│  │             │  │              │  │                       │  │
│  └─────────────┘  └──────────────┘  └───────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### Key Components

#### New Drawer and Tabs Component Usage

- **Drawer Component**: Found in `Calendar.tsx`, designed to be toggled by a button or escape key on mobile.
- **Tabs Component**: Implemented in `tabs.tsx`, utilized in pages like `AIWorkflows.tsx` and `Candidates.tsx` for navigation between different workflow and candidate management sections. Supports state management through URL parameters.

#### 1. **Frontend Architecture**

- **React + TypeScript**: Type-safe component development
- **Vite**: Fast build tooling and hot module replacement
- **React Flow**: Visual workflow builder with custom nodes
- **shadcn/ui**: Modern, accessible UI components
- **Tailwind CSS**: Utility-first styling

#### 2. **Backend Services**

- **Supabase Database**: PostgreSQL with row-level security
- **Edge Functions**: Serverless workflow execution
- **Real-time Subscriptions**: Live updates across the platform
- **Storage**: Secure document and file management

#### 3. **Workflow Engine**

- **Visual Builder**: Drag-and-drop workflow creation
- **Node Types**: Triggers, Actions, Conditions, Transformations
- **Execution Engine**: Serverless workflow processing
- **Scheduler**: Cron-based workflow scheduling
- **Webhooks**: External integration endpoints

### Data Flow

```
User Action → Frontend → API Call → Edge Function → Database
     ↑                                    │
     └────── Real-time Update ←───────────┘
```

## 🚀 Developer Setup

### Prerequisites

- Node.js 18+ and npm
- Supabase CLI
- Git

### Local Development Setup

1. **Clone the repository**

   ```bash
   git clone https://github.com/your-org/rms-refresh.git
   cd rms-refresh
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:

   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Start Supabase locally (optional)**

   ```bash
   supabase start
   ```

5. **Run database migrations**

   ```bash
   supabase db push
   ```

6. **Deploy Edge Functions**

   ```bash
   supabase functions deploy
   ```

7. **Start the development server**
   ```bash
   npm run dev
   ```

### Project Structure

```
rms-refresh/
├── src/
│   ├── components/       # React components
│   │   ├── ai/          # AI & workflow components
│   │   ├── candidate/   # Candidate management
│   │   ├── job/         # Job posting components
│   │   └── ui/          # Shared UI components
│   ├── engine/          # Workflow execution engine
│   ├── hooks/           # Custom React hooks
│   ├── pages/           # Route components
│   ├── services/        # Business logic services
│   └── utils/           # Helper functions
├── supabase/
│   ├── functions/       # Edge Functions
│   └── migrations/      # Database migrations
├── docs/                # Documentation
└── public/             # Static assets
```

## 🔧 Development Workflow

### Code Style

- ESLint and Prettier are configured for code consistency
- Run `npm run lint` to check for issues
- Run `npm run format` to auto-format code

### Testing

```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run all tests with coverage
npm run test:coverage
```

### Building for Production

```bash
# Build the application
npm run build

# Preview production build
npm run preview
```

### Database Management

```bash
# Create a new migration
supabase migration new <migration_name>

# Apply migrations
supabase db push

# Reset database
supabase db reset
```

## 📚 Core Features

### 1. Workflow Automation

- Visual workflow builder with drag-and-drop interface
- Pre-built workflow templates
- Custom node types for various automation tasks
- Real-time execution monitoring

### 2. Candidate Management

- Comprehensive candidate profiles
- Document management and parsing
- Interview scheduling and tracking
- AI-powered candidate screening

### 3. Job Management

- Multi-channel job posting
- Applicant tracking system (ATS)
- Pipeline visualization
- Analytics and reporting

### 4. Communication Hub

- Email templates and automation
- In-app messaging
- Calendar integration
- Team collaboration tools

### 5. Analytics Dashboard

- Recruitment metrics and KPIs
- Source effectiveness tracking
- Diversity and inclusion metrics
- Custom report generation

## 🎨 UI Components

### Drawer Component

The drawer component provides a collapsible sidebar interface, primarily used in the Calendar view for task management.

**Features:**

- **Responsive Design**: Full-screen overlay on mobile, sidebar on desktop
- **State Persistence**: User preference saved to localStorage
- **Keyboard Support**: ESC key closes drawer on mobile devices
- **Smooth Animations**: CSS transitions for open/close actions
- **Accessibility**: ARIA labels and proper focus management

**Usage Example (Calendar.tsx):**

```tsx
// Toggle button on desktop
<button onClick={toggleDrawer}>
  {isDrawerOpen ? 'Hide Tasks' : 'Show Tasks'}
</button>

// Drawer container with responsive classes
<aside className={`
  fixed lg:relative top-0 right-0 h-full lg:h-auto
  transform transition-transform duration-300
  ${isDrawerOpen ? 'translate-x-0' : 'translate-x-full'}
`}>
  <TaskCalendarWidget />
</aside>
```

### Tabs Component

The tabs component enables organized content navigation with URL-based state management.

**Components:**

- `Tabs`: Root container
- `TabsList`: Navigation container
- `TabsTrigger`: Individual tab buttons
- `TabsContent`: Content panels

**Features:**

- **URL State Sync**: Tab state persisted in URL parameters
- **Responsive Grid**: Adapts from 2 to 4 columns based on screen size
- **Accessible**: Full keyboard navigation and ARIA support
- **Flexible Layout**: Supports both horizontal and grid layouts

**Usage Example (AIWorkflows.tsx):**

```tsx
// URL-synced tabs with grid layout
<Tabs value={activeTab} onValueChange={handleTabChange}>
  <TabsList className="grid grid-cols-2 sm:grid-cols-4">
    <TabsTrigger value="editor">Workflow Editor</TabsTrigger>
    <TabsTrigger value="manage">Manage</TabsTrigger>
    <TabsTrigger value="analytics">Analytics</TabsTrigger>
    <TabsTrigger value="tools">AI Tools</TabsTrigger>
  </TabsList>
  <TabsContent value="editor">
    <AIWorkflowCanvas />
  </TabsContent>
  // ... more tab contents
</Tabs>
```

**URL Parameter Support:**

```typescript
// Get tab from URL
const queryParams = new URLSearchParams(location.search);
const tabParam = queryParams.get("tab");

// Update URL when tab changes
useEffect(() => {
  const params = new URLSearchParams();
  params.set("tab", activeTab);
  navigate(`/path?${params.toString()}`, { replace: true });
}, [activeTab]);
```

## 🔍 Search and Vectorization Strategy

### Full-text Search Implementation

The RMS platform uses PostgreSQL's built-in full-text search capabilities for fast and accurate search functionality across candidates, jobs, and messages.

#### Architecture

1. **Search Vectors**: Each searchable table (`candidates`, `jobs`, `messages`) has a `search_vector` column of type `tsvector`
2. **Automatic Updates**: Database triggers automatically update search vectors when records are inserted or modified
3. **Weighted Search**: Different fields have different weights for relevance scoring:
   - Weight A (highest): Name, role, title
   - Weight B: Email, location, department
   - Weight C: Experience, industry, job type
   - Weight D (lowest): AI summaries, requirements, benefits

#### Search Features

- **Multi-field Search**: Searches across all relevant fields simultaneously
- **Fuzzy Matching**: Handles partial matches and variations
- **Performance Optimized**: GIN indexes on search vectors for sub-millisecond queries
- **Relevance Scoring**: Results are sorted by relevance to the search query

#### Usage Examples

```typescript
// Search candidates by name, role, or skills
const results = await searchCandidates("React Developer", {
  remoteOnly: true,
  skills: [{ name: "React", required: true }]
});

// Search jobs with filters
const jobs = await searchJobs("Frontend", {
  remoteOnly: true,
  location: { address: "San Francisco", radius: 50 }
});
```

#### Performance Metrics

- Average search time: < 100ms for datasets up to 100k records
- Index size: ~30% of table size
- Real-time updates: Search vectors updated within database transaction

### Testing Search Functionality

```bash
# Run integration tests
npm run test src/__tests__/integration/search-integration.test.ts

# Run manual validation script
TEST_EMAIL=<EMAIL> TEST_PASSWORD=your-password npx ts-node scripts/validate-search.ts
```

## 🔒 Security

- Row-level security (RLS) on all database tables
- JWT-based authentication
- Secure file storage with access controls
- CORS configuration for API endpoints
- Environment variable protection

## 🚀 Deployment

### Supabase Platform

1. Create a new Supabase project
2. Run migrations in the Supabase dashboard
3. Deploy Edge Functions
4. Update environment variables
5. Deploy frontend to your preferred hosting service

### Vercel Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

## 📖 Additional Resources

- [API Documentation](./docs/api-reference.md)
- [Workflow Node Reference](./docs/workflows/node-spec.md)
- [Creating Your First Workflow](./docs/tutorials/first-workflow.md)
- [Contributing Guide](./CONTRIBUTING.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.
