# Missing and Sub-optimal Indexes Analysis

## Overview
This analysis compares the existing database indexes with the query usage patterns to identify missing or sub-optimal indexes.

## Key Findings

### 1. Missing Text Search Indexes (GIN)

The following columns are used for text search (.ilike) but lack GIN indexes:

- **candidates.role** - Has btree index but missing GIN for text search
- **candidates.experience** - No index at all
- **candidates.industry** - No index at all
- **events.title** - No text search index
- **messages.sender_name** - No index at all
- **messages.content** - Has search_vector but no direct GIN index on content
- **tasks.assignee** - No index at all
- **workflow_configurations.name** - No text search index

### 2. Missing Composite Indexes for Common Query Patterns

The following composite indexes are missing but would optimize frequent query patterns:

- **messages.user_id + is_starred** - For filtering starred messages by user
- **tasks.user_id + created_at DESC** - Common ordering pattern
- **tasks.user_id + due_date** - For overdue task queries
- **tasks.user_id + category** - For category filtering
- **candidate_skills.candidate_id + created_at** - For ordering skills
- **skills.category + name** - For category-based ordering
- **events.user_id + category** - For category filtering

### 3. Missing Array Indexes (GIN)

The following array columns lack GIN indexes:

- **candidates.tags** - Used with overlaps operator
- **jobs.requirements** - Array field without index
- **jobs.benefits** - Array field without index
- **candidate_interviews.interviewers** - Array field without index

### 4. Missing Indexes for Foreign Key Relationships

The following columns are frequently used in joins but lack indexes:

- **candidate_interviews.candidate_id** - No index found
- **candidate_interviews.user_id** - No index found
- **candidate_interviews.scheduled_date** - No index found
- **candidate_notes.candidate_id** - No index found
- **candidate_notes.user_id** - No index found
- **candidate_timeline.candidate_id** - No index found
- **candidate_timeline.user_id** - No index found

### 5. Missing Partial Indexes for Common Filters

The following partial indexes would optimize common filter conditions:

- **messages WHERE status = 'unread'** - Frequently filtered condition
- **workflow_schedules WHERE is_active = true** - Active schedules only
- **tasks WHERE status != 'completed' AND due_date IS NOT NULL** - Overdue tasks

### 6. JSONB Indexes

The following JSONB columns might benefit from GIN indexes:

- **candidates.skills** - JSONB field without GIN index
- **workflow_configurations.config** - JSONB field without GIN index
- **workflow_executions.execution_data** - JSONB field without GIN index
- **notifications.metadata** - JSONB field without GIN index

## Replica Identity Status

Good news: All major tables already have REPLICA IDENTITY FULL set, which is required for realtime functionality.

## Recommended Index Creation Statements

```sql
-- Text Search Indexes
CREATE INDEX IF NOT EXISTS idx_candidates_role_gin ON candidates USING gin(to_tsvector('english', role));
CREATE INDEX IF NOT EXISTS idx_candidates_experience_gin ON candidates USING gin(to_tsvector('english', experience));
CREATE INDEX IF NOT EXISTS idx_candidates_industry_gin ON candidates USING gin(to_tsvector('english', industry));
CREATE INDEX IF NOT EXISTS idx_events_title_gin ON events USING gin(to_tsvector('english', title));
CREATE INDEX IF NOT EXISTS idx_messages_sender_name_gin ON messages USING gin(to_tsvector('english', sender_name));
CREATE INDEX IF NOT EXISTS idx_messages_content_gin ON messages USING gin(to_tsvector('english', content));
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_gin ON tasks USING gin(to_tsvector('english', assignee));
CREATE INDEX IF NOT EXISTS idx_workflow_configurations_name_gin ON workflow_configurations USING gin(to_tsvector('english', name));

-- Composite Indexes for Common Query Patterns
CREATE INDEX IF NOT EXISTS idx_messages_user_starred ON messages(user_id, is_starred) WHERE is_starred = true;
CREATE INDEX IF NOT EXISTS idx_tasks_user_created ON tasks(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_tasks_user_due ON tasks(user_id, due_date) WHERE status != 'completed' AND due_date IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_user_category ON tasks(user_id, category);
CREATE INDEX IF NOT EXISTS idx_candidate_skills_candidate_created ON candidate_skills(candidate_id, created_at);
CREATE INDEX IF NOT EXISTS idx_skills_category_name ON skills(category, name);
CREATE INDEX IF NOT EXISTS idx_events_user_category ON events(user_id, category);
CREATE INDEX IF NOT EXISTS idx_events_user_start_future ON events(user_id, start_time) WHERE start_time > NOW();

-- Array Indexes
CREATE INDEX IF NOT EXISTS idx_candidates_tags_gin ON candidates USING gin(tags);
CREATE INDEX IF NOT EXISTS idx_jobs_requirements_gin ON jobs USING gin(requirements);
CREATE INDEX IF NOT EXISTS idx_jobs_benefits_gin ON jobs USING gin(benefits);
CREATE INDEX IF NOT EXISTS idx_candidate_interviews_interviewers_gin ON candidate_interviews USING gin(interviewers);

-- Foreign Key Relationship Indexes
CREATE INDEX IF NOT EXISTS idx_candidate_interviews_candidate_id ON candidate_interviews(candidate_id);
CREATE INDEX IF NOT EXISTS idx_candidate_interviews_user_id ON candidate_interviews(user_id);
CREATE INDEX IF NOT EXISTS idx_candidate_interviews_scheduled_date ON candidate_interviews(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_candidate_notes_candidate_id ON candidate_notes(candidate_id);
CREATE INDEX IF NOT EXISTS idx_candidate_notes_user_id ON candidate_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_candidate_timeline_candidate_id ON candidate_timeline(candidate_id);
CREATE INDEX IF NOT EXISTS idx_candidate_timeline_user_id ON candidate_timeline(user_id);
CREATE INDEX IF NOT EXISTS idx_candidate_timeline_candidate_created ON candidate_timeline(candidate_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_candidate_notes_candidate_created ON candidate_notes(candidate_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_candidate_interviews_candidate_scheduled ON candidate_interviews(candidate_id, scheduled_date);

-- Partial Indexes for Common Filters
CREATE INDEX IF NOT EXISTS idx_messages_user_status_unread ON messages(user_id, status) WHERE status = 'unread';
CREATE INDEX IF NOT EXISTS idx_workflow_schedules_active_next ON workflow_schedules(is_active, next_run) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_workflow_exec_status_active ON workflow_executions(status) WHERE status IN ('in_progress', 'failed');

-- JSONB Indexes
CREATE INDEX IF NOT EXISTS idx_candidates_skills_gin ON candidates USING gin(skills);
CREATE INDEX IF NOT EXISTS idx_workflow_configurations_config_gin ON workflow_configurations USING gin(config);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_data_gin ON workflow_executions USING gin(execution_data);
CREATE INDEX IF NOT EXISTS idx_notifications_metadata_gin ON notifications USING gin(metadata);

-- Additional Performance Indexes
CREATE INDEX IF NOT EXISTS idx_jobs_user_status ON jobs(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_jobs_title_gin ON jobs USING gin(to_tsvector('english', title));
CREATE INDEX IF NOT EXISTS idx_jobs_company_gin ON jobs USING gin(to_tsvector('english', department));
CREATE INDEX IF NOT EXISTS idx_candidates_remote_pref ON candidates(remote_preference) WHERE remote_preference IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_candidates_visa_status ON candidates(visa_status) WHERE visa_status IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_notifications_user_type ON notifications(user_id, type);
CREATE INDEX IF NOT EXISTS idx_workflow_exec_workflow_started ON workflow_executions(workflow_id, started_at DESC);
```

## Priority Recommendations

### High Priority (Immediate Impact)
1. Foreign key indexes for candidate_* tables - these are causing slow joins
2. Text search indexes for frequently searched columns
3. Partial indexes for unread messages and active workflows

### Medium Priority (Performance Optimization)
1. Array column GIN indexes
2. Composite indexes for common query patterns
3. JSONB GIN indexes for complex queries

### Low Priority (Future Optimization)
1. Additional covering indexes
2. More specific partial indexes
3. Expression indexes for computed values

## Notes
- All tables already have REPLICA IDENTITY FULL, so no changes needed for realtime
- Consider using CONCURRENTLY when creating indexes on production to avoid locks
- Monitor index usage after creation to ensure they're being utilized
- Some indexes might be redundant with existing ones - verify before creating
