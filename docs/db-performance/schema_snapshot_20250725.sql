--- Schema ---
[TABLES]
Table: user_connections
Columns:
  - id
  - user_id
  - provider
  - provider_account_id
  - provider_account_email
  - access_token
  - refresh_token
  - token_expires_at
  - scopes
  - settings
  - is_active
  - created_at
  - updated_at

Table: usage_quotas
Columns:
  - id
  - user_id
  - provider
  - quota_type
  - quota_limit
  - quota_used
  - reset_at
  - created_at
  - updated_at

Table: report_templates
Columns:
  - id
  - name
  - description
  - template_config
  - query_template
  - visualization_config
  - created_by
  - created_at
  - updated_at
  - is_active
  - search_vector

Table: scheduled_reports
Columns:
  - id
  - name
  - description
  - template_id
  - schedule_config
  - parameters
  - recipients
  - output_format
  - is_active
  - last_run_at
  - next_run_at
  - created_by
  - created_at
  - updated_at
  - search_vector

Table: generated_reports
Columns:
  - id
  - scheduled_report_id
  - template_id
  - name
  - description
  - parameters_used
  - file_path
  - file_size
  - format
  - status
  - error_message
  - metadata
  - generated_by
  - generated_at
  - expires_at
  - accessed_at
  - access_count
  - search_vector

Table: analytics_skills
Columns:
  - id
  - user_id
  - skill
  - current_level
  - required_level
  - gap
  - recommendation
  - success_impact
  - created_at
  - updated_at
  - skill_id
  - job_id

Table: analytics_salary
Columns:
  - id
  - user_id
  - role
  - market_salary
  - recommended_salary
  - position
  - current_salary
  - market_rate
  - confidence
  - location
  - created_at
  - updated_at
  - experience_level
  - industry
  - job_id

Table: jobs
Columns:
  - id
  - user_id
  - title
  - department
  - location
  - job_type
  - salary_range
  - experience_required
  - description
  - requirements
  - benefits
  - is_urgent
  - is_active
  - applicant_count
  - created_at
  - updated_at
  - search_vector

Table: profiles
Columns:
  - id
  - first_name
  - last_name
  - company
  - role
  - avatar_url
  - created_at
  - updated_at
  - search_vector

Table: events
Columns:
  - id
  - title
  - description
  - start_time
  - end_time
  - location
  - meeting_link
  - event_type
  - priority
  - category
  - user_id
  - created_at
  - updated_at
  - candidate_id
  - job_id
  - search_vector

Table: messages
Columns:
  - id
  - sender_name
  - sender_email
  - sender_role
  - sender_avatar
  - content
  - status
  - is_starred
  - follow_up
  - reminder
  - user_id
  - created_at
  - updated_at
  - candidate_id
  - job_id
  - search_vector

Table: message_templates
Columns:
  - id
  - name
  - subject
  - content
  - template_category
  - user_id
  - created_at
  - updated_at
  - search_vector

Table: workflow_configurations
Columns:
  - id
  - name
  - description
  - config
  - is_active
  - created_by
  - created_at
  - updated_at
  - search_vector

Table: analytics_applications
Columns:
  - id
  - user_id
  - period
  - applications
  - interviews
  - created_at
  - updated_at

Table: analytics_sources
Columns:
  - id
  - user_id
  - source
  - effectiveness
  - hires
  - created_at
  - updated_at

Table: analytics_diversity
Columns:
  - id
  - user_id
  - category
  - value
  - total
  - created_at
  - updated_at

Table: workflow_executions
Columns:
  - id
  - workflow_id
  - status
  - started_at
  - completed_at
  - execution_data
  - logs
  - created_by
  - created_at
  - updated_at

Table: workflow_schedules
Columns:
  - id
  - workflow_id
  - cron_expression
  - is_active
  - next_run
  - last_run
  - created_by
  - created_at
  - updated_at


[INDEXES]
Index: audit_log_entries_pkey
Location: auth.audit_log_entries
Definition: CREATE UNIQUE INDEX audit_log_entries_pkey ON auth.audit_log_entries USING btree (id)

Index: audit_logs_instance_id_idx
Location: auth.audit_log_entries
Definition: CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id)

Index: flow_state_created_at_idx
Location: auth.flow_state
Definition: CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC)

Index: flow_state_pkey
Location: auth.flow_state
Definition: CREATE UNIQUE INDEX flow_state_pkey ON auth.flow_state USING btree (id)

Index: idx_auth_code
Location: auth.flow_state
Definition: CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code)

Index: idx_user_id_auth_method
Location: auth.flow_state
Definition: CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method)

Index: identities_email_idx
Location: auth.identities
Definition: CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops)

Index: identities_pkey
Location: auth.identities
Definition: CREATE UNIQUE INDEX identities_pkey ON auth.identities USING btree (id)

Index: identities_provider_id_provider_unique
Location: auth.identities
Definition: CREATE UNIQUE INDEX identities_provider_id_provider_unique ON auth.identities USING btree (provider_id, provider)

Index: identities_user_id_idx
Location: auth.identities
Definition: CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id)

Index: instances_pkey
Location: auth.instances
Definition: CREATE UNIQUE INDEX instances_pkey ON auth.instances USING btree (id)

Index: amr_id_pk
Location: auth.mfa_amr_claims
Definition: CREATE UNIQUE INDEX amr_id_pk ON auth.mfa_amr_claims USING btree (id)

Index: mfa_amr_claims_session_id_authentication_method_pkey
Location: auth.mfa_amr_claims
Definition: CREATE UNIQUE INDEX mfa_amr_claims_session_id_authentication_method_pkey ON auth.mfa_amr_claims USING btree (session_id, authentication_method)

Index: mfa_challenge_created_at_idx
Location: auth.mfa_challenges
Definition: CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC)

Index: mfa_challenges_pkey
Location: auth.mfa_challenges
Definition: CREATE UNIQUE INDEX mfa_challenges_pkey ON auth.mfa_challenges USING btree (id)

Index: factor_id_created_at_idx
Location: auth.mfa_factors
Definition: CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at)

Index: mfa_factors_last_challenged_at_key
Location: auth.mfa_factors
Definition: CREATE UNIQUE INDEX mfa_factors_last_challenged_at_key ON auth.mfa_factors USING btree (last_challenged_at)

Index: mfa_factors_pkey
Location: auth.mfa_factors
Definition: CREATE UNIQUE INDEX mfa_factors_pkey ON auth.mfa_factors USING btree (id)

Index: mfa_factors_user_friendly_name_unique
Location: auth.mfa_factors
Definition: CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text)

Index: mfa_factors_user_id_idx
Location: auth.mfa_factors
Definition: CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id)

Index: unique_phone_factor_per_user
Location: auth.mfa_factors
Definition: CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone)

Index: one_time_tokens_pkey
Location: auth.one_time_tokens
Definition: CREATE UNIQUE INDEX one_time_tokens_pkey ON auth.one_time_tokens USING btree (id)

Index: one_time_tokens_relates_to_hash_idx
Location: auth.one_time_tokens
Definition: CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to)

Index: one_time_tokens_token_hash_hash_idx
Location: auth.one_time_tokens
Definition: CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash)

Index: one_time_tokens_user_id_token_type_key
Location: auth.one_time_tokens
Definition: CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type)

Index: refresh_tokens_instance_id_idx
Location: auth.refresh_tokens
Definition: CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id)

Index: refresh_tokens_instance_id_user_id_idx
Location: auth.refresh_tokens
Definition: CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id)

Index: refresh_tokens_parent_idx
Location: auth.refresh_tokens
Definition: CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent)

Index: refresh_tokens_pkey
Location: auth.refresh_tokens
Definition: CREATE UNIQUE INDEX refresh_tokens_pkey ON auth.refresh_tokens USING btree (id)

Index: refresh_tokens_session_id_revoked_idx
Location: auth.refresh_tokens
Definition: CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked)

Index: refresh_tokens_token_unique
Location: auth.refresh_tokens
Definition: CREATE UNIQUE INDEX refresh_tokens_token_unique ON auth.refresh_tokens USING btree (token)

Index: refresh_tokens_updated_at_idx
Location: auth.refresh_tokens
Definition: CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC)

Index: saml_providers_entity_id_key
Location: auth.saml_providers
Definition: CREATE UNIQUE INDEX saml_providers_entity_id_key ON auth.saml_providers USING btree (entity_id)

Index: saml_providers_pkey
Location: auth.saml_providers
Definition: CREATE UNIQUE INDEX saml_providers_pkey ON auth.saml_providers USING btree (id)

Index: saml_providers_sso_provider_id_idx
Location: auth.saml_providers
Definition: CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id)

Index: saml_relay_states_created_at_idx
Location: auth.saml_relay_states
Definition: CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC)

Index: saml_relay_states_for_email_idx
Location: auth.saml_relay_states
Definition: CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email)

Index: saml_relay_states_pkey
Location: auth.saml_relay_states
Definition: CREATE UNIQUE INDEX saml_relay_states_pkey ON auth.saml_relay_states USING btree (id)

Index: saml_relay_states_sso_provider_id_idx
Location: auth.saml_relay_states
Definition: CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id)

Index: schema_migrations_pkey
Location: auth.schema_migrations
Definition: CREATE UNIQUE INDEX schema_migrations_pkey ON auth.schema_migrations USING btree (version)

Index: sessions_not_after_idx
Location: auth.sessions
Definition: CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC)

Index: sessions_pkey
Location: auth.sessions
Definition: CREATE UNIQUE INDEX sessions_pkey ON auth.sessions USING btree (id)

Index: sessions_user_id_idx
Location: auth.sessions
Definition: CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id)

Index: user_id_created_at_idx
Location: auth.sessions
Definition: CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at)

Index: sso_domains_domain_idx
Location: auth.sso_domains
Definition: CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain))

Index: sso_domains_pkey
Location: auth.sso_domains
Definition: CREATE UNIQUE INDEX sso_domains_pkey ON auth.sso_domains USING btree (id)

Index: sso_domains_sso_provider_id_idx
Location: auth.sso_domains
Definition: CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id)

Index: sso_providers_pkey
Location: auth.sso_providers
Definition: CREATE UNIQUE INDEX sso_providers_pkey ON auth.sso_providers USING btree (id)

Index: sso_providers_resource_id_idx
Location: auth.sso_providers
Definition: CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id))

Index: confirmation_token_idx
Location: auth.users
Definition: CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text)

Index: email_change_token_current_idx
Location: auth.users
Definition: CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text)

Index: email_change_token_new_idx
Location: auth.users
Definition: CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text)

Index: reauthentication_token_idx
Location: auth.users
Definition: CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text)

Index: recovery_token_idx
Location: auth.users
Definition: CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text)

Index: users_email_partial_key
Location: auth.users
Definition: CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false)

Index: users_instance_id_email_idx
Location: auth.users
Definition: CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text))

Index: users_instance_id_idx
Location: auth.users
Definition: CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id)

Index: users_is_anonymous_idx
Location: auth.users
Definition: CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous)

Index: users_phone_key
Location: auth.users
Definition: CREATE UNIQUE INDEX users_phone_key ON auth.users USING btree (phone)

Index: users_pkey
Location: auth.users
Definition: CREATE UNIQUE INDEX users_pkey ON auth.users USING btree (id)

Index: analytics_applications_pkey
Location: public.analytics_applications
Definition: CREATE UNIQUE INDEX analytics_applications_pkey ON public.analytics_applications USING btree (id)

Index: analytics_applications_user_id_period_key
Location: public.analytics_applications
Definition: CREATE UNIQUE INDEX analytics_applications_user_id_period_key ON public.analytics_applications USING btree (user_id, period)

Index: idx_analytics_applications_user_id
Location: public.analytics_applications
Definition: CREATE INDEX idx_analytics_applications_user_id ON public.analytics_applications USING btree (user_id)

Index: analytics_diversity_pkey
Location: public.analytics_diversity
Definition: CREATE UNIQUE INDEX analytics_diversity_pkey ON public.analytics_diversity USING btree (id)

Index: idx_analytics_diversity_user_id
Location: public.analytics_diversity
Definition: CREATE INDEX idx_analytics_diversity_user_id ON public.analytics_diversity USING btree (user_id)

Index: analytics_metrics_pkey
Location: public.analytics_metrics
Definition: CREATE UNIQUE INDEX analytics_metrics_pkey ON public.analytics_metrics USING btree (id)

Index: analytics_metrics_user_id_metric_name_period_key
Location: public.analytics_metrics
Definition: CREATE UNIQUE INDEX analytics_metrics_user_id_metric_name_period_key ON public.analytics_metrics USING btree (user_id, metric_name, period)

Index: idx_analytics_metrics_user_metric
Location: public.analytics_metrics
Definition: CREATE INDEX idx_analytics_metrics_user_metric ON public.analytics_metrics USING btree (user_id, metric_name, period)

Index: idx_analytics_metrics_user_realtime
Location: public.analytics_metrics
Definition: CREATE INDEX idx_analytics_metrics_user_realtime ON public.analytics_metrics USING btree (user_id, metric_name, period, created_at DESC)

Index: analytics_salary_pkey
Location: public.analytics_salary
Definition: CREATE UNIQUE INDEX analytics_salary_pkey ON public.analytics_salary USING btree (id)

Index: idx_analytics_salary_job_id
Location: public.analytics_salary
Definition: CREATE INDEX idx_analytics_salary_job_id ON public.analytics_salary USING btree (job_id)

Index: idx_analytics_salary_user_id
Location: public.analytics_salary
Definition: CREATE INDEX idx_analytics_salary_user_id ON public.analytics_salary USING btree (user_id)

Index: analytics_skills_pkey
Location: public.analytics_skills
Definition: CREATE UNIQUE INDEX analytics_skills_pkey ON public.analytics_skills USING btree (id)

Index: idx_analytics_skills_job_id
Location: public.analytics_skills
Definition: CREATE INDEX idx_analytics_skills_job_id ON public.analytics_skills USING btree (job_id)

Index: idx_analytics_skills_skill_id
Location: public.analytics_skills
Definition: CREATE INDEX idx_analytics_skills_skill_id ON public.analytics_skills USING btree (skill_id)

Index: idx_analytics_skills_user_id
Location: public.analytics_skills
Definition: CREATE INDEX idx_analytics_skills_user_id ON public.analytics_skills USING btree (user_id)

Index: analytics_sources_pkey
Location: public.analytics_sources
Definition: CREATE UNIQUE INDEX analytics_sources_pkey ON public.analytics_sources USING btree (id)

Index: idx_analytics_sources_user_id
Location: public.analytics_sources
Definition: CREATE INDEX idx_analytics_sources_user_id ON public.analytics_sources USING btree (user_id)

-- Additional public schema indexes for full text search, performance, and constraints

Index: jobs_pkey
Location: public.jobs
Definition: CREATE UNIQUE INDEX jobs_pkey ON public.jobs USING btree (id)

Index: jobs_search_vector_idx
Location: public.jobs
Definition: CREATE INDEX jobs_search_vector_idx ON public.jobs USING gin (search_vector)

Index: idx_jobs_user_id
Location: public.jobs
Definition: CREATE INDEX idx_jobs_user_id ON public.jobs USING btree (user_id)

Index: idx_jobs_active
Location: public.jobs
Definition: CREATE INDEX idx_jobs_active ON public.jobs USING btree (is_active)

Index: idx_jobs_department
Location: public.jobs
Definition: CREATE INDEX idx_jobs_department ON public.jobs USING btree (department) WHERE (department IS NOT NULL)

Index: idx_jobs_search_vector
Location: public.jobs
Definition: CREATE INDEX idx_jobs_search_vector ON public.jobs USING gin (search_vector)

Index: idx_jobs_title_search
Location: public.jobs
Definition: CREATE INDEX idx_jobs_title_search ON public.jobs USING gin (to_tsvector('english'::regconfig, title))

Index: idx_jobs_urgency_tracking
Location: public.jobs
Definition: CREATE INDEX idx_jobs_urgency_tracking ON public.jobs USING btree (user_id, is_urgent, created_at DESC) WHERE (is_urgent = true)

Index: idx_jobs_user_active_realtime
Location: public.jobs
Definition: CREATE INDEX idx_jobs_user_active_realtime ON public.jobs USING btree (user_id, is_active, updated_at DESC) WHERE (is_active = true)

Index: idx_jobs_analytics_performance
Location: public.jobs
Definition: CREATE INDEX idx_jobs_analytics_performance ON public.jobs USING btree (user_id, department, job_type, is_active, applicant_count DESC)

Index: idx_jobs_complex_filter_realtime
Location: public.jobs
Definition: CREATE INDEX idx_jobs_complex_filter_realtime ON public.jobs USING btree (user_id, is_active, department, created_at DESC)

Index: idx_jobs_realtime_feed
Location: public.jobs
Definition: CREATE INDEX idx_jobs_realtime_feed ON public.jobs USING btree (user_id, created_at DESC, updated_at DESC)

Index: profiles_pkey
Location: public.profiles
Definition: CREATE UNIQUE INDEX profiles_pkey ON public.profiles USING btree (id)

Index: profiles_search_vector_idx
Location: public.profiles
Definition: CREATE INDEX profiles_search_vector_idx ON public.profiles USING gin (search_vector)

Index: idx_profiles_updated_realtime
Location: public.profiles
Definition: CREATE INDEX idx_profiles_updated_realtime ON public.profiles USING btree (id, updated_at DESC)

Index: events_pkey
Location: public.events
Definition: CREATE UNIQUE INDEX events_pkey ON public.events USING btree (id)

Index: events_search_vector_idx
Location: public.events
Definition: CREATE INDEX events_search_vector_idx ON public.events USING gin (search_vector)

Index: idx_events_user_id
Location: public.events
Definition: CREATE INDEX idx_events_user_id ON public.events USING btree (user_id)

Index: idx_events_start_time
Location: public.events
Definition: CREATE INDEX idx_events_start_time ON public.events USING btree (start_time)

Index: idx_events_end_time
Location: public.events
Definition: CREATE INDEX idx_events_end_time ON public.events USING btree (end_time)

Index: idx_events_event_type
Location: public.events
Definition: CREATE INDEX idx_events_event_type ON public.events USING btree (event_type)

Index: idx_events_type
Location: public.events
Definition: CREATE INDEX idx_events_type ON public.events USING btree (event_type)

Index: idx_events_category
Location: public.events
Definition: CREATE INDEX idx_events_category ON public.events USING btree (category)

Index: idx_events_user_start
Location: public.events
Definition: CREATE INDEX idx_events_user_start ON public.events USING btree (user_id, start_time DESC)

Index: idx_events_candidate_id
Location: public.events
Definition: CREATE INDEX idx_events_candidate_id ON public.events USING btree (candidate_id)

Index: idx_events_job_id
Location: public.events
Definition: CREATE INDEX idx_events_job_id ON public.events USING btree (job_id)

Index: idx_events_complex_scheduling
Location: public.events
Definition: CREATE INDEX idx_events_complex_scheduling ON public.events USING btree (user_id, event_type, start_time, end_time, category)

Index: idx_events_realtime_calendar
Location: public.events
Definition: CREATE INDEX idx_events_realtime_calendar ON public.events USING btree (user_id, start_time DESC, created_at DESC)

Index: idx_events_user_time_realtime
Location: public.events
Definition: CREATE INDEX idx_events_user_time_realtime ON public.events USING btree (user_id, start_time, end_time)

Index: messages_pkey
Location: public.messages
Definition: CREATE UNIQUE INDEX messages_pkey ON public.messages USING btree (id)

Index: messages_search_vector_idx
Location: public.messages
Definition: CREATE INDEX messages_search_vector_idx ON public.messages USING gin (search_vector)

Index: idx_messages_user_id
Location: public.messages
Definition: CREATE INDEX idx_messages_user_id ON public.messages USING btree (user_id)

Index: idx_messages_status
Location: public.messages
Definition: CREATE INDEX idx_messages_status ON public.messages USING btree (status)

Index: idx_messages_candidate_id
Location: public.messages
Definition: CREATE INDEX idx_messages_candidate_id ON public.messages USING btree (candidate_id)

Index: idx_messages_job_id
Location: public.messages
Definition: CREATE INDEX idx_messages_job_id ON public.messages USING btree (job_id)

Index: idx_messages_user_created
Location: public.messages
Definition: CREATE INDEX idx_messages_user_created ON public.messages USING btree (user_id, created_at DESC)

Index: idx_messages_analytics
Location: public.messages
Definition: CREATE INDEX idx_messages_analytics ON public.messages USING btree (user_id, status, created_at DESC)

Index: idx_messages_user_status_realtime
Location: public.messages
Definition: CREATE INDEX idx_messages_user_status_realtime ON public.messages USING btree (user_id, status, created_at DESC)

Index: message_templates_pkey
Location: public.message_templates
Definition: CREATE UNIQUE INDEX message_templates_pkey ON public.message_templates USING btree (id)

Index: message_templates_search_vector_idx
Location: public.message_templates
Definition: CREATE INDEX message_templates_search_vector_idx ON public.message_templates USING gin (search_vector)

Index: idx_message_templates_user_id
Location: public.message_templates
Definition: CREATE INDEX idx_message_templates_user_id ON public.message_templates USING btree (user_id)

Index: idx_message_templates_category
Location: public.message_templates
Definition: CREATE INDEX idx_message_templates_category ON public.message_templates USING btree (template_category)

Index: idx_message_templates_usage
Location: public.message_templates
Definition: CREATE INDEX idx_message_templates_usage ON public.message_templates USING btree (user_id, template_category, updated_at DESC)

Index: workflow_configurations_pkey
Location: public.workflow_configurations
Definition: CREATE UNIQUE INDEX workflow_configurations_pkey ON public.workflow_configurations USING btree (id)

Index: workflow_configurations_search_vector_idx
Location: public.workflow_configurations
Definition: CREATE INDEX workflow_configurations_search_vector_idx ON public.workflow_configurations USING gin (search_vector)

Index: idx_workflow_configurations_created_by
Location: public.workflow_configurations
Definition: CREATE INDEX idx_workflow_configurations_created_by ON public.workflow_configurations USING btree (created_by)

Index: idx_workflow_configurations_is_active
Location: public.workflow_configurations
Definition: CREATE INDEX idx_workflow_configurations_is_active ON public.workflow_configurations USING btree (is_active)

Index: idx_workflow_configurations_user_realtime
Location: public.workflow_configurations
Definition: CREATE INDEX idx_workflow_configurations_user_realtime ON public.workflow_configurations USING btree (created_by, is_active, updated_at DESC)

Index: workflow_executions_pkey
Location: public.workflow_executions
Definition: CREATE UNIQUE INDEX workflow_executions_pkey ON public.workflow_executions USING btree (id)

Index: idx_workflow_executions_workflow_id
Location: public.workflow_executions
Definition: CREATE INDEX idx_workflow_executions_workflow_id ON public.workflow_executions USING btree (workflow_id)

Index: idx_workflow_executions_status
Location: public.workflow_executions
Definition: CREATE INDEX idx_workflow_executions_status ON public.workflow_executions USING btree (status)

Index: idx_workflow_executions_created_by
Location: public.workflow_executions
Definition: CREATE INDEX idx_workflow_executions_created_by ON public.workflow_executions USING btree (created_by)

Index: idx_workflow_executions_user_realtime
Location: public.workflow_executions
Definition: CREATE INDEX idx_workflow_executions_user_realtime ON public.workflow_executions USING btree (created_by, status, created_at DESC)

Index: idx_workflow_execution_analytics
Location: public.workflow_executions
Definition: CREATE INDEX idx_workflow_execution_analytics ON public.workflow_executions USING btree (workflow_id, status, started_at DESC, completed_at DESC)

Index: idx_workflow_execution_monitoring_realtime
Location: public.workflow_executions
Definition: CREATE INDEX idx_workflow_execution_monitoring_realtime ON public.workflow_executions USING btree (created_by, workflow_id, status, started_at DESC)

Index: workflow_schedules_pkey
Location: public.workflow_schedules
Definition: CREATE UNIQUE INDEX workflow_schedules_pkey ON public.workflow_schedules USING btree (id)

Index: idx_workflow_schedules_workflow_id
Location: public.workflow_schedules
Definition: CREATE INDEX idx_workflow_schedules_workflow_id ON public.workflow_schedules USING btree (workflow_id)

Index: idx_workflow_schedules_created_by
Location: public.workflow_schedules
Definition: CREATE INDEX idx_workflow_schedules_created_by ON public.workflow_schedules USING btree (created_by)

Index: idx_workflow_schedules_user_active_realtime
Location: public.workflow_schedules
Definition: CREATE INDEX idx_workflow_schedules_user_active_realtime ON public.workflow_schedules USING btree (created_by, is_active, next_run)

Index: user_connections_pkey
Location: public.user_connections
Definition: CREATE UNIQUE INDEX user_connections_pkey ON public.user_connections USING btree (id)

Index: user_connections_user_id_provider_provider_account_id_key
Location: public.user_connections
Definition: CREATE UNIQUE INDEX user_connections_user_id_provider_provider_account_id_key ON public.user_connections USING btree (user_id, provider, provider_account_id)

Index: idx_user_connections_user_id
Location: public.user_connections
Definition: CREATE INDEX idx_user_connections_user_id ON public.user_connections USING btree (user_id)

Index: idx_user_connections_provider
Location: public.user_connections
Definition: CREATE INDEX idx_user_connections_provider ON public.user_connections USING btree (provider)

Index: idx_user_connections_active
Location: public.user_connections
Definition: CREATE INDEX idx_user_connections_active ON public.user_connections USING btree (is_active)

Index: usage_quotas_pkey
Location: public.usage_quotas
Definition: CREATE UNIQUE INDEX usage_quotas_pkey ON public.usage_quotas USING btree (id)

Index: usage_quotas_user_id_provider_quota_type_key
Location: public.usage_quotas
Definition: CREATE UNIQUE INDEX usage_quotas_user_id_provider_quota_type_key ON public.usage_quotas USING btree (user_id, provider, quota_type)

Index: idx_usage_quotas_user_provider
Location: public.usage_quotas
Definition: CREATE INDEX idx_usage_quotas_user_provider ON public.usage_quotas USING btree (user_id, provider)

Index: idx_usage_quotas_reset_at
Location: public.usage_quotas
Definition: CREATE INDEX idx_usage_quotas_reset_at ON public.usage_quotas USING btree (reset_at)

Index: report_templates_pkey
Location: public.report_templates
Definition: CREATE UNIQUE INDEX report_templates_pkey ON public.report_templates USING btree (id)

Index: report_templates_search_vector_idx
Location: public.report_templates
Definition: CREATE INDEX report_templates_search_vector_idx ON public.report_templates USING gin (search_vector)

Index: idx_report_templates_created_by
Location: public.report_templates
Definition: CREATE INDEX idx_report_templates_created_by ON public.report_templates USING btree (created_by)

Index: idx_report_templates_is_active
Location: public.report_templates
Definition: CREATE INDEX idx_report_templates_is_active ON public.report_templates USING btree (is_active)

Index: scheduled_reports_pkey
Location: public.scheduled_reports
Definition: CREATE UNIQUE INDEX scheduled_reports_pkey ON public.scheduled_reports USING btree (id)

Index: scheduled_reports_search_vector_idx
Location: public.scheduled_reports
Definition: CREATE INDEX scheduled_reports_search_vector_idx ON public.scheduled_reports USING gin (search_vector)

Index: idx_scheduled_reports_created_by
Location: public.scheduled_reports
Definition: CREATE INDEX idx_scheduled_reports_created_by ON public.scheduled_reports USING btree (created_by)

Index: idx_scheduled_reports_template_id
Location: public.scheduled_reports
Definition: CREATE INDEX idx_scheduled_reports_template_id ON public.scheduled_reports USING btree (template_id)

Index: idx_scheduled_reports_is_active
Location: public.scheduled_reports
Definition: CREATE INDEX idx_scheduled_reports_is_active ON public.scheduled_reports USING btree (is_active)

Index: idx_scheduled_reports_next_run_at
Location: public.scheduled_reports
Definition: CREATE INDEX idx_scheduled_reports_next_run_at ON public.scheduled_reports USING btree (next_run_at) WHERE (is_active = true)

Index: idx_scheduled_reports_active_realtime
Location: public.scheduled_reports
Definition: CREATE INDEX idx_scheduled_reports_active_realtime ON public.scheduled_reports USING btree (created_by, is_active, next_run_at) WHERE (is_active = true)

Index: generated_reports_pkey
Location: public.generated_reports
Definition: CREATE UNIQUE INDEX generated_reports_pkey ON public.generated_reports USING btree (id)

Index: generated_reports_search_vector_idx
Location: public.generated_reports
Definition: CREATE INDEX generated_reports_search_vector_idx ON public.generated_reports USING gin (search_vector)

Index: idx_generated_reports_scheduled_report_id
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_scheduled_report_id ON public.generated_reports USING btree (scheduled_report_id)

Index: idx_generated_reports_template_id
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_template_id ON public.generated_reports USING btree (template_id)

Index: idx_generated_reports_generated_by
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_generated_by ON public.generated_reports USING btree (generated_by)

Index: idx_generated_reports_status
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_status ON public.generated_reports USING btree (status)

Index: idx_generated_reports_generated_at
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_generated_at ON public.generated_reports USING btree (generated_at)

Index: idx_generated_reports_expires_at
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_expires_at ON public.generated_reports USING btree (expires_at) WHERE (expires_at IS NOT NULL)

Index: idx_generated_reports_complex
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_complex ON public.generated_reports USING btree (template_id, status, generated_at DESC, expires_at)

Index: idx_generated_reports_user_status_realtime
Location: public.generated_reports
Definition: CREATE INDEX idx_generated_reports_user_status_realtime ON public.generated_reports USING btree (generated_by, status, generated_at DESC)


[SUMMARY]
-- Schema Snapshot for Project: ueanagtsdavrgolecirg
-- Generated: 2025-07-25
-- Total Tables in public schema: 18
-- Total Indexes: 197 (auth schema: 60, public schema: 137)
-- Index Types:
--   - Primary Keys (UNIQUE btree)
--   - Foreign Key Support Indexes (btree)
--   - Full Text Search Indexes (gin on tsvector)
--   - Performance Indexes (btree with DESC ordering)
--   - Partial Indexes (WHERE conditions)
--   - Composite Indexes (multiple columns)
--   - Hash Indexes (for token lookups)
-- Note: This snapshot includes only table structure and indexes.
-- It does not include table data, functions, triggers, or row-level security policies.

