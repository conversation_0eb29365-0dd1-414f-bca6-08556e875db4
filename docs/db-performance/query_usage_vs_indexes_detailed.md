# Query Usage vs Indexes Matrix

This document analyzes query patterns found in the Greplink codebase and maps them to required database indexes.

## Table: `candidates`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Used for single candidate lookup |
| user_id | ✓ | - | - | - | All queries filter by user_id |
| name | - | - | ✓ | - | Search candidates by name |
| email | - | - | ✓ | - | Search candidates by email |
| role | - | - | ✓ | - | Search candidates by role |
| created_at | - | ✓ (order by desc) | - | - | Default ordering for lists |
| tags | - | - | - | ✓ (overlaps) | Array overlap queries |
| experience | - | - | ✓ | - | Filter by experience text |
| industry | - | - | ✓ | - | Filter by industry text |
| remote_preference | ✓ | - | - | - | Exact match filtering |
| visa_status | ✓ | - | - | - | Exact match filtering |
| relationship_score | - | ✓ (gte) | - | - | Filter by minimum score |

**Recommended Indexes:**
```sql
-- Composite index for user filtering and sorting
CREATE INDEX idx_candidates_user_created ON candidates(user_id, created_at DESC);

-- Text search indexes
CREATE INDEX idx_candidates_name_gin ON candidates USING gin(to_tsvector('english', name));
CREATE INDEX idx_candidates_email_gin ON candidates USING gin(to_tsvector('english', email));
CREATE INDEX idx_candidates_role_gin ON candidates USING gin(to_tsvector('english', role));

-- Array index for tags
CREATE INDEX idx_candidates_tags_gin ON candidates USING gin(tags);

-- Filter indexes
CREATE INDEX idx_candidates_remote_pref ON candidates(remote_preference) WHERE remote_preference IS NOT NULL;
CREATE INDEX idx_candidates_visa_status ON candidates(visa_status) WHERE visa_status IS NOT NULL;
```

## Table: `jobs`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Single job lookup |
| user_id | ✓ | - | - | - | All queries filter by user_id |
| created_at | - | ✓ (order by desc) | - | - | Default ordering |
| status | ✓ | - | - | - | Filter by job status |
| title | - | - | ✓ | - | Search jobs by title |
| company | - | - | ✓ | - | Search by company name |

**Recommended Indexes:**
```sql
-- Composite index for user filtering and sorting
CREATE INDEX idx_jobs_user_created ON jobs(user_id, created_at DESC);

-- Status filtering
CREATE INDEX idx_jobs_user_status ON jobs(user_id, status);

-- Text search
CREATE INDEX idx_jobs_title_gin ON jobs USING gin(to_tsvector('english', title));
```

## Table: `events`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Single event lookup |
| user_id | ✓ | - | - | - | All queries filter by user_id |
| start_time | - | ✓ (gte, order by asc) | - | - | Date range queries, ordering |
| end_time | - | ✓ (lte) | - | - | Date range queries |
| event_type | ✓ | - | - | - | Filter by event type |
| category | ✓ | - | - | - | Filter by category |

**Recommended Indexes:**
```sql
-- Composite index for user and time-based queries
CREATE INDEX idx_events_user_start ON events(user_id, start_time);

-- Index for upcoming events
CREATE INDEX idx_events_user_start_future ON events(user_id, start_time) 
WHERE start_time > NOW();

-- Type and category filtering
CREATE INDEX idx_events_user_type ON events(user_id, event_type);
CREATE INDEX idx_events_user_category ON events(user_id, category);
```

## Table: `messages`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Single message lookup |
| user_id | ✓ | - | - | - | All queries filter by user_id |
| created_at | - | ✓ (order by desc) | - | - | Default ordering |
| status | ✓ | - | - | - | Filter unread/read/archived |
| is_starred | ✓ | - | - | - | Filter starred messages |
| sender_name | - | - | ✓ | - | Search by sender |
| content | - | - | ✓ | - | Search message content |

**Recommended Indexes:**
```sql
-- Composite index for user and ordering
CREATE INDEX idx_messages_user_created ON messages(user_id, created_at DESC);

-- Status filtering (unread messages are common)
CREATE INDEX idx_messages_user_status ON messages(user_id, status) 
WHERE status = 'unread';

-- Full text search
CREATE INDEX idx_messages_content_gin ON messages USING gin(to_tsvector('english', content));
```

## Table: `tasks`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | ✓ | Bulk updates |
| user_id | ✓ | - | - | - | All queries filter by user_id |
| created_at | - | ✓ (order by desc) | - | - | Default ordering |
| status | ✓ | - | - | - | Filter by status |
| priority | ✓ | - | - | - | Filter by priority |
| category | ✓ | - | - | - | Filter by category |
| due_date | - | ✓ (gte, lte, lt) | - | - | Date range, overdue tasks |
| assignee | - | - | ✓ | - | Search by assignee |

**Recommended Indexes:**
```sql
-- Composite index for user and ordering
CREATE INDEX idx_tasks_user_created ON tasks(user_id, created_at DESC);

-- Status and priority filtering
CREATE INDEX idx_tasks_user_status ON tasks(user_id, status);
CREATE INDEX idx_tasks_user_priority ON tasks(user_id, priority);

-- Due date queries (overdue tasks)
CREATE INDEX idx_tasks_user_due ON tasks(user_id, due_date) 
WHERE status != 'completed' AND due_date IS NOT NULL;
```

## Table: `notifications`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Single notification |
| user_id | ✓ | - | - | - | All queries filter by user_id |
| created_at | - | ✓ (order by desc) | - | - | Default ordering |
| read | ✓ | - | - | - | Filter unread notifications |
| type | ✓ | - | - | - | Filter by notification type |

**Recommended Indexes:**
```sql
-- Composite index for unread notifications
CREATE INDEX idx_notifications_user_read_created ON notifications(user_id, read, created_at DESC);

-- Type filtering
CREATE INDEX idx_notifications_user_type ON notifications(user_id, type);
```

## Table: `workflow_configurations`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Single workflow lookup |
| created_by | ✓ | - | - | - | User's workflows |
| is_active | ✓ | - | - | - | Filter active workflows |
| created_at | - | ✓ (order by desc) | - | - | Default ordering |
| name | - | - | ✓ | - | Search by name |

**Recommended Indexes:**
```sql
-- User's active workflows
CREATE INDEX idx_workflow_configs_user_active ON workflow_configurations(created_by, is_active);

-- Ordering
CREATE INDEX idx_workflow_configs_created ON workflow_configurations(created_at DESC);
```

## Table: `workflow_executions`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Single execution lookup |
| workflow_id | ✓ | - | - | - | Executions for workflow |
| status | ✓ | - | - | - | Filter by status |
| started_at | - | ✓ (order by desc) | - | - | Ordering by start time |

**Recommended Indexes:**
```sql
-- Workflow executions lookup
CREATE INDEX idx_workflow_exec_workflow_started ON workflow_executions(workflow_id, started_at DESC);

-- Status filtering
CREATE INDEX idx_workflow_exec_status ON workflow_executions(status) WHERE status IN ('in_progress', 'failed');
```

## Table: `workflow_schedules`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Single schedule lookup |
| workflow_id | ✓ | - | - | - | Schedules for workflow |
| is_active | ✓ | - | - | - | Filter active schedules |
| next_run | - | ✓ (lte) | - | - | Due schedules |

**Recommended Indexes:**
```sql
-- Active schedules due for execution
CREATE INDEX idx_workflow_schedules_active_next ON workflow_schedules(is_active, next_run) 
WHERE is_active = true;
```

## Table: `skills`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | Skill lookup |
| name | - | ✓ (order by) | - | - | Alphabetical ordering |
| category | - | ✓ (order by) | - | - | Group by category |

**Recommended Indexes:**
```sql
-- Category and name ordering
CREATE INDEX idx_skills_category_name ON skills(category, name);
```

## Table: `candidate_skills`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| id | ✓ (primary key) | - | - | - | |
| candidate_id | ✓ | - | - | - | Skills for candidate |
| skill_id | ✓ | - | - | - | Join with skills table |
| created_at | - | ✓ (order by) | - | - | Ordering |

**Recommended Indexes:**
```sql
-- Candidate skills lookup
CREATE INDEX idx_candidate_skills_candidate ON candidate_skills(candidate_id, created_at);
```

## Table: `candidate_tags`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| candidate_id | ✓ | - | - | - | Tags for candidate |
| tag_id | ✓ | - | - | - | Join with tags table |

**Recommended Indexes:**
```sql
-- Candidate tags lookup
CREATE INDEX idx_candidate_tags_candidate ON candidate_tags(candidate_id);
```

## Table: `candidate_timeline`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| candidate_id | ✓ | - | - | - | Timeline for candidate |
| created_at | - | ✓ (order by desc) | - | - | Chronological order |

**Recommended Indexes:**
```sql
-- Timeline entries for candidate
CREATE INDEX idx_candidate_timeline_candidate_created ON candidate_timeline(candidate_id, created_at DESC);
```

## Table: `candidate_interviews`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| candidate_id | ✓ | - | - | - | Interviews for candidate |
| scheduled_at | - | ✓ (order by) | - | - | Chronological order |
| status | ✓ | - | - | - | Filter by status |

**Recommended Indexes:**
```sql
-- Candidate interviews lookup
CREATE INDEX idx_candidate_interviews_candidate_scheduled ON candidate_interviews(candidate_id, scheduled_at);
```

## Table: `candidate_documents`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| candidate_id | ✓ | - | - | - | Documents for candidate |
| created_at | - | ✓ (order by desc) | - | - | Recent documents first |

**Recommended Indexes:**
```sql
-- Candidate documents lookup
CREATE INDEX idx_candidate_documents_candidate_created ON candidate_documents(candidate_id, created_at DESC);
```

## Table: `candidate_notes`

| Column | Equality Filters (.eq) | Range/Order By (.gte/.lte/.order) | Text Search (.ilike) | In Filters (.in) | Notes |
|--------|----------------------|-----------------------------------|---------------------|------------------|-------|
| candidate_id | ✓ | - | - | - | Notes for candidate |
| created_at | - | ✓ (order by desc) | - | - | Recent notes first |

**Recommended Indexes:**
```sql
-- Candidate notes lookup
CREATE INDEX idx_candidate_notes_candidate_created ON candidate_notes(candidate_id, created_at DESC);
```

## Summary of Key Patterns

1. **User-based Filtering**: Almost all tables filter by `user_id` as the primary access pattern
2. **Chronological Ordering**: Most tables use `created_at DESC` for default ordering
3. **Status Filtering**: Many tables have status columns that are frequently filtered
4. **Text Search**: Candidates, jobs, and messages use text search on key fields
5. **Relationship Tables**: candidate_* tables always filter by candidate_id
6. **Date Range Queries**: Events and tasks use date range filters extensively

## Performance Recommendations

1. **Composite Indexes**: Use composite indexes for (user_id, created_at DESC) on most tables
2. **Partial Indexes**: Use WHERE clauses on indexes for common filter conditions
3. **GIN Indexes**: Use GIN indexes for text search and array operations
4. **Covering Indexes**: Consider including commonly selected columns in indexes
5. **Index Maintenance**: Regularly analyze and vacuum tables with high write activity
