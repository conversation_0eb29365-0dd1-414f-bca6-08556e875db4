# Stage & Rating Visual Components

This document describes the standardized approach for displaying candidate/application stage and rating information across the application.

## Overview

The application uses consistent visual components for displaying:
- **Stage Badges**: Colored badges indicating the current stage of a candidate in the pipeline
- **Rating Stars**: Star icons showing candidate ratings (1-5 scale)

## Stage Badges

### Usage

Stage badges use the `Badge` component with `variant="secondary"` and stage-specific color classes:

```tsx
import { Badge } from "@/components/ui/badge";
import { getStageColorClasses } from "@/utils/stageUtils";

<Badge variant="secondary" className={getStageColorClasses(stage)}>
  {stage}
</Badge>
```

### Supported Stages

The following stages are supported with their corresponding colors:

| Stage Category | Stages | Color Classes |
|----------------|--------|---------------|
| **Initial Application** | Applied, New | `bg-blue-100 text-blue-700` |
| **Screening** | Phone Screen, Screening | `bg-yellow-100 text-yellow-700` |
| **Interview** | Interview, Technical Interview, Behavioral Interview | `bg-purple-100 text-purple-700` |
| **Final Stages** | Final Round, Final Interview | `bg-orange-100 text-orange-700` |
| **Decision** | Offer, Offer Extended, Offer Accepted | `bg-green-100 text-green-700` |
| **Completion** | Hired, Onboarding | `bg-emerald-100 text-emerald-700` |
| **Rejection** | Rejected, Declined, Withdrawn | `bg-red-100 text-red-700` |
| **On Hold** | On Hold, Paused | `bg-gray-100 text-gray-700` |

Unknown stages default to: `bg-gray-100 text-gray-700`

## Rating Stars

### Usage

Rating stars display using the `Star` icon from lucide-react:

```tsx
import { Star } from "lucide-react";

const renderStars = (rating: number) => {
  return (
    <div className="flex items-center gap-0.5">
      {Array.from({ length: 5 }).map((_, index) => (
        <Star
          key={index}
          className={`w-4 h-4 ${
            index < rating
              ? "fill-yellow-400 text-yellow-400"
              : "text-gray-300"
          }`}
        />
      ))}
    </div>
  );
};
```

### Star Sizes

The shared configuration supports multiple sizes:
- Small: `w-3 h-3`
- Medium: `w-4 h-4` (default)
- Large: `w-5 h-5`

### Alternative Simple Rating

For simpler displays, you can use emoji stars:

```tsx
<div className="text-lg">
  {"⭐".repeat(rating)}
  {"☆".repeat(5 - rating)}
</div>
```

## Implementation Examples

### ApplicantList Component
Shows candidates with stage badges and star ratings in a card layout.

### PipelineKanbanView Component
Displays candidates in a kanban board with inline star ratings.

### JobApplicants Page
Shows applicants in a table format with stage badges and star ratings.

### CandidateDetailsDialog
Displays detailed candidate information with prominent stage badge and rating display.

## Shared Utilities

All stage-related utilities are centralized in `/src/utils/stageUtils.ts`:

```typescript
// Get consistent color classes for stage badges
export const getStageColorClasses = (stage: string): string

// Legacy function for backward compatibility
export const getStageColor = getStageColorClasses // @deprecated

// Star rating configuration
export const STAR_RATING_CONFIG = {
  maxStars: 5,
  filledClass: "fill-yellow-400 text-yellow-400",
  emptyClass: "text-gray-300",
  sizes: {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5",
  }
}
```

## Best Practices

1. Always use `Badge` with `variant="secondary"` for stage display
2. Apply stage colors using the `getStageColor()` utility function
3. Use consistent star sizes within the same view
4. Maintain the 5-star scale across all rating displays
5. Consider context when choosing between icon stars vs emoji stars

## Migration Guide

If you have existing components using custom stage colors:

1. Import the shared utility: `import { getStageColorClasses } from "@/utils/stageUtils"`
2. Remove local `getStageColor` or similar functions
3. Update Badge usage to include `variant="secondary"`
4. Apply the color classes from the utility function

Example migration:
```tsx
// Before
<Badge className="bg-blue-500 text-white">{stage}</Badge>

// After
<Badge variant="secondary" className={getStageColorClasses(stage)}>
  {stage}
</Badge>
```

Note: The `getStageColor` function is still available for backward compatibility but is deprecated. New code should use `getStageColorClasses`.
