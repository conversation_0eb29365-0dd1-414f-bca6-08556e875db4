# Workflow Quick Reference

## Common Node Patterns

### 1. Send Email on New Candidate

```json
{
  "nodes": [
    {
      "id": "trigger-1",
      "type": "trigger",
      "data": {
        "config": {
          "triggerType": "event",
          "event": { "name": "candidate.created" }
        }
      }
    },
    {
      "id": "action-1",
      "type": "action",
      "data": {
        "config": {
          "actionType": "send_email",
          "parameters": {
            "to": "{{candidate.email}}",
            "template": "welcome"
          }
        }
      }
    }
  ],
  "edges": [{ "source": "trigger-1", "target": "action-1" }]
}
```

### 2. Conditional Email Based on Experience

```json
{
  "nodes": [
    {
      "id": "trigger-1",
      "type": "trigger",
      "data": {
        "config": {
          "triggerType": "event",
          "event": { "name": "candidate.created" }
        }
      }
    },
    {
      "id": "condition-1",
      "type": "condition",
      "data": {
        "config": {
          "conditions": [
            {
              "field": "candidate.experience_years",
              "operator": "greater_than",
              "value": 5
            }
          ]
        }
      }
    },
    {
      "id": "senior-email",
      "type": "action",
      "data": {
        "config": {
          "actionType": "send_email",
          "parameters": {
            "to": "{{candidate.email}}",
            "template": "senior_candidate_welcome"
          }
        }
      }
    },
    {
      "id": "junior-email",
      "type": "action",
      "data": {
        "config": {
          "actionType": "send_email",
          "parameters": {
            "to": "{{candidate.email}}",
            "template": "junior_candidate_welcome"
          }
        }
      }
    }
  ],
  "edges": [
    { "source": "trigger-1", "target": "condition-1" },
    {
      "source": "condition-1",
      "target": "senior-email",
      "sourceHandle": "true"
    },
    {
      "source": "condition-1",
      "target": "junior-email",
      "sourceHandle": "false"
    }
  ]
}
```

### 3. Daily Report Generation

```json
{
  "nodes": [
    {
      "id": "trigger-1",
      "type": "trigger",
      "data": {
        "config": {
          "triggerType": "schedule",
          "schedule": {
            "cron": "0 9 * * *",
            "timezone": "America/New_York"
          }
        }
      }
    },
    {
      "id": "transform-1",
      "type": "transformation",
      "data": {
        "config": {
          "transformationType": "custom",
          "custom": {
            "code": "return { date: new Date().toISOString().split('T')[0] };"
          }
        }
      }
    },
    {
      "id": "output-1",
      "type": "output",
      "data": {
        "config": {
          "outputType": "report",
          "report": {
            "template": "daily_recruitment_summary",
            "format": "pdf",
            "recipients": ["<EMAIL>"]
          }
        }
      }
    }
  ],
  "edges": [
    { "source": "trigger-1", "target": "transform-1" },
    { "source": "transform-1", "target": "output-1" }
  ]
}
```

## Variable Reference

### System Variables

- `{{date.now}}` - Current timestamp
- `{{execution.id}}` - Current execution ID
- `{{workflow.id}}` - Workflow ID
- `{{user.email}}` - Executing user's email

### Candidate Variables

- `{{candidate.id}}`
- `{{candidate.first_name}}`
- `{{candidate.last_name}}`
- `{{candidate.email}}`
- `{{candidate.phone}}`
- `{{candidate.status}}`
- `{{candidate.experience_years}}`
- `{{candidate.skills}}` - Array
- `{{candidate.applied_position}}`

### Job Variables

- `{{job.id}}`
- `{{job.title}}`
- `{{job.department}}`
- `{{job.location}}`
- `{{job.salary_range}}`
- `{{job.requirements}}` - Array

### Company Variables

- `{{company.name}}`
- `{{company.website}}`
- `{{company.logo_url}}`

## Condition Operators

| Operator        | Description        | Example                                   |
| --------------- | ------------------ | ----------------------------------------- |
| `equals`        | Exact match        | `status equals "active"`                  |
| `not_equals`    | Not equal          | `department not_equals "Sales"`           |
| `contains`      | Contains substring | `skills contains "Python"`                |
| `not_contains`  | Doesn't contain    | `email not_contains "@competitor.com"`    |
| `starts_with`   | Starts with        | `name starts_with "Dr."`                  |
| `ends_with`     | Ends with          | `email ends_with "@company.com"`          |
| `greater_than`  | Greater than       | `experience_years greater_than 5`         |
| `less_than`     | Less than          | `salary less_than 100000`                 |
| `in`            | In array           | `location in ["NYC", "SF", "LA"]`         |
| `not_in`        | Not in array       | `status not_in ["rejected", "withdrawn"]` |
| `is_empty`      | Is null/empty      | `phone is_empty`                          |
| `is_not_empty`  | Has value          | `linkedin_url is_not_empty`               |
| `matches_regex` | Regex match        | `email matches_regex "^.*@company\.com$"` |

## Action Types Quick Reference

### Communication

- `send_email` - Send email
- `send_sms` - Send SMS
- `send_notification` - In-app notification

### Data Management

- `update_candidate` - Update candidate data
- `update_job` - Update job data
- `add_to_pool` - Add to talent pool
- `add_note` - Add note to record

### Scheduling

- `schedule_interview` - Schedule interview
- `create_event` - Create calendar event
- `send_reminder` - Send reminder

### Integration

- `api_call` - External API call
- `webhook` - Send webhook
- `database_query` - Custom query

## Cron Expression Examples

| Expression     | Description          |
| -------------- | -------------------- |
| `0 9 * * *`    | Daily at 9 AM        |
| `0 9 * * 1`    | Every Monday at 9 AM |
| `0 */2 * * *`  | Every 2 hours        |
| `0 9,17 * * *` | At 9 AM and 5 PM     |
| `0 0 1 * *`    | First day of month   |
| `*/15 * * * *` | Every 15 minutes     |

## Error Handling

### Continue on Error

```json
{
  "actionType": "send_email",
  "parameters": { ... },
  "errorHandling": {
    "continueOnError": true
  }
}
```

### Retry Configuration

```json
{
  "errorHandling": {
    "continueOnError": false,
    "retryCount": 3,
    "retryDelay": 5000
  }
}
```

## Testing Workflows

### Test Context Example

```json
{
  "candidate": {
    "id": "test-123",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "experience_years": 7,
    "skills": ["Python", "JavaScript", "SQL"]
  },
  "job": {
    "id": "job-456",
    "title": "Senior Developer",
    "department": "Engineering"
  }
}
```

## Common Troubleshooting

| Issue                   | Solution                                               |
| ----------------------- | ------------------------------------------------------ |
| Workflow not triggering | Check workflow is active and trigger configuration     |
| Email not sending       | Verify email template exists and variables are correct |
| Condition always false  | Check field path and operator logic                    |
| API call failing        | Verify authentication and endpoint URL                 |
| Variable not replaced   | Ensure variable syntax is correct: `{{path.to.value}}` |
