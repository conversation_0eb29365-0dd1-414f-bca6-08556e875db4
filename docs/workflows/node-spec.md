# Workflow Node Specification

Version: 2.0.0

## Overview

This document provides a comprehensive specification for all workflow node types in the RMS-Refresh platform. Each node type has specific configuration requirements, capabilities, and security considerations.

## Node Categories

### 1. Trigger Nodes

Trigger nodes initiate workflow execution based on various events or schedules.

#### Configuration Schema

```typescript
interface TriggerNodeConfig {
  triggerType: "event" | "schedule" | "webhook" | "manual";
  // Event-based triggers
  event?: {
    name: string; // e.g., 'candidate.created', 'job.updated'
    filters?: Record<string, any>; // Optional filters for the event
  };
  // Schedule-based triggers
  schedule?: {
    cron: string; // Cron expression
    timezone?: string; // Default: 'UTC'
  };
  // Webhook triggers
  webhook?: {
    id: string; // Webhook configuration ID
    validateSignature: boolean;
  };
}
```

#### Available Events

- **Candidate Events**
  - `candidate.created` - New candidate added
  - `candidate.updated` - Candidate information updated
  - `candidate.status_changed` - Candidate status changed
  - `candidate.document_uploaded` - New document uploaded

- **Job Events**
  - `job.created` - New job posted
  - `job.updated` - Job details updated
  - `job.application_received` - New application received
  - `job.closed` - Job posting closed

- **Interview Events**
  - `interview.scheduled` - Interview scheduled
  - `interview.completed` - Interview marked complete
  - `interview.feedback_submitted` - Feedback submitted

#### Side Effects

- Registers event listeners
- Creates webhook endpoints
- Schedules cron jobs

#### Security Scopes

- `workflow:trigger:read`
- `event:subscribe`

### 2. Action Nodes

Action nodes perform operations that modify data or interact with external systems.

#### Configuration Schema

```typescript
interface ActionNodeConfig {
  actionType: string;
  parameters: Record<string, any>;
  errorHandling?: {
    continueOnError: boolean;
    retryCount?: number;
    retryDelay?: number; // milliseconds
  };
}
```

#### Available Actions

##### Communication Actions

**send_email**

```typescript
{
  actionType: 'send_email',
  parameters: {
    to: string | string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    template?: string;
    body?: string;
    attachments?: Array<{
      filename: string;
      content: string; // base64
      contentType: string;
    }>;
    variables?: Record<string, any>;
  }
}
```

**send_sms**

```typescript
{
  actionType: 'send_sms',
  parameters: {
    to: string;
    message: string;
    provider?: 'twilio' | 'sns';
  }
}
```

##### Data Management Actions

**update_candidate**

```typescript
{
  actionType: 'update_candidate',
  parameters: {
    candidateId: string;
    updates: {
      status?: string;
      tags?: string[];
      customFields?: Record<string, any>;
    };
  }
}
```

**add_to_pool**

```typescript
{
  actionType: 'add_to_pool',
  parameters: {
    candidateId: string;
    poolId: string;
    notes?: string;
  }
}
```

##### Integration Actions

**api_call**

```typescript
{
  actionType: 'api_call',
  parameters: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    body?: any;
    authentication?: {
      type: 'bearer' | 'basic' | 'apikey';
      credentials: string;
    };
  }
}
```

#### Side Effects

- Database modifications
- External API calls
- Email/SMS sending
- File operations

#### Security Scopes

- `workflow:action:execute`
- `data:write`
- `integration:external`

### 3. Condition Nodes

Condition nodes create branching logic based on data evaluation.

#### Configuration Schema

```typescript
interface ConditionNodeConfig {
  conditions: Array<{
    field: string;
    operator: ComparisonOperator;
    value: any;
    logicalOperator?: "AND" | "OR";
  }>;
  defaultBranch?: "true" | "false";
}

type ComparisonOperator =
  | "equals"
  | "not_equals"
  | "contains"
  | "not_contains"
  | "starts_with"
  | "ends_with"
  | "greater_than"
  | "less_than"
  | "greater_than_or_equal"
  | "less_than_or_equal"
  | "in"
  | "not_in"
  | "is_empty"
  | "is_not_empty"
  | "matches_regex";
```

#### Examples

**Simple Condition**

```json
{
  "conditions": [
    {
      "field": "candidate.experience_years",
      "operator": "greater_than",
      "value": 5
    }
  ]
}
```

**Complex Condition**

```json
{
  "conditions": [
    {
      "field": "candidate.skills",
      "operator": "contains",
      "value": "Python"
    },
    {
      "field": "candidate.location",
      "operator": "in",
      "value": ["New York", "San Francisco"],
      "logicalOperator": "AND"
    }
  ]
}
```

#### Side Effects

- None (pure evaluation)

#### Security Scopes

- `workflow:condition:evaluate`
- `data:read`

### 4. Transformation Nodes

Transformation nodes modify data structure and content.

#### Configuration Schema

```typescript
interface TransformationNodeConfig {
  transformationType: "map" | "filter" | "reduce" | "custom";
  // Field mapping
  map?: {
    mappings: Record<string, string | MappingFunction>;
    preserveUnmapped?: boolean;
  };
  // Array filtering
  filter?: {
    conditions: ConditionNodeConfig["conditions"];
  };
  // Data aggregation
  reduce?: {
    operation: "sum" | "average" | "count" | "min" | "max";
    field: string;
    groupBy?: string;
  };
  // Custom JavaScript transformation
  custom?: {
    code: string; // JavaScript code
    timeout?: number; // milliseconds
  };
}

interface MappingFunction {
  type: "concatenate" | "split" | "format" | "calculate";
  parameters: any;
}
```

#### Examples

**Field Mapping**

```json
{
  "transformationType": "map",
  "map": {
    "mappings": {
      "fullName": {
        "type": "concatenate",
        "parameters": {
          "fields": ["candidate.firstName", "candidate.lastName"],
          "separator": " "
        }
      },
      "email": "candidate.email",
      "appliedDate": {
        "type": "format",
        "parameters": {
          "field": "candidate.createdAt",
          "format": "YYYY-MM-DD"
        }
      }
    }
  }
}
```

#### Side Effects

- In-memory data modification only

#### Security Scopes

- `workflow:transform:execute`
- `data:transform`

### 5. Output Nodes

Output nodes send processed data to external systems or storage.

#### Configuration Schema

```typescript
interface OutputNodeConfig {
  outputType: "webhook" | "database" | "file" | "report";
  // Webhook output
  webhook?: {
    url: string;
    method: "POST" | "PUT";
    headers?: Record<string, string>;
    format?: "json" | "xml" | "form-data";
  };
  // Database output
  database?: {
    table: string;
    operation: "insert" | "update" | "upsert";
    uniqueKey?: string[];
  };
  // File output
  file?: {
    format: "csv" | "json" | "excel";
    filename: string;
    path?: string;
  };
  // Report generation
  report?: {
    template: string;
    format: "pdf" | "html" | "docx";
    recipients?: string[];
  };
}
```

#### Side Effects

- External API calls
- File system writes
- Database operations
- Report generation

#### Security Scopes

- `workflow:output:write`
- `integration:external`
- `storage:write`

## Variable System

### Variable Syntax

Variables use double curly braces: `{{variable_name}}`

### Available Context Variables

```typescript
interface WorkflowContext {
  // Trigger context
  trigger: {
    type: string;
    timestamp: string;
    data: any;
  };

  // Current execution
  execution: {
    id: string;
    workflowId: string;
    startTime: string;
  };

  // User context
  user: {
    id: string;
    email: string;
    organization: string;
  };

  // Data from previous nodes
  nodes: {
    [nodeId: string]: any;
  };

  // Global data
  candidate?: CandidateData;
  job?: JobData;
  company: CompanyData;
}
```

### Variable Functions

- `{{date.now}}` - Current timestamp
- `{{date.format(candidate.createdAt, 'YYYY-MM-DD')}}` - Date formatting
- `{{string.uppercase(candidate.name)}}` - String manipulation
- `{{array.join(candidate.skills, ', ')}}` - Array operations

## Error Handling

### Node-Level Error Handling

```typescript
interface ErrorHandlingConfig {
  continueOnError: boolean;
  errorBranch?: string; // Node ID to execute on error
  retryPolicy?: {
    maxRetries: number;
    backoffType: "fixed" | "exponential";
    initialDelay: number;
    maxDelay?: number;
  };
  notification?: {
    channels: ("email" | "slack" | "webhook")[];
    recipients: string[];
  };
}
```

### Global Error Handling

Workflows can define global error handlers that catch any unhandled errors.

## Performance Considerations

### Node Limits

- Maximum nodes per workflow: 50
- Maximum execution time per node: 30 seconds
- Maximum parallel branches: 10
- Maximum workflow execution time: 5 minutes

### Best Practices

1. **Use conditions early** - Filter data as soon as possible
2. **Batch operations** - Group similar actions together
3. **Limit external calls** - Cache results when possible
4. **Handle errors gracefully** - Always plan for failure scenarios
5. **Test with real data** - Ensure performance at scale

## Security Model

### Scope Hierarchy

```
workflow:admin
├── workflow:create
├── workflow:read
├── workflow:update
├── workflow:delete
├── workflow:execute
│   ├── workflow:trigger:*
│   ├── workflow:action:*
│   ├── workflow:condition:*
│   ├── workflow:transform:*
│   └── workflow:output:*
└── workflow:monitor
```

### Data Access Controls

- Workflows inherit permissions from their creator
- Service account workflows have elevated permissions
- Row-level security applies to all database operations
- External integrations require explicit authorization

---
