# Creating Your First Workflow

This tutorial will guide you through creating your first automated workflow in RMS-Refresh. We'll build a simple "New Candidate Welcome" workflow that automatically sends a welcome email when a new candidate is added to the system.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Step 1: Access the Workflow Builder](#step-1-access-the-workflow-builder)
- [Step 2: Create a New Workflow](#step-2-create-a-new-workflow)
- [Step 3: Add a Trigger Node](#step-3-add-a-trigger-node)
- [Step 4: Add an Action Node](#step-4-add-an-action-node)
- [Step 5: Connect the Nodes](#step-5-connect-the-nodes)
- [Step 6: Configure the Email Action](#step-6-configure-the-email-action)
- [Step 7: Test Your Workflow](#step-7-test-your-workflow)
- [Step 8: Activate and Monitor](#step-8-activate-and-monitor)
- [Advanced Features](#advanced-features)

## Prerequisites

Before starting this tutorial, ensure you have:

- Access to the RMS-Refresh platform
- Admin or workflow management permissions
- At least one email template created (or use the default templates)

## Step 1: Access the Workflow Builder

1. Log in to your RMS-Refresh account
2. Navigate to the **AI & Workflows** section in the main menu
3. Click on **Workflow Builder**

![Workflow Builder Navigation](../assets/workflow-builder-nav.png)
_Screenshot: Accessing the Workflow Builder from the main navigation_

## Step 2: Create a New Workflow

1. Click the **"Create New Workflow"** button
2. Fill in the workflow details:
   - **Name**: "New Candidate Welcome"
   - **Description**: "Automatically send welcome email to new candidates"
   - **Category**: Select "Candidate Management"

![Create New Workflow](../assets/create-new-workflow.png)
_Screenshot: New workflow creation dialog_

3. Click **"Create"** to open the workflow canvas

## Step 3: Add a Trigger Node

The trigger node determines when your workflow will run.

1. From the node palette on the left, drag a **Trigger** node onto the canvas
2. Click on the node to open its configuration panel
3. Configure the trigger:
   - **Label**: "New Candidate Added"
   - **Trigger Type**: Select "Event"
   - **Event**: Choose "candidate.created"

![Add Trigger Node](../assets/add-trigger-node.png)
_Screenshot: Configuring the trigger node_

### Understanding Trigger Types

- **Event**: Triggered by system events (e.g., new candidate, status change)
- **Schedule**: Runs on a schedule (e.g., daily at 9 AM)
- **Webhook**: Triggered by external systems
- **Manual**: Started manually by users

## Step 4: Add an Action Node

Action nodes perform the actual work in your workflow.

1. Drag an **Action** node from the palette onto the canvas
2. Position it to the right of your trigger node
3. Click to configure:
   - **Label**: "Send Welcome Email"
   - **Action Type**: Select "Send Email"

![Add Action Node](../assets/add-action-node.png)
_Screenshot: Adding and positioning an action node_

## Step 5: Connect the Nodes

Connect the trigger to the action to create the workflow flow.

1. Hover over the trigger node until you see the connection handle (small circle on the right)
2. Click and drag from the handle to the action node
3. Release when the action node is highlighted

![Connect Nodes](../assets/connect-nodes.png)
_Screenshot: Connecting workflow nodes_

## Step 6: Configure the Email Action

Now let's configure what email to send and to whom.

1. Click on the "Send Welcome Email" action node
2. In the configuration panel, set:

### Email Configuration

```json
{
  "to": "{{candidate.email}}",
  "template": "candidate_welcome",
  "subject": "Welcome to {{company.name}}!",
  "variables": {
    "candidate_name": "{{candidate.first_name}}",
    "position": "{{candidate.applied_position}}",
    "next_steps": "We'll review your application and get back to you within 48 hours."
  }
}
```

### Available Variables

You can use these variables in your email templates:

- `{{candidate.first_name}}` - Candidate's first name
- `{{candidate.last_name}}` - Candidate's last name
- `{{candidate.email}}` - Candidate's email address
- `{{candidate.applied_position}}` - Position they applied for
- `{{company.name}}` - Your company name

![Configure Email Action](../assets/configure-email-action.png)
_Screenshot: Email action configuration panel_

## Step 7: Test Your Workflow

Before activating, let's test the workflow to ensure it works correctly.

1. Click the **"Test Workflow"** button in the top toolbar
2. Select or create a test candidate
3. Click **"Run Test"**

![Test Workflow](../assets/test-workflow.png)
_Screenshot: Testing workflow with sample data_

### Review Test Results

After running the test, you'll see:

- Execution logs for each node
- Any errors or warnings
- Preview of the email that would be sent

![Test Results](../assets/test-results.png)
_Screenshot: Workflow test results_

## Step 8: Activate and Monitor

Once testing is successful, activate your workflow.

1. Click the **"Save"** button to save your workflow
2. Toggle the **"Active"** switch to enable the workflow
3. Click **"Deploy"**

![Activate Workflow](../assets/activate-workflow.png)
_Screenshot: Activating the workflow_

### Monitoring Your Workflow

After activation, monitor your workflow's performance:

1. Go to **Workflow History** to see all executions
2. Click on any execution to view detailed logs
3. Set up alerts for failures (optional)

![Workflow Monitoring](../assets/workflow-monitoring.png)
_Screenshot: Workflow execution history_

## Advanced Features

### Adding Conditions

Make your workflow smarter by adding conditional logic.

Example: Only send welcome email if candidate is for a specific department:

1. Add a **Condition** node between trigger and action
2. Configure condition:
   ```json
   {
     "field": "candidate.department",
     "operator": "equals",
     "value": "Engineering"
   }
   ```

![Conditional Logic](../assets/conditional-logic.png)
_Screenshot: Adding conditional logic to workflow_

### Multiple Actions

Chain multiple actions together:

1. **Send Welcome Email** → **Add to Talent Pool** → **Notify Hiring Manager**

![Multiple Actions](../assets/multiple-actions.png)
_Screenshot: Workflow with multiple actions_

### Error Handling

Add error handling to your workflow:

1. Click on any action node
2. Enable **"Continue on Error"** to prevent workflow from stopping
3. Add an error notification action

## Best Practices

### 1. Start Simple

Begin with basic workflows and gradually add complexity.

### 2. Use Descriptive Names

Give your nodes and workflows clear, descriptive names.

### 3. Test Thoroughly

Always test with different scenarios before activating.

### 4. Monitor Performance

Regularly check workflow execution logs and success rates.

### 5. Document Your Workflows

Add descriptions to complex workflows for team understanding.

## Common Workflow Templates

Here are some popular workflow templates to get you started:

### 1. Interview Reminder

- **Trigger**: 24 hours before scheduled interview
- **Action**: Send reminder email to candidate and interviewer

### 2. Application Status Update

- **Trigger**: Candidate status change
- **Action**: Send status update email
- **Condition**: Only if status is "Shortlisted" or "Rejected"

### 3. Document Collection

- **Trigger**: Candidate moves to "Document Review" stage
- **Action**: Send document request email
- **Follow-up**: Send reminder after 3 days if not completed

## Troubleshooting

### Workflow Not Triggering

- Verify the workflow is active
- Check trigger configuration matches your events
- Review workflow logs for errors

### Email Not Sending

- Confirm email template exists
- Verify candidate has valid email address
- Check email service configuration

### Performance Issues

- Limit the number of nodes in a single workflow
- Use conditions to prevent unnecessary actions
- Consider splitting complex workflows

## Next Steps

Congratulations! You've created your first workflow. Here's what to explore next:

1. **[Advanced Workflow Patterns](./advanced-workflows.md)** - Learn about parallel execution, loops, and more
2. **[Integration Guide](./integrations.md)** - Connect external services to your workflows
3. **[Workflow Best Practices](./best-practices.md)** - Optimize your workflow design

## Need Help?

- Check our [FAQ section](../faq.md)
- Join our [Community Forum](https://community.rms-refresh.com)
- Contact [Support](mailto:<EMAIL>)

---

_Note: Screenshots are placeholders. In a production environment, these would be actual screenshots from the application._
