# Skills Schema Inventory

## Project: ueanagtsdavrgolecirg

Date: Generated on request

## 1. Tables Overview

### Skills-related tables found:

- `skills` - Master skills table
- `candidate_skills` - Junction table linking candidates to skills
- `analytics_skills` - Analytics table for skills data

## 2. Table Schemas

### 2.1 `skills` Table

| Column Name | Data Type                |
| ----------- | ------------------------ |
| id          | uuid                     |
| name        | text                     |
| category    | text                     |
| created_at  | timestamp with time zone |
| updated_at  | timestamp with time zone |

### 2.2 `candidate_skills` Table (Junction Table)

| Column Name       | Data Type                |
| ----------------- | ------------------------ |
| id                | uuid                     |
| candidate_id      | uuid                     |
| skill_id          | uuid                     |
| proficiency_level | text                     |
| years_experience  | numeric                  |
| created_at        | timestamp with time zone |

## 3. Constraints

### 3.1 `skills` Table Constraints

- **Primary Key**: `skills_pkey` on column `id`
- **Unique Constraint**: `skills_name_key` on column `name` (ensures unique skill names)

### 3.2 `candidate_skills` Table Constraints

- **Primary Key**: `candidate_skills_pkey` on column `id`
- **Unique Constraint**: `candidate_skills_candidate_id_skill_id_key` on columns `(candidate_id, skill_id)`
  - Prevents duplicate skill assignments for the same candidate
- **Check Constraint**: `candidate_skills_proficiency_level_check`
  - Ensures proficiency_level is one of: 'beginner', 'intermediate', 'advanced', 'expert'

## 4. Foreign Keys

### 4.1 `candidate_skills` Table Foreign Keys

1. **candidate_skills_candidate_id_fkey**
   - From: `candidate_id`
   - References: `candidates(id)`
   - On Delete: CASCADE

2. **candidate_skills_skill_id_fkey**
   - From: `skill_id`
   - References: `skills(id)`
   - On Delete: CASCADE

## 5. Indexes

### 5.1 `skills` Table Indexes

- `skills_pkey` - Primary key index on `id`
- `skills_name_key` - Unique index on `name`
- `idx_skills_name` - Additional index on `name` for performance

### 5.2 `candidate_skills` Table Indexes

- `candidate_skills_pkey` - Primary key index on `id`
- `candidate_skills_candidate_id_skill_id_key` - Unique index on `(candidate_id, skill_id)`
- `idx_candidate_skills_candidate` - Index on `candidate_id` for foreign key performance
- `idx_candidate_skills_skill` - Index on `skill_id` for foreign key performance

## Summary

The existing schema already has:

1. ✅ A normalized `skills` master table with unique skill names
2. ✅ A junction table `candidate_skills` that links candidates to skills
3. ✅ Proper foreign key constraints with CASCADE deletes
4. ✅ Unique constraint preventing duplicate skill assignments
5. ✅ Proficiency level tracking with validation
6. ✅ Years of experience tracking
7. ✅ Proper indexes for performance

**No additional migrations are needed** for basic skills functionality. The schema is already well-designed with:

- Proper normalization
- Referential integrity
- Performance optimizations
- Data validation constraints
