# Search and Vectorization Developer Guide

## Overview

The RMS platform implements a robust search system using PostgreSQL's full-text search capabilities. This guide provides detailed information for developers working with the search functionality.

## Architecture

### Database Schema

Each searchable table contains:

- **search_vector** column (`tsvector` type): Stores the preprocessed search data
- **GIN index**: Provides fast full-text search capabilities
- **Automatic triggers**: Keep search vectors synchronized with data changes

### Tables with Search Support

1. **candidates**: Search by name, role, email, location, skills, and more
2. **jobs**: Search by title, department, location, description, requirements
3. **messages**: Search by sender details and content
4. **events**: Search by title and description
5. **message_templates**: Search by name, subject, and content

## Search Implementation Details

### Weight System

The search system uses PostgreSQL's weight system (A-D) to prioritize results:

```sql
-- Example from candidates table
setweight(to_tsvector('english', COALESCE(name, '')), 'A') ||      -- Highest priority
setweight(to_tsvector('english', COALESCE(role, '')), 'A') ||
setweight(to_tsvector('english', COALESCE(email, '')), 'B') ||     -- High priority
setweight(to_tsvector('english', COALESCE(location, '')), 'B') ||
setweight(to_tsvector('english', COALESCE(experience, '')), 'C') || -- Medium priority
setweight(to_tsvector('english', COALESCE(industry, '')), 'C') ||
setweight(to_tsvector('english', COALESCE(ai_summary, '')), 'D') || -- Low priority
setweight(to_tsvector('english', COALESCE(array_to_string(tags, ' '), '')), 'B')
```

### Trigger Functions

Each searchable table has an associated trigger function that automatically updates the search vector:

```sql
CREATE TRIGGER candidates_search_vector_update
  BEFORE INSERT OR UPDATE ON public.candidates
  FOR EACH ROW EXECUTE FUNCTION update_candidates_search_vector();
```

## Frontend Integration

### Search Utilities

The search functionality is implemented in utility functions:

#### searchCandidates

```typescript
import { searchCandidates } from "@/utils/searchCandidates";

// Basic search
const results = await searchCandidates("John Doe");

// Search with filters
const results = await searchCandidates("Developer", {
  remoteOnly: true,
  skills: [
    { name: "React", required: true, proficiency: "expert" },
    { name: "TypeScript", required: false }
  ],
  location: {
    address: "San Francisco, CA",
    radius: 50
  },
  visaSponsor: false
});
```

#### searchJobs

```typescript
import { searchJobs } from "@/utils/searchJobs";

// Search jobs with filters
const jobs = await searchJobs("Frontend Developer", {
  remoteOnly: true,
  location: {
    address: "New York, NY",
    radius: 100
  }
});
```

#### searchMessages

```typescript
import { searchMessages } from "@/utils/searchMessages";

// Search messages with filters
const messages = await searchMessages("interview", {
  status: "unread",
  isStarred: true,
  dateRange: {
    start: new Date("2024-01-01"),
    end: new Date()
  }
});
```

## Query Processing

### Search Query Preparation

All search queries are processed before being sent to the database:

```typescript
// Clean and prepare query for full-text search
const cleanQuery = query
  .trim()
  .replace(/[^\w\s]/g, "")  // Remove special characters
  .split(/\s+/)             // Split by whitespace
  .join(" & ");             // Join with AND operator
```

### Full-text Search Filter

```typescript
// Use PostgreSQL's full-text search
supabaseQuery = supabaseQuery.filter("search_vector", "fts", cleanQuery);
```

### Client-side Relevance Scoring

After database results, we apply additional relevance scoring:

```typescript
import { calculateRelevanceScore } from "./searchUtils";

results.sort((a, b) => {
  const scoreA = calculateRelevanceScore(
    `${a.name} ${a.role} ${a.location}`,
    query
  );
  const scoreB = calculateRelevanceScore(
    `${b.name} ${b.role} ${b.location}`,
    query
  );
  return scoreB - scoreA;
});
```

## Performance Optimization

### Index Maintenance

The GIN indexes are automatically maintained by PostgreSQL. No manual intervention required.

### Query Performance

- **Sub-100ms response times** for most queries
- **Efficient for datasets up to 1M records**
- **Real-time updates** through database triggers

### Best Practices

1. **Use specific queries**: More specific queries perform better
2. **Apply filters early**: Database-level filtering is faster than client-side
3. **Limit results**: Use pagination for large result sets
4. **Monitor performance**: Use `EXPLAIN ANALYZE` for slow queries

## Testing

### Unit Tests

```typescript
// Example test for search functionality
describe("searchCandidates", () => {
  it("should find candidates by name", async () => {
    const results = await searchCandidates("John");
    expect(results).toHaveLength(greaterThan(0));
    expect(results[0].name).toContain("John");
  });
});
```

### Integration Tests

Run the comprehensive integration test suite:

```bash
npm run test src/__tests__/integration/search-integration.test.ts
```

### Manual Testing

Use the validation script for manual testing:

```bash
TEST_EMAIL=<EMAIL> TEST_PASSWORD=password npx ts-node scripts/validate-search.ts
```

## Troubleshooting

### Common Issues

1. **Missing search vectors**
   ```sql
   -- Check for missing vectors
   SELECT COUNT(*) FROM candidates WHERE search_vector IS NULL;
   
   -- Regenerate vectors
   UPDATE candidates SET updated_at = NOW() WHERE search_vector IS NULL;
   ```

2. **Slow queries**
   ```sql
   -- Check index usage
   EXPLAIN ANALYZE SELECT * FROM candidates WHERE search_vector @@ to_tsquery('developer');
   ```

3. **Incorrect results**
   - Verify search weights are appropriate
   - Check if all relevant fields are included in search vector
   - Ensure triggers are active

### Performance Monitoring

```sql
-- Check search performance
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
WHERE indexname LIKE '%search_vector%';
```

## Future Enhancements

### Planned Improvements

1. **Vector Embeddings**: Migration to pgvector for semantic search
2. **Multi-language Support**: Support for non-English content
3. **Fuzzy Matching**: Improved handling of typos and variations
4. **Search Analytics**: Track popular searches and improve relevance

### Migration Path to Vector Embeddings

When migrating to vector embeddings:

1. Install pgvector extension
2. Add embedding columns to tables
3. Generate embeddings for existing data
4. Create vector similarity indexes
5. Update search functions to use similarity search

Example migration:

```sql
-- Enable pgvector
CREATE EXTENSION IF NOT EXISTS vector;

-- Add embedding column
ALTER TABLE candidates ADD COLUMN embedding vector(384);

-- Create index for similarity search
CREATE INDEX candidates_embedding_idx ON candidates 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Search using similarity
SELECT * FROM candidates
ORDER BY embedding <=> '[0.1, 0.2, ...]'::vector
LIMIT 10;
```

## API Reference

### Search Endpoints

All search operations go through Supabase client:

```typescript
// Direct database search
const { data, error } = await supabase
  .from("candidates")
  .select("*")
  .filter("search_vector", "fts", "developer");

// Using utility functions (recommended)
const results = await searchCandidates("developer");
```

### Search Operators

- `&` - AND operator
- `|` - OR operator
- `!` - NOT operator
- `:*` - Prefix matching

Example:
```typescript
// Search for "React" AND "TypeScript"
const query = "React & TypeScript";

// Search for "React" OR "Vue"
const query = "React | Vue";

// Search for "Developer" but NOT "Junior"
const query = "Developer & !Junior";
```

## Support

For questions or issues:

1. Check this documentation
2. Review the test files for examples
3. Check the migration files for schema details
4. Contact the development team
